{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "تعديل المنتج" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .image-preview {
        width: 200px;
        height: 200px;
        border: 1px dashed #ccc;
        border-radius: 5px;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        margin-bottom: 10px;
    }

    .image-preview img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
    }

    .image-preview-placeholder {
        color: #6c757d;
        text-align: center;
    }

    .required-field::after {
        content: " *";
        color: red;
    }

    .form-section {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .form-section-title {
        border-bottom: 1px solid #dee2e6;
        padding-bottom: 10px;
        margin-bottom: 20px;
    }
    
    /* تنسيقات ماسح الباركود */
    #scanner-container {
        position: relative;
        border: 1px solid #ddd;
        border-radius: 5px;
        overflow: hidden;
        max-height: 300px;
    }
    
    #scanner {
        display: block;
        width: 100%;
    }
    
    .btn-outline-secondary .fa-barcode {
        color: #6c757d;
    }
    
    .btn-outline-secondary:hover .fa-barcode {
        color: #fff;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0 text-gray-800">{% trans "تعديل المنتج" %}: {{ product.name }}</h1>
    <div>
        <a href="{% url 'inventory:index' %}" class="btn btn-secondary me-2">
            <i class="fas fa-arrow-right me-1"></i> {% trans "العودة إلى المخزون" %}
        </a>
        <a href="{% url 'inventory:delete_product' product.id %}" class="btn btn-danger">
            <i class="fas fa-trash me-1"></i> {% trans "حذف المنتج" %}
        </a>
    </div>
</div>

{% if messages %}
    {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    {% endfor %}
{% endif %}

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">{% trans "معلومات المنتج" %}</h6>
    </div>
    <div class="card-body">
        <form method="post" enctype="multipart/form-data" id="productForm">
            {% csrf_token %}

            <!-- Basic Information Section -->
            <div class="form-section">
                <h5 class="form-section-title">{% trans "المعلومات الأساسية" %}</h5>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="code" class="form-label required-field">{% trans "كود المنتج" %}</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-hashtag"></i></span>
                            <input type="text" class="form-control" id="code" name="code" value="{{ product.code }}" required>
                        </div>
                        <div class="form-text">{% trans "يجب أن يكون الكود فريدًا" %}</div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="barcode" class="form-label">
                            <i class="fas fa-barcode me-2"></i>{% trans "باركود المنتج" %}
                        </label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-barcode"></i></span>
                            <input type="text" class="form-control" id="barcode" name="barcode"
                                   value="{{ primary_barcode.barcode_number|default_if_none:'' }}"
                                   placeholder="{% trans 'أدخل رقم الباركود أو امسحه...' %}">
                            <button class="btn btn-outline-secondary" type="button" id="scanBarcodeBtn"
                                    title="{% trans 'مسح الباركود' %}" data-bs-toggle="modal" data-bs-target="#scannerModal">
                                <i class="fas fa-camera"></i>
                            </button>
                        </div>
                        <div class="form-text">
                            {% if primary_barcode %}
                                <span class="text-success">
                                    <i class="fas fa-check-circle me-1"></i>
                                    {% trans "الباركود الحالي:" %} {{ primary_barcode.barcode_number }}
                                </span>
                            {% else %}
                                <span class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    {% trans "لا يوجد باركود مسجل لهذا المنتج" %}
                                </span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="name" class="form-label required-field">{% trans "اسم المنتج" %}</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-tag"></i></span>
                            <input type="text" class="form-control" id="name" name="name" value="{{ product.name }}" required>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="category" class="form-label required-field">{% trans "الفئة" %}</label>
                        <div class="select-wrapper position-relative">
                            <select class="form-select" id="category" name="category" required>
                                <option value="">{% trans "اختر الفئة" %}</option>
                                {% for category in categories %}
                                <option value="{{ category.id }}" {% if category.id == product.category.id %}selected{% endif %}>{{ category.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="mt-2">
                            <a href="{% url 'inventory:categories' %}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-plus me-1"></i> {% trans "إدارة الفئات" %}
                            </a>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="supplier" class="form-label">{% trans "المورد" %}</label>
                        <div class="select-wrapper position-relative">
                            <select class="form-select" id="supplier" name="supplier">
                                <option value="">{% trans "اختر المورد" %}</option>
                                {% for supplier in suppliers %}
                                <option value="{{ supplier.id }}" {% if supplier.id == product.supplier.id %}selected{% endif %}>{{ supplier.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="mt-2">
                            <a href="{% url 'purchases:suppliers' %}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-plus me-1"></i> {% trans "إدارة الموردين" %}
                            </a>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="storage_location" class="form-label">{% trans "مكان التخزين" %}</label>
                        <div class="select-wrapper position-relative">
                            <select class="form-select" id="storage_location" name="storage_location">
                                <option value="">{% trans "اختر مكان التخزين" %}</option>
                                {% for location in storage_locations %}
                                <option value="{{ location.id }}" {% if location.id == product.storage_location.id %}selected{% endif %}>{{ location.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="mt-2">
                            <a href="{% url 'inventory:storage_locations' %}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-plus me-1"></i> {% trans "إدارة مواقع التخزين" %}
                            </a>
                        </div>
                    </div>
                </div>



                <div class="mb-3">
                    <label for="description" class="form-label">{% trans "وصف المنتج" %}</label>
                    <textarea class="form-control" id="description" name="description" rows="3">{{ product.description }}</textarea>
                </div>
            </div>

            <!-- Pricing and Stock Section -->
            <div class="form-section">
                <h5 class="form-section-title">{% trans "التسعير والمخزون" %}</h5>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="purchase_price" class="form-label required-field">{% trans "سعر الشراء" %}</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="purchase_price" name="purchase_price" step="0.01" min="0" value="{{ product.purchase_price }}" required>
                            <span class="input-group-text">د.م</span>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="selling_price" class="form-label required-field">{% trans "سعر البيع" %}</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="selling_price" name="selling_price" step="0.01" min="0" value="{{ product.selling_price }}" required>
                            <span class="input-group-text">د.م</span>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="quantity" class="form-label">{% trans "الكمية الحالية" %}</label>
                        <input type="number" class="form-control" id="quantity" value="{{ product.quantity }}" readonly>
                        <div class="form-text">{% trans "الكمية تتغير تلقائياً عند إضافة مشتريات أو إجراء مبيعات" %}</div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="unit" class="form-label required-field">
                            <i class="fas fa-balance-scale me-2"></i>{% trans "الوحدة" %}
                        </label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-ruler"></i></span>
                            <select class="form-select" id="unit" name="unit" required>
                                <option value="">{% trans "اختر الوحدة..." %}</option>
                                <option value="piece" {% if product.unit == 'piece' %}selected{% endif %}>📦 {% trans "قطعة" %}</option>
                                <option value="kg" {% if product.unit == 'kg' %}selected{% endif %}>⚖️ {% trans "كيلوغرام" %}</option>
                                <option value="gram" {% if product.unit == 'gram' %}selected{% endif %}>⚖️ {% trans "غرام" %}</option>
                                <option value="liter" {% if product.unit == 'liter' %}selected{% endif %}>🥤 {% trans "لتر" %}</option>
                                <option value="ml" {% if product.unit == 'ml' %}selected{% endif %}>🥤 {% trans "مليلتر" %}</option>
                                <option value="meter" {% if product.unit == 'meter' %}selected{% endif %}>📏 {% trans "متر" %}</option>
                                <option value="cm" {% if product.unit == 'cm' %}selected{% endif %}>📏 {% trans "سنتيمتر" %}</option>
                                <option value="mm" {% if product.unit == 'mm' %}selected{% endif %}>📏 {% trans "مليمتر" %}</option>
                                <option value="m2" {% if product.unit == 'm2' %}selected{% endif %}>📐 {% trans "متر مربع" %}</option>
                                <option value="cm2" {% if product.unit == 'cm2' %}selected{% endif %}>📐 {% trans "سنتيمتر مربع" %}</option>
                                <option value="m3" {% if product.unit == 'm3' %}selected{% endif %}>📦 {% trans "متر مكعب" %}</option>
                                <option value="cm3" {% if product.unit == 'cm3' %}selected{% endif %}>📦 {% trans "سنتيمتر مكعب" %}</option>
                                <option value="box" {% if product.unit == 'box' %}selected{% endif %}>📦 {% trans "صندوق" %}</option>
                                <option value="pack" {% if product.unit == 'pack' %}selected{% endif %}>📦 {% trans "علبة" %}</option>
                                <option value="bottle" {% if product.unit == 'bottle' %}selected{% endif %}>🍼 {% trans "زجاجة" %}</option>
                                <option value="bag" {% if product.unit == 'bag' %}selected{% endif %}>👜 {% trans "كيس" %}</option>
                                <option value="roll" {% if product.unit == 'roll' %}selected{% endif %}>🧻 {% trans "لفة" %}</option>
                                <option value="sheet" {% if product.unit == 'sheet' %}selected{% endif %}>📄 {% trans "ورقة" %}</option>
                                <option value="pair" {% if product.unit == 'pair' %}selected{% endif %}>👟 {% trans "زوج" %}</option>
                                <option value="set" {% if product.unit == 'set' %}selected{% endif %}>🎯 {% trans "طقم" %}</option>
                                <option value="dozen" {% if product.unit == 'dozen' %}selected{% endif %}>🥚 {% trans "دزينة" %}</option>
                                <option value="carton" {% if product.unit == 'carton' %}selected{% endif %}>📦 {% trans "كرتونة" %}</option>
                                <option value="pallet" {% if product.unit == 'pallet' %}selected{% endif %}>🚛 {% trans "منصة نقالة" %}</option>
                                <option value="ton" {% if product.unit == 'ton' %}selected{% endif %}>⚖️ {% trans "طن" %}</option>
                                <option value="inch" {% if product.unit == 'inch' %}selected{% endif %}>📏 {% trans "بوصة" %}</option>
                                <option value="foot" {% if product.unit == 'foot' %}selected{% endif %}>📏 {% trans "قدم" %}</option>
                                <option value="yard" {% if product.unit == 'yard' %}selected{% endif %}>📏 {% trans "ياردة" %}</option>
                                <option value="gallon" {% if product.unit == 'gallon' %}selected{% endif %}>🥤 {% trans "غالون" %}</option>
                                <option value="other" {% if product.unit == 'other' %}selected{% endif %}>❓ {% trans "أخرى" %}</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="min_quantity" class="form-label required-field">{% trans "الحد الأدنى للكمية" %}</label>
                        <input type="number" class="form-control" id="min_quantity" name="min_quantity" min="1" value="{{ product.min_quantity }}" required>
                        <div class="form-text">{% trans "سيتم تنبيهك عندما تصل الكمية إلى هذا الحد أو أقل" %}</div>
                    </div>
                </div>

                <!-- حقل الوحدة المخصصة (يظهر عند اختيار "أخرى") -->
                <div class="row" id="customUnitRow" {% if product.unit != 'other' %}style="display: none;"{% endif %}>
                    <div class="col-md-6 mb-3">
                        <label for="custom_unit" class="form-label">
                            <i class="fas fa-edit me-2"></i>{% trans "الوحدة المخصصة" %}
                        </label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-pencil-alt"></i></span>
                            <input type="text" class="form-control" id="custom_unit" name="custom_unit" value="{{ product.custom_unit|default:'' }}" placeholder="{% trans 'أدخل الوحدة المخصصة...' %}">
                        </div>
                        <div class="form-text">{% trans "أدخل اسم الوحدة المخصصة (مثل: علبة صغيرة، حبة، إلخ)" %}</div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="profit_margin" class="form-label">{% trans "هامش الربح" %}</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="profit_margin" value="{{ product.profit_margin|floatformat:2 }}" readonly>
                            <span class="input-group-text">%</span>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="form-check mt-4">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" {% if product.is_active %}checked{% endif %}>
                            <label class="form-check-label" for="is_active">
                                {% trans "منتج نشط" %}
                            </label>
                            <div class="form-text">{% trans "إلغاء التحديد سيخفي المنتج من صفحة المبيعات" %}</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Image Section -->
            <div class="form-section">
                <h5 class="form-section-title">{% trans "صورة المنتج" %}</h5>
                <div class="row">
                    <div class="col-md-4">
                        <div class="image-preview" id="imagePreview">
                            {% if product.image %}
                            <img src="{{ product.image.url }}" alt="{{ product.name }}">
                            {% else %}
                            <div class="image-preview-placeholder">
                                <i class="fas fa-image fa-3x mb-2"></i>
                                <p>{% trans "لا توجد صورة" %}</p>
                            </div>
                            {% endif %}
                        </div>
                        <input type="file" class="form-control" id="image" name="image" accept="image/*">
                        <div class="form-text">{% trans "اترك هذا الحقل فارغًا إذا كنت لا ترغب في تغيير الصورة" %}</div>
                    </div>
                    <div class="col-md-8">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-1"></i>
                            {% trans "نصائح للصور:" %}
                            <ul class="mb-0 mt-2">
                                <li>{% trans "استخدم صورًا واضحة وعالية الجودة" %}</li>
                                <li>{% trans "الحجم المثالي: 800×800 بكسل" %}</li>
                                <li>{% trans "الصيغ المدعومة: JPG، PNG، GIF" %}</li>
                                <li>{% trans "الحد الأقصى لحجم الملف: 5 ميجابايت" %}</li>
                            </ul>
                        </div>
                        {% if product.image %}
                        <div class="form-check mt-3">
                            <input class="form-check-input" type="checkbox" id="delete_image" name="delete_image">
                            <label class="form-check-label" for="delete_image">
                                {% trans "حذف الصورة الحالية" %}
                            </label>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Product History Section -->
            <div class="form-section">
                <h5 class="form-section-title">{% trans "معلومات إضافية" %}</h5>
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>{% trans "تاريخ الإنشاء:" %}</strong> {{ product.created_at|date:"Y-m-d H:i" }}</p>
                        <p><strong>{% trans "آخر تحديث:" %}</strong> {{ product.updated_at|date:"Y-m-d H:i" }}</p>
                    </div>
                    <div class="col-md-6">
                        <a href="#" class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#movementsModal">
                            <i class="fas fa-history me-1"></i> {% trans "عرض سجل حركات المخزون" %}
                        </a>
                    </div>
                </div>
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <button type="button" class="btn btn-secondary me-md-2" onclick="window.location.href='{% url 'inventory:index' %}'">
                    {% trans "إلغاء" %}
                </button>
                <button type="submit" class="btn btn-primary" id="saveProductBtn">
                    <i class="fas fa-save me-1"></i> {% trans "حفظ التغييرات" %}
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Add Storage Location Modal -->
<div class="modal fade" id="addLocationModal" tabindex="-1" aria-labelledby="addLocationModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addLocationModalLabel">{% trans "إضافة مكان تخزين جديد" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="locationForm">
                    <div class="mb-3">
                        <label for="locationName" class="form-label required-field">{% trans "اسم مكان التخزين" %}</label>
                        <input type="text" class="form-control" id="locationName" required>
                    </div>
                    <div class="mb-3">
                        <label for="locationDescription" class="form-label">{% trans "وصف مكان التخزين" %}</label>
                        <textarea class="form-control" id="locationDescription" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                <button type="button" class="btn btn-primary" id="saveLocationBtn">{% trans "حفظ" %}</button>
            </div>
        </div>
    </div>
</div>

<!-- Barcode Scanner Modal -->
<div class="modal fade" id="scannerModal" tabindex="-1" aria-labelledby="scannerModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="scannerModalLabel">{% trans "مسح الباركود" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <div id="scanner-container" class="mb-3">
                    <video id="scanner" class="w-100"></video>
                </div>
                <p class="mb-0">{% trans "قم بتوجيه الكاميرا نحو الباركود للمسح التلقائي." %}</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إغلاق" %}</button>
            </div>
        </div>
    </div>
</div>



<!-- Product Movements Modal -->
<div class="modal fade" id="movementsModal" tabindex="-1" aria-labelledby="movementsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="movementsModalLabel">{% trans "سجل حركات المخزون" %}: {{ product.name }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead>
                            <tr>
                                <th>{% trans "التاريخ" %}</th>
                                <th>{% trans "نوع الحركة" %}</th>
                                <th>{% trans "الكمية" %}</th>
                                <th>{% trans "المرجع" %}</th>
                                <th>{% trans "ملاحظات" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for movement in product.movements.all %}
                            <tr>
                                <td>{{ movement.created_at|date:"Y-m-d H:i" }}</td>
                                <td>
                                    {% if movement.movement_type == 'in' %}
                                    <span class="badge bg-success">{% trans "وارد" %}</span>
                                    {% elif movement.movement_type == 'out' %}
                                    <span class="badge bg-danger">{% trans "صادر" %}</span>
                                    {% else %}
                                    <span class="badge bg-info">{% trans "تعديل" %}</span>
                                    {% endif %}
                                </td>
                                <td>{{ movement.quantity }}</td>
                                <td>{{ movement.reference|default:"-" }}</td>
                                <td>{{ movement.notes|default:"-" }}</td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="5" class="text-center">{% trans "لا توجد حركات مخزون لهذا المنتج" %}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إغلاق" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Quagga JS for barcode scanning -->
<script src="https://cdn.jsdelivr.net/npm/quagga@0.12.1/dist/quagga.min.js"></script>

<script>
    $(document).ready(function() {
        // إظهار/إخفاء حقل الوحدة المخصصة
        $('#unit').change(function() {
            const selectedUnit = $(this).val();
            if (selectedUnit === 'other') {
                $('#customUnitRow').show();
                $('#custom_unit').attr('required', true);
            } else {
                $('#customUnitRow').hide();
                $('#custom_unit').attr('required', false);
            }
        });

        // تتبع تغييرات الباركود
        var originalBarcode = $('#barcode').val();
        $('#barcode').on('input', function() {
            var currentBarcode = $(this).val();
            if (currentBarcode !== originalBarcode && currentBarcode.length > 0) {
                // إظهار رسالة تنبيه
                $('.barcode-change-warning').remove();
                var warningMessage = '<div class="alert alert-warning alert-dismissible fade show mt-2 barcode-change-warning" role="alert">' +
                    '<i class="fas fa-exclamation-triangle me-2"></i>سيتم تحديث الباركود عند حفظ المنتج' +
                    '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                    '</div>';
                $(this).parent().after(warningMessage);
            } else {
                $('.barcode-change-warning').remove();
            }
        });

        // تأكيد حفظ المنتج
        $('#saveProductBtn').click(function(e) {
            var currentBarcode = $('#barcode').val();
            if (currentBarcode !== originalBarcode) {
                if (currentBarcode.length > 0) {
                    var confirmMessage = 'سيتم تحديث الباركود إلى: ' + currentBarcode + '\nهل أنت متأكد؟';
                } else {
                    var confirmMessage = 'سيتم حذف الباركود الحالي من المنتج\nهل أنت متأكد؟';
                }

                if (!confirm(confirmMessage)) {
                    e.preventDefault();
                    return false;
                }
            }

            // إظهار رسالة تحميل
            $(this).html('<i class="fas fa-spinner fa-spin me-1"></i> جاري الحفظ...');
            $(this).prop('disabled', true);
        });

        // تم نقل وظيفة تهيئة القوائم المنسدلة إلى ملف custom-dropdowns.js
        // وسيتم تطبيقها تلقائيًا على جميع الحقول المنسدلة في الصفحة

        // Image Preview
        $('#image').change(function() {
            const file = this.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    $('#imagePreview').html('<img src="' + e.target.result + '" alt="Preview">');
                }
                reader.readAsDataURL(file);

                // Uncheck delete image if a new image is selected
                $('#delete_image').prop('checked', false);
            }
        });

        // Delete Image Checkbox
        $('#delete_image').change(function() {
            if ($(this).is(':checked')) {
                $('#imagePreview').html('<div class="image-preview-placeholder"><i class="fas fa-image fa-3x mb-2"></i><p>{% trans "لا توجد صورة" %}</p></div>');
                $('#image').val('');
            } else {
                {% if product.image %}
                $('#imagePreview').html('<img src="{{ product.image.url }}" alt="{{ product.name }}">');
                {% else %}
                $('#imagePreview').html('<div class="image-preview-placeholder"><i class="fas fa-image fa-3x mb-2"></i><p>{% trans "لا توجد صورة" %}</p></div>');
                {% endif %}
            }
        });

        // Calculate Profit Margin
        function calculateProfitMargin() {
            const purchasePrice = parseFloat($('#purchase_price').val()) || 0;
            const sellingPrice = parseFloat($('#selling_price').val()) || 0;

            if (purchasePrice > 0) {
                const profitMargin = ((sellingPrice - purchasePrice) / purchasePrice) * 100;
                $('#profit_margin').val(profitMargin.toFixed(2));

                // Change color based on margin
                if (profitMargin < 0) {
                    $('#profit_margin').css('color', 'red');
                } else if (profitMargin < 10) {
                    $('#profit_margin').css('color', 'orange');
                } else {
                    $('#profit_margin').css('color', 'green');
                }
            } else {
                $('#profit_margin').val('0.00');
                $('#profit_margin').css('color', 'black');
            }
        }

        $('#purchase_price, #selling_price').on('input', calculateProfitMargin);

        // Set initial profit margin color
        const initialProfitMargin = parseFloat($('#profit_margin').val()) || 0;
        if (initialProfitMargin < 0) {
            $('#profit_margin').css('color', 'red');
        } else if (initialProfitMargin < 10) {
            $('#profit_margin').css('color', 'orange');
        } else {
            $('#profit_margin').css('color', 'green');
        }

        // Form Validation
        $('#productForm').submit(function(e) {
            const purchasePrice = parseFloat($('#purchase_price').val()) || 0;
            const sellingPrice = parseFloat($('#selling_price').val()) || 0;

            if (purchasePrice <= 0) {
                e.preventDefault();
                alert('{% trans "يجب أن يكون سعر الشراء أكبر من صفر" %}');
                $('#purchase_price').focus();
                return false;
            }

            if (sellingPrice <= 0) {
                e.preventDefault();
                alert('{% trans "يجب أن يكون سعر البيع أكبر من صفر" %}');
                $('#selling_price').focus();
                return false;
            }

            return true;
        });

        // Add New Storage Location
        $('#saveLocationBtn').click(function() {
            const locationName = $('#locationName').val();
            const locationDescription = $('#locationDescription').val();

            if (!locationName) {
                alert('{% trans "يرجى إدخال اسم مكان التخزين" %}');
                return;
            }

            $.ajax({
                url: '{% url "inventory:add_storage_location_ajax" %}',
                type: 'POST',
                data: {
                    name: locationName,
                    description: locationDescription,
                    csrfmiddlewaretoken: '{{ csrf_token }}'
                },
                success: function(response) {
                    if (response.success) {
                        // Add new storage location to dropdown
                        $('#storage_location').append(new Option(response.storage_location.name, response.storage_location.id, true, true));

                        // Close modal and reset form
                        $('#addLocationModal').modal('hide');
                        $('#locationForm')[0].reset();
                    } else {
                        alert(response.error);
                    }
                },
                error: function() {
                    alert('{% trans "حدث خطأ أثناء إضافة مكان التخزين" %}');
                }
            });
        });
        
        // Initialize barcode scanner
        var scannerInitialized = false;
        
        $('#scannerModal').on('shown.bs.modal', function () {
            if (!scannerInitialized) {
                // إضافة رسالة تحميل
                $('#scanner-container').html('<div class="text-center p-3"><i class="fas fa-spinner fa-spin fa-2x mb-2"></i><p>{% trans "جاري تهيئة الكاميرا..." %}</p></div>');
                
                // التحقق من وجود مكتبة Quagga
                if (typeof Quagga === 'undefined') {
                    $('#scanner-container').html('<div class="alert alert-danger"><i class="fas fa-exclamation-triangle me-2"></i>{% trans "لم يتم تحميل مكتبة ماسح الباركود بشكل صحيح" %}</div>');
                    return;
                }
                
                Quagga.init({
                    inputStream: {
                        name: "Live",
                        type: "LiveStream",
                        target: document.querySelector('#scanner'),
                        constraints: {
                            width: 640,
                            height: 480,
                            facingMode: "environment"
                        },
                    },
                    locator: {
                        patchSize: "medium",
                        halfSample: true
                    },
                    numOfWorkers: 2,
                    frequency: 10,
                    decoder: {
                        readers: [
                            "code_128_reader",
                            "ean_reader",
                            "ean_8_reader",
                            "code_39_reader",
                            "code_39_vin_reader",
                            "codabar_reader",
                            "upc_reader",
                            "upc_e_reader",
                            "i2of5_reader"
                        ],
                        debug: {
                            showCanvas: true,
                            showPatches: false,
                            showFoundPatches: false,
                            showSkeleton: false,
                            showLabels: false,
                            showPatchLabels: false,
                            showRemainingPatchLabels: false,
                            boxFromPatches: {
                                showTransformed: false,
                                showTransformedBox: false,
                                showBB: true
                            }
                        }
                    },
                }, function(err) {
                    if (err) {
                        console.error("خطأ في تهيئة ماسح الباركود:", err);
                        
                        // عرض رسالة خطأ مناسبة للمستخدم
                        let errorMessage = '{% trans "حدث خطأ أثناء تهيئة ماسح الباركود" %}';
                        
                        // تحديد نوع الخطأ وعرض رسالة مناسبة
                        if (err.name === 'NotAllowedError' || err.name === 'PermissionDeniedError') {
                            errorMessage = '{% trans "لم يتم السماح بالوصول إلى الكاميرا. يرجى السماح بالوصول للكاميرا في إعدادات المتصفح." %}';
                        } else if (err.name === 'NotFoundError' || err.name === 'DevicesNotFoundError') {
                            errorMessage = '{% trans "لم يتم العثور على كاميرا متصلة بجهازك." %}';
                        } else if (err.name === 'NotReadableError' || err.name === 'TrackStartError') {
                            errorMessage = '{% trans "لا يمكن الوصول إلى الكاميرا. قد تكون مستخدمة من قبل تطبيق آخر." %}';
                        } else if (err.name === 'OverconstrainedError' || err.name === 'ConstraintNotSatisfiedError') {
                            errorMessage = '{% trans "لا يمكن استخدام الكاميرا بالإعدادات المطلوبة." %}';
                        }
                        
                        $('#scanner-container').html(`<div class="alert alert-danger"><i class="fas fa-exclamation-triangle me-2"></i>${errorMessage}</div>`);
                        return;
                    }
                    
                    console.log("Quagga initialization finished. Ready to start");
                    Quagga.start();
                    scannerInitialized = true;
                    
                    // إعادة عرض عنصر الفيديو بعد التهيئة الناجحة
                    $('#scanner-container').html('<video id="scanner" class="w-100"></video>');
                });
            } else {
                try {
                    Quagga.start();
                } catch (error) {
                    console.error("خطأ في بدء ماسح الباركود:", error);
                    $('#scanner-container').html(`<div class="alert alert-danger"><i class="fas fa-exclamation-triangle me-2"></i>{% trans "حدث خطأ أثناء تشغيل ماسح الباركود" %}</div>`);
                    scannerInitialized = false;
                }
            }
        });

        $('#scannerModal').on('hidden.bs.modal', function () {
            Quagga.stop();
        });

        // When a barcode is detected
        Quagga.onDetected(function(result) {
            var code = result.codeResult.code;
            $('#barcode').val(code);  // تم تصحيح هذا لحفظ الباركود في الحقل الصحيح
            $('#scannerModal').modal('hide');

            // إظهار رسالة نجاح
            var successMessage = '<div class="alert alert-success alert-dismissible fade show mt-2" role="alert">' +
                '<i class="fas fa-check-circle me-2"></i>تم مسح الباركود بنجاح: ' + code +
                '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                '</div>';
            $('#barcode').parent().after(successMessage);
            
            // إظهار رسالة نجاح
            const alertHtml = `
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i> {% trans "تم مسح الباركود بنجاح" %}: ${code}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            `;
            $('.card-body').prepend(alertHtml);
        });
    });
    // كود التعامل مع تعديل الكمية
    $(document).ready(function() {
        // عند تغيير نوع الحركة أو الكمية، حساب الكمية المتوقعة
        $('#movement_type, #quantity_change').on('change input', function() {
            calculateExpectedQuantity();
        });

        function calculateExpectedQuantity() {
            const currentQuantity = parseFloat($('#current_quantity').val()) || 0;
            const quantityChange = parseFloat($('#quantity_change').val()) || 0;
            const movementType = $('#movement_type').val();
            
            let expectedQuantity = currentQuantity;
            
            if (movementType === 'in') {
                expectedQuantity = currentQuantity + quantityChange;
            } else if (movementType === 'out') {
                expectedQuantity = currentQuantity - quantityChange;
            }
            
            // إذا كان هناك عنصر لعرض الكمية المتوقعة، قم بتحديثه
            if ($('#expected_quantity').length === 0) {
                // إنشاء عنصر جديد لعرض الكمية المتوقعة
                const expectedQuantityHtml = `
                <div class="mb-3" id="expected_quantity_container">
                    <label class="form-label">{% trans "الكمية المتوقعة بعد التعديل" %}</label>
                    <input type="text" class="form-control bg-light" id="expected_quantity" value="${expectedQuantity}" readonly>
                </div>`;
                
                // إضافة العنصر بعد حقل الكمية
                $(expectedQuantityHtml).insertAfter($('#quantity_change').closest('.mb-3'));
            } else {
                // تحديث قيمة الكمية المتوقعة
                $('#expected_quantity').val(expectedQuantity);
                
                // إظهار تحذير إذا كانت الكمية المتوقعة سالبة
                if (expectedQuantity < 0) {
                    if ($('#negative_quantity_warning').length === 0) {
                        $('#expected_quantity_container').append(`
                            <div class="alert alert-danger mt-2" id="negative_quantity_warning">
                                <i class="fas fa-exclamation-triangle me-1"></i>
                                {% trans "تحذير: الكمية المتوقعة ستكون سالبة!" %}
                            </div>
                        `);
                    }
                } else {
                    $('#negative_quantity_warning').remove();
                }
            }
        }

        // عند فتح النافذة المنبثقة، إعادة تعيين النموذج
        $('#adjustQuantityModal').on('show.bs.modal', function() {
            $('#adjustQuantityForm')[0].reset();
            $('#expected_quantity_container').remove();
        });
    });
</script>
{% endblock %}
