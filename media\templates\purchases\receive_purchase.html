{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "استلام طلب الشراء" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .purchase-header {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .purchase-info {
        margin-bottom: 0;
    }
    
    .purchase-info dt {
        font-weight: bold;
    }
    
    .purchase-info dd {
        margin-bottom: 0.5rem;
    }
    
    .product-image-small {
        width: 50px;
        height: 50px;
        object-fit: contain;
        border-radius: 4px;
    }
    
    .quantity-input {
        max-width: 100px;
    }
    
    .required-field::after {
        content: " *";
        color: red;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0 text-gray-800">{% trans "استلام طلب الشراء" %}</h1>
    <div>
        <a href="{% url 'purchases:view_purchase' purchase_id=purchase.id %}" class="btn btn-primary">
            <i class="fas fa-arrow-right me-1"></i> {% trans "العودة إلى تفاصيل الطلب" %}
        </a>
    </div>
</div>

{% if messages %}
    {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    {% endfor %}
{% endif %}

<!-- Purchase Header -->
<div class="purchase-header">
    <div class="row">
        <div class="col-md-6">
            <h2>{{ purchase.reference_number }}</h2>
            <p class="text-muted">{% trans "تاريخ الطلب" %}: {{ purchase.date|date:"Y-m-d" }}</p>
        </div>
        <div class="col-md-6 text-md-end">
            <div class="mb-2">
                <span class="badge bg-warning text-dark">{% trans "معلق" %}</span>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-md-6">
            <h5>{% trans "معلومات المورد" %}</h5>
            <dl class="row purchase-info">
                <dt class="col-sm-4">{% trans "اسم المورد" %}</dt>
                <dd class="col-sm-8">{{ purchase.supplier.name }}</dd>
                
                <dt class="col-sm-4">{% trans "رقم الهاتف" %}</dt>
                <dd class="col-sm-8">{{ purchase.supplier.phone }}</dd>
                
                {% if purchase.supplier.email %}
                <dt class="col-sm-4">{% trans "البريد الإلكتروني" %}</dt>
                <dd class="col-sm-8">{{ purchase.supplier.email }}</dd>
                {% endif %}
            </dl>
        </div>
        <div class="col-md-6">
            <h5>{% trans "معلومات الطلب" %}</h5>
            <dl class="row purchase-info">
                <dt class="col-sm-5">{% trans "تاريخ التسليم المتوقع" %}</dt>
                <dd class="col-sm-7">
                    {% if purchase.expected_delivery_date %}
                        {{ purchase.expected_delivery_date|date:"Y-m-d" }}
                    {% else %}
                        -
                    {% endif %}
                </dd>
                
                <dt class="col-sm-5">{% trans "الموظف" %}</dt>
                <dd class="col-sm-7">{{ purchase.employee.get_full_name|default:purchase.employee.username }}</dd>
            </dl>
        </div>
    </div>
</div>

<!-- Receive Form -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">{% trans "استلام البضائع" %}</h6>
    </div>
    <div class="card-body">
        <form method="post">
            {% csrf_token %}
            
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="actual_delivery_date" class="form-label required-field">{% trans "تاريخ الاستلام الفعلي" %}</label>
                        <input type="date" class="form-control" id="actual_delivery_date" name="actual_delivery_date" value="{{ today }}" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="notes" class="form-label">{% trans "ملاحظات" %}</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="{% trans 'أي ملاحظات حول الاستلام...' %}"></textarea>
                    </div>
                </div>
            </div>
            
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th width="50px">{% trans "صورة" %}</th>
                            <th>{% trans "المنتج" %}</th>
                            <th width="100px">{% trans "الكمية المطلوبة" %}</th>
                            <th width="150px">{% trans "الكمية المستلمة" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in items %}
                        <tr>
                            <td>
                                {% if item.product.image %}
                                <img src="{{ item.product.image.url }}" alt="{{ item.product.name }}" class="product-image-small">
                                {% else %}
                                <div class="text-center">
                                    <i class="fas fa-image text-muted"></i>
                                </div>
                                {% endif %}
                            </td>
                            <td>
                                <strong>{{ item.product.code }}</strong> - {{ item.product.name }}
                            </td>
                            <td>{{ item.quantity }}</td>
                            <td>
                                <input type="number" class="form-control quantity-input" name="received_quantity_{{ item.id }}" value="{{ item.quantity }}" min="0" max="{{ item.quantity }}" required>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <div class="alert alert-info mt-3">
                <i class="fas fa-info-circle me-2"></i>
                {% trans "سيتم تحديث حالة الطلب إلى 'تم الاستلام' وتحديث كميات المخزون تلقائيًا." %}
            </div>
            
            <div class="mt-4">
                <button type="submit" class="btn btn-success">
                    <i class="fas fa-check me-1"></i> {% trans "تأكيد الاستلام" %}
                </button>
                <a href="{% url 'purchases:view_purchase' purchase_id=purchase.id %}" class="btn btn-secondary">
                    {% trans "إلغاء" %}
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Validate form before submit
        $('form').submit(function(e) {
            var isValid = true;
            
            // Check if all required fields are filled
            $(this).find('[required]').each(function() {
                if ($(this).val() === '') {
                    isValid = false;
                    $(this).addClass('is-invalid');
                } else {
                    $(this).removeClass('is-invalid');
                }
            });
            
            if (!isValid) {
                e.preventDefault();
                alert('{% trans "يرجى ملء جميع الحقول المطلوبة" %}');
                return false;
            }
            
            return true;
        });
    });
</script>
{% endblock %}
