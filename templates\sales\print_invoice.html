<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة رقم {{ sale.invoice_number }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            direction: rtl;
            text-align: right;
        }
        
        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: white;
        }
        
        .invoice-header {
            border-bottom: 3px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .company-info {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .company-name {
            font-size: 28px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 10px;
        }
        
        .invoice-title {
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
            color: #333;
        }
        
        .invoice-details {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }
        
        .invoice-info, .customer-info {
            width: 48%;
        }
        
        .info-title {
            font-size: 16px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 10px;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }
        
        .info-item {
            margin-bottom: 8px;
        }
        
        .info-label {
            font-weight: bold;
            display: inline-block;
            width: 100px;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        
        .items-table th,
        .items-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: center;
        }
        
        .items-table th {
            background-color: #007bff;
            color: white;
            font-weight: bold;
        }
        
        .items-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .product-name {
            text-align: right;
            font-weight: bold;
        }
        
        .totals-section {
            float: left;
            width: 300px;
            margin-top: 20px;
        }
        
        .totals-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .totals-table td {
            padding: 8px 12px;
            border-bottom: 1px solid #eee;
        }
        
        .totals-table .label {
            font-weight: bold;
            text-align: right;
        }
        
        .totals-table .amount {
            text-align: left;
            font-weight: bold;
        }
        
        .total-row {
            background-color: #007bff;
            color: white;
            font-size: 16px;
        }
        
        .payment-status {
            text-align: center;
            margin-top: 30px;
            padding: 15px;
            border-radius: 5px;
        }
        
        .paid {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .unpaid {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .footer {
            margin-top: 50px;
            text-align: center;
            font-size: 12px;
            color: #666;
            border-top: 1px solid #eee;
            padding-top: 20px;
        }
        
        .clearfix::after {
            content: "";
            display: table;
            clear: both;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 0;
            }
            
            .invoice-container {
                max-width: none;
                margin: 0;
                padding: 0;
            }
            
            .no-print {
                display: none !important;
            }
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <!-- Company Header -->
        <div class="invoice-header">
            <div class="company-info">
                <div class="company-name">نظام إدارة المبيعات</div>
                <div>العنوان: المملكة العربية السعودية</div>
                <div>الهاتف: +966 XX XXX XXXX | البريد الإلكتروني: <EMAIL></div>
            </div>
        </div>

        <!-- Invoice Title -->
        <div class="invoice-title">فاتورة مبيعات</div>

        <!-- Invoice Details -->
        <div class="invoice-details">
            <div class="invoice-info">
                <div class="info-title">معلومات الفاتورة</div>
                <div class="info-item">
                    <span class="info-label">رقم الفاتورة:</span>
                    {{ sale.invoice_number }}
                </div>
                <div class="info-item">
                    <span class="info-label">التاريخ:</span>
                    {{ sale.date|date:"Y-m-d" }}
                </div>
                <div class="info-item">
                    <span class="info-label">الوقت:</span>
                    {{ sale.date|date:"H:i" }}
                </div>
                <div class="info-item">
                    <span class="info-label">الموظف:</span>
                    {{ sale.employee.get_full_name|default:sale.employee.username }}
                </div>
            </div>

            <div class="customer-info">
                <div class="info-title">معلومات العميل</div>
                <div class="info-item">
                    <span class="info-label">الاسم:</span>
                    {{ sale.customer.name }}
                </div>
                {% if sale.customer.phone %}
                <div class="info-item">
                    <span class="info-label">الهاتف:</span>
                    {{ sale.customer.phone }}
                </div>
                {% endif %}
                {% if sale.customer.email %}
                <div class="info-item">
                    <span class="info-label">البريد:</span>
                    {{ sale.customer.email }}
                </div>
                {% endif %}
                {% if sale.customer.address %}
                <div class="info-item">
                    <span class="info-label">العنوان:</span>
                    {{ sale.customer.address }}
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Items Table -->
        <table class="items-table">
            <thead>
                <tr>
                    <th>م</th>
                    <th>اسم المنتج</th>
                    <th>كود المنتج</th>
                    <th>الكمية</th>
                    <th>سعر الوحدة</th>
                    <th>المجموع</th>
                </tr>
            </thead>
            <tbody>
                {% for item in sale.items.all %}
                <tr>
                    <td>{{ forloop.counter }}</td>
                    <td class="product-name">{{ item.product.name }}</td>
                    <td>{{ item.product.code }}</td>
                    <td>{{ item.quantity }}</td>
                    <td>{{ item.unit_price|floatformat:2 }} د.م</td>
                    <td>{{ item.subtotal|floatformat:2 }} د.م</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <!-- Totals Section -->
        <div class="clearfix">
            <div class="totals-section">
                <table class="totals-table">
                    <tr>
                        <td class="label">المجموع الفرعي:</td>
                        <td class="amount">{{ sale.subtotal|floatformat:2 }} د.م</td>
                    </tr>
                    {% if sale.discount > 0 %}
                    <tr>
                        <td class="label">الخصم:</td>
                        <td class="amount">{{ sale.discount|floatformat:2 }} د.م</td>
                    </tr>
                    {% endif %}
                    <tr>
                        <td class="label">الضريبة ({{ sale.tax_rate }}%):</td>
                        <td class="amount">{{ sale.tax_amount|floatformat:2 }} د.م</td>
                    </tr>
                    <tr class="total-row">
                        <td class="label">المجموع الكلي:</td>
                        <td class="amount">{{ sale.total_amount|floatformat:2 }} د.م</td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- Payment Status -->
        <div class="payment-status {% if sale.is_paid %}paid{% else %}unpaid{% endif %}">
            {% if sale.is_paid %}
                <strong>✓ تم الدفع بالكامل</strong>
                <br>المبلغ المدفوع: {{ sale.paid_amount|floatformat:2 }} د.م
            {% else %}
                <strong>⚠ لم يتم الدفع</strong>
                {% if sale.paid_amount > 0 %}
                    <br>المبلغ المدفوع: {{ sale.paid_amount|floatformat:2 }} د.م
                    <br>المبلغ المتبقي: {{ sale.remaining_amount|floatformat:2 }} د.م
                {% else %}
                    <br>المبلغ المطلوب: {{ sale.total_amount|floatformat:2 }} د.م
                {% endif %}
            {% endif %}
        </div>

        {% if sale.notes %}
        <!-- Notes -->
        <div style="margin-top: 30px; padding: 15px; background-color: #f8f9fa; border-radius: 5px;">
            <strong>ملاحظات:</strong>
            <p>{{ sale.notes }}</p>
        </div>
        {% endif %}

        <!-- Footer -->
        <div class="footer">
            <p>شكراً لتعاملكم معنا</p>
            <p>تم إنشاء هذه الفاتورة بواسطة نظام إدارة المبيعات</p>
        </div>
    </div>

    <script>
        // Auto print when page loads
        window.onload = function() {
            window.print();
        }
    </script>
</body>
</html>
