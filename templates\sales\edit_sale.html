{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "تعديل البيع" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<!-- DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
<!-- Google Fonts -->
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
<!-- Animate.css -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
<style>
    :root {
        --primary-color:hsl(197, 99.20%, 49.60%);
        --primary-dark:hsl(197, 99.20%, 49.60%);
        --secondary-color: #f8fafc;
        --accent-color: #06b6d4;
        --success-color: #10b981;
        --warning-color: #f59e0b;
        --danger-color: #ef4444;
        --text-primary: #1e293b;
        --text-secondary: #64748b;
        --border-color: #e2e8f0;
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
        --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    }

    * {
        font-family: 'Cairo', sans-serif;
    }

    body {
        background: linear-gradient(135deg,hsl(197, 87.60%, 68.40%) 0%, hsl(197, 87.60%, 68.40%) 100%);
        min-height: 100vh;
    }

    .main-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        box-shadow: var(--shadow-xl);
        margin: 20px;
        padding: 30px;
        animation: fadeInUp 0.6s ease-out;
    }

    .page-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
        color: white;
        padding: 25px 30px;
        border-radius: 15px;
        margin-bottom: 30px;
        box-shadow: var(--shadow-lg);
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        pointer-events: none;
    }

    .page-title {
        font-size: 2rem;
        font-weight: 700;
        margin: 0;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .required-field::after {
        content: " *";
        color: var(--danger-color);
        font-weight: bold;
    }

    .form-section {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: var(--shadow-md);
        border: 1px solid var(--border-color);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .form-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 4px;
        background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    }

    .form-section:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .form-section-title {
        color: var(--text-primary);
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 2px solid var(--border-color);
        position: relative;
    }

    .form-section-title::before {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 50px;
        height: 2px;
        background: var(--primary-color);
    }

    .form-control, .form-select {
        border: 2px solid var(--border-color);
        border-radius: 10px;
        padding: 12px 15px;
        font-size: 0.95rem;
        transition: all 0.3s ease;
        background: white;
    }

    .form-control:focus, .form-select:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        transform: translateY(-1px);
    }

    .btn {
        border-radius: 10px;
        padding: 12px 20px;
        font-weight: 600;
        transition: all 0.3s ease;
        border: none;
        position: relative;
        overflow: hidden;
    }

    .btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .btn:hover::before {
        left: 100%;
    }

    .btn-primary {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        color: white;
        box-shadow: var(--shadow-md);
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .btn-add-product {
        background: linear-gradient(135deg, var(--success-color), #059669);
        color: white;
        box-shadow: var(--shadow-md);
    }

    .btn-add-product:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .product-row {
        transition: all 0.3s ease;
        border-radius: 10px;
    }

    .product-row:hover {
        background: linear-gradient(135deg, #f8fafc, #f1f5f9);
        transform: translateX(5px);
    }

    .product-image-small {
        width: 55px;
        height: 55px;
        object-fit: contain;
        border-radius: 10px;
        border: 2px solid var(--border-color);
        padding: 4px;
        background: white;
        transition: all 0.3s ease;
        box-shadow: var(--shadow-sm);
    }

    .product-image-small:hover {
        transform: scale(1.1) rotate(2deg);
        box-shadow: var(--shadow-md);
        z-index: 100;
    }

    .barcode-scanner {
        position: relative;
    }

    /* تنسيقات ماسح الباركود */
    .barcode-scanner {
        position: relative;
        margin-bottom: 20px;
    }

    .barcode-scanner .scan-icon {
        position: absolute;
        top: 50%;
        left: 15px;
        transform: translateY(-50%);
        cursor: pointer;
        color: var(--primary-color);
        font-size: 1.2rem;
        transition: all 0.3s ease;
        z-index: 10;
    }

    .barcode-scanner .scan-icon:hover {
        color: var(--primary-dark);
        transform: translateY(-50%) scale(1.1);
    }

    .barcode-scanner .form-control {
        padding-left: 50px;
        background: linear-gradient(135deg, #f8fafc, white);
        border: 2px solid var(--border-color);
        border-radius: 15px;
        font-size: 1rem;
        height: 55px;
        transition: all 0.3s ease;
    }

    .barcode-scanner .form-control:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        background: white;
    }

    #scanner-container {
        width: 100%;
        height: 350px;
        overflow: hidden;
        position: relative;
        background: linear-gradient(135deg, #1e293b, #334155);
        border-radius: 15px;
        box-shadow: var(--shadow-lg);
    }

    #scanner-video-container {
        width: 100%;
        height: 100%;
        position: relative;
    }

    #scanner-video {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 15px;
    }

    /* حدود منطقة مسح الباركود */
    .barcode-scanner-border {
        position: absolute;
        top: 50%;
        left: 50%;
        width: 70%;
        height: 30%;
        transform: translate(-50%, -50%);
        border: 3px solid var(--accent-color);
        border-radius: 15px;
        box-shadow: 0 0 0 100vw rgba(0, 0, 0, 0.6);
        pointer-events: none;
        z-index: 10;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { box-shadow: 0 0 0 100vw rgba(0, 0, 0, 0.6), 0 0 0 0 var(--accent-color); }
        50% { box-shadow: 0 0 0 100vw rgba(0, 0, 0, 0.6), 0 0 0 10px rgba(6, 182, 212, 0.3); }
        100% { box-shadow: 0 0 0 100vw rgba(0, 0, 0, 0.6), 0 0 0 0 var(--accent-color); }
    }

    /* تأثير عند مسح الباركود */
    .barcode-scanned {
        animation: barcode-success 0.8s ease-out;
    }

    @keyframes barcode-success {
        0% {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 100vw rgba(0, 0, 0, 0.6);
        }
        50% {
            border-color: var(--success-color);
            box-shadow: 0 0 0 100vw rgba(16, 185, 129, 0.3);
            transform: translate(-50%, -50%) scale(1.1);
        }
        100% {
            border-color: var(--success-color);
            box-shadow: 0 0 0 100vw rgba(0, 0, 0, 0.6);
            transform: translate(-50%, -50%) scale(1);
        }
    }

    /* تنسيقات النوافذ المنبثقة */
    .modal-content {
        border: none;
        border-radius: 20px;
        overflow: hidden;
        box-shadow: var(--shadow-xl);
        backdrop-filter: blur(10px);
        background: rgba(255, 255, 255, 0.98);
    }

    .modal-header {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        color: white;
        padding: 20px 25px;
        border: none;
        position: relative;
    }

    .modal-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
        pointer-events: none;
    }

    .modal-title {
        font-weight: 700;
        font-size: 1.3rem;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .modal-body {
        padding: 25px;
        background: white;
    }

    .modal-footer {
        background: var(--secondary-color);
        border: none;
        padding: 20px 25px;
    }

    /* تنسيقات نافذة إضافة المنتج */
    #addProductModal .search-container {
        position: relative;
        margin-bottom: 25px;
    }

    #addProductModal .input-group {
        border-radius: 15px;
        overflow: hidden;
        box-shadow: var(--shadow-md);
    }

    #addProductModal .input-group-text {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        color: white;
        border: none;
        padding: 15px 20px;
    }

    #addProductModal .product-image-tiny {
        width: 45px;
        height: 45px;
        object-fit: contain;
        background: var(--secondary-color);
        border-radius: 8px;
        transition: all 0.3s ease;
        border: 2px solid var(--border-color);
    }

    #addProductModal .product-item {
        transition: all 0.3s ease;
        border-radius: 10px;
        margin-bottom: 5px;
    }

    #addProductModal .product-item:hover {
        background: linear-gradient(135deg, #f8fafc, #f1f5f9);
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }

    #addProductModal .has-promotion {
        background: linear-gradient(135deg, rgba(16, 185, 129, 0.05), rgba(5, 150, 105, 0.05));
        border-left: 4px solid var(--success-color);
    }

    #addProductModal .filter-btn {
        border-radius: 25px;
        padding: 8px 20px;
        font-weight: 600;
        transition: all 0.3s ease;
        border: 2px solid var(--border-color);
        background: white;
        color: var(--text-secondary);
    }

    #addProductModal .filter-btn:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
        border-color: var(--primary-color);
        color: var(--primary-color);
    }

    #addProductModal .filter-btn.active {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        color: white;
        border-color: var(--primary-color);
        box-shadow: var(--shadow-md);
    }

    #addProductModal .price-container {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
    }

    #addProductModal .discount-badge {
        margin-top: 5px;
    }

    #addProductModal .product-table-container {
        max-height: 450px;
        overflow-y: auto;
        border-radius: 15px;
        border: 2px solid var(--border-color);
        background: white;
        box-shadow: var(--shadow-sm);
    }

    #addProductModal .select-product {
        transition: all 0.3s ease;
        border-radius: 20px;
        padding: 8px 16px;
        font-weight: 600;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        border: none;
        color: white;
    }

    #addProductModal .select-product:hover:not([disabled]) {
        transform: scale(1.05) translateY(-2px);
        box-shadow: var(--shadow-md);
    }

    #addProductModal .select-product:disabled {
        background: var(--text-secondary);
        opacity: 0.6;
        cursor: not-allowed;
    }

    /* بطاقة الملخص */
    .summary-card {
        position: sticky;
        top: 20px;
        background: white;
        border-radius: 20px;
        box-shadow: var(--shadow-xl);
        border: none;
        overflow: hidden;
        animation: slideInRight 0.6s ease-out;
    }

    .summary-card .card-header {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        color: white;
        padding: 20px 25px;
        border: none;
        position: relative;
    }

    .summary-card .card-header h6 {
        font-weight: 700;
        font-size: 1.1rem;
        margin: 0;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .summary-card .card-body {
        padding: 25px;
        background: white;
    }

    .summary-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid var(--border-color);
        transition: all 0.3s ease;
    }

    .summary-item:last-child {
        border-bottom: none;
        font-weight: 700;
        font-size: 1.1rem;
        color: var(--primary-color);
        background: linear-gradient(135deg, rgba(79, 70, 229, 0.05), rgba(55, 48, 163, 0.05));
        margin: 15px -25px -25px -25px;
        padding: 20px 25px;
    }

    /* تأثيرات الحركة */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(30px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="main-container">
        <!-- Page Header -->
        <div class="page-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="mb-2">
                        <i class="fas fa-edit me-3"></i>{% trans "تعديل التخفيضات" %}
                    </h1>
                    <p class="mb-0">{% trans "تعديل بيانات التخفيضات والمنتجات المباعة" %}</p>
                </div>
                <div>
                    <a href="{% url 'sales:view_sale' sale.id %}" class="btn btn-light btn-enhanced">
                        <i class="fas fa-arrow-right me-2"></i>{% trans "العودة إلى تفاصيل البيع" %}
                    </a>
                </div>
            </div>
        </div>

{% if messages %}
    {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    {% endfor %}
{% endif %}

<div class="alert alert-warning mb-4">
    <i class="fas fa-exclamation-triangle me-1"></i>
    {% trans "تنبيه: تعديل البيع سيؤثر على المخزون. سيتم تعديل كميات المنتجات في المخزون وفقاً للتغييرات." %}
</div>

<form method="post" id="saleForm">
    {% csrf_token %}

    <div class="row">
        <div class="col-md-8">
            <!-- Basic Information Section -->
            <div class="form-section">
                <h5 class="form-section-title">{% trans "المعلومات الأساسية" %}</h5>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="customer" class="form-label required-field">{% trans "العميل" %}</label>
                        <select class="form-select" id="customer" name="customer" required>
                            <option value="">{% trans "اختر العميل" %}</option>
                            {% for customer in customers %}
                            <option value="{{ customer.id }}" {% if customer.id == sale.customer.id %}selected{% endif %}>{{ customer.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="date" class="form-label required-field">{% trans "تاريخ البيع" %}</label>
                        <input type="date" class="form-control" id="date" name="date" value="{{ sale.date|date:'Y-m-d' }}" required>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="payment_method" class="form-label required-field">{% trans "طريقة الدفع" %}</label>
                        <select class="form-select" id="payment_method" name="payment_method" required>
                            <option value="cash" {% if sale.payment_method == 'cash' %}selected{% endif %}>{% trans "نقدي" %}</option>
                            <option value="card" {% if sale.payment_method == 'card' %}selected{% endif %}>{% trans "بطاقة ائتمان" %}</option>
                            <option value="transfer" {% if sale.payment_method == 'transfer' %}selected{% endif %}>{% trans "تحويل بنكي" %}</option>
                            <option value="check" {% if sale.payment_method == 'check' %}selected{% endif %}>{% trans "شيك" %}</option>
                            <option value="credit" {% if sale.payment_method == 'credit' %}selected{% endif %}>{% trans "آجل" %}</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="status" class="form-label required-field">{% trans "الحالة" %}</label>
                        <select class="form-select" id="status" name="status" required>
                            <option value="completed" {% if sale.status == 'completed' %}selected{% endif %}>{% trans "مكتمل" %}</option>
                            <option value="pending" {% if sale.status == 'pending' %}selected{% endif %}>{% trans "معلق" %}</option>
                            <option value="cancelled" {% if sale.status == 'cancelled' %}selected{% endif %}>{% trans "ملغي" %}</option>
                        </select>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="notes" class="form-label">{% trans "ملاحظات" %}</label>
                    <textarea class="form-control" id="notes" name="notes" rows="2">{{ sale.notes }}</textarea>
                </div>
            </div>

            <!-- Products Section -->
            <div class="form-section">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h5 class="form-section-title mb-0">{% trans "المنتجات" %}</h5>
                    <div>
                        <button type="button" class="btn btn-primary btn-enhanced me-2" data-bs-toggle="modal" data-bs-target="#addProductModal">
                            <i class="fas fa-plus me-2"></i>{% trans "إضافة منتج" %}
                        </button>
                        <button type="button" class="btn btn-info btn-enhanced" data-bs-toggle="modal" data-bs-target="#scannerModal">
                            <i class="fas fa-barcode me-2"></i>{% trans "مسح الباركود" %}
                        </button>
                    </div>
                </div>

                <div class="barcode-scanner mb-3">
                    <input type="text" class="form-control" id="barcodeInput" placeholder="{% trans 'أدخل الباركود أو اضغط على أيقونة المسح...' %}">
                    <span class="scan-icon" data-bs-toggle="modal" data-bs-target="#scannerModal">
                        <i class="fas fa-barcode"></i>
                    </span>
                </div>

                <div class="table-responsive">
                    <table class="table table-bordered" id="productsTable">
                        <thead>
                            <tr>
                                <th width="50px">{% trans "صورة" %}</th>
                                <th>{% trans "المنتج" %}</th>
                                <th width="100px">{% trans "الكمية" %}</th>
                                <th width="150px">{% trans "سعر الوحدة" %}</th>
                                <th width="150px">{% trans "المجموع" %}</th>
                                <th width="50px">{% trans "الإجراءات" %}</th>
                            </tr>
                        </thead>
                        <tbody id="productsTableBody">
                            {% for item in items %}
                            <tr class="product-row">
                                <td>
                                    {% if item.product.image %}
                                    <img src="{{ item.product.image.url }}" alt="{{ item.product.name }}" class="product-image-small">
                                    {% else %}
                                    <div class="text-center">
                                        <i class="fas fa-box text-muted"></i>
                                    </div>
                                    {% endif %}
                                </td>
                                <td>
                                    <input type="hidden" name="product_ids[]" value="{{ item.product.id }}">
                                    <input type="hidden" name="item_ids[]" value="{{ item.id }}">
                                    <strong>{{ item.product.code }}</strong> - {{ item.product.name }}
                                    <div class="small text-muted">{% trans "المخزون المتاح:" %} {{ item.product.quantity|add:item.quantity }}</div>
                                </td>
                                <td>
                                    <input type="number" class="form-control form-control-sm quantity-input" name="quantities[]" value="{{ item.quantity }}" min="1" max="{{ item.product.quantity|add:item.quantity }}" required data-stock="{{ item.product.quantity|add:item.quantity }}" data-original="{{ item.quantity }}">
                                </td>
                                <td>
                                    <div class="input-group input-group-sm">
                                        <input type="number" class="form-control form-control-sm unit-price-input" name="unit_prices[]" value="{{ item.unit_price }}" step="0.01" min="0" required>
                                        <span class="input-group-text">د.م</span>
                                    </div>
                                </td>
                                <td>
                                    <span class="subtotal">{{ item.subtotal }}</span> د.م
                                </td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-danger remove-product">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            {% empty %}
                            <tr id="noProductsRow">
                                <td colspan="6" class="text-center">{% trans "لم يتم إضافة منتجات بعد" %}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle me-1"></i>
                    {% trans "يمكنك إضافة المنتجات باستخدام زر 'إضافة منتج' أعلاه أو عن طريق مسح الباركود. تأكد من إضافة منتج واحد على الأقل قبل حفظ الفاتورة." %}
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Summary Section -->
            <div class="card shadow summary-card">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "ملخص الفاتورة" %}</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>{% trans "المجموع الفرعي:" %}</span>
                            <span id="subtotalSummary">{{ sale.subtotal }}</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>{% trans "ضريبة القيمة المضافة (15%):" %}</span>
                            <span id="taxSummary">{{ sale.tax_amount }}</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="discount" class="form-label">{% trans "الخصم:" %}</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="discount" name="discount" step="0.01" min="0" value="{{ sale.discount }}">
                            <span class="input-group-text">د.م</span>
                        </div>
                    </div>
                    <hr>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <strong>{% trans "المجموع الكلي:" %}</strong>
                            <strong id="totalSummary">{{ sale.total_amount }}</strong>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="amount_paid" class="form-label">{% trans "المبلغ المدفوع:" %}</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="amount_paid" name="amount_paid" step="0.01" min="0" value="{{ total_paid }}">
                            <span class="input-group-text">د.م</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>{% trans "المبلغ المتبقي:" %}</span>
                            <span id="remainingAmount">{{ remaining_amount }}</span>
                        </div>
                    </div>

                    <div class="d-grid gap-3">
                        <button type="submit" class="btn btn-success btn-enhanced" id="saveBtn">
                            <i class="fas fa-save me-2"></i>{% trans "حفظ التغييرات" %}
                        </button>
                        <a href="{% url 'sales:view_sale' sale.id %}" class="btn btn-secondary btn-enhanced">
                            <i class="fas fa-times me-2"></i>{% trans "إلغاء" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

    </div> <!-- End main-container -->
</div> <!-- End container-fluid -->

<!-- Add Product Modal -->
<div class="modal fade" id="addProductModal" tabindex="-1" aria-labelledby="addProductModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addProductModalLabel">
                    <i class="fas fa-box-open me-2"></i>{% trans "إضافة منتج إلى الفاتورة" %}
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- شريط البحث المتقدم -->
                <div class="search-container">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control" id="productSearch"
                               placeholder="{% trans 'ابحث عن المنتج بالاسم، الكود، أو الباركود...' %}">
                        <button class="btn btn-outline-secondary" type="button" id="clearProductSearch">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>

                <!-- فلاتر سريعة -->
                <div class="mb-4">
                    <div class="d-flex flex-wrap gap-2">
                        <button type="button" class="filter-btn active" data-filter="all">
                            <i class="fas fa-list me-1"></i>{% trans "جميع المنتجات" %}
                        </button>
                        <button type="button" class="filter-btn" data-filter="available">
                            <i class="fas fa-check-circle me-1"></i>{% trans "متوفر" %}
                        </button>
                        <button type="button" class="filter-btn" data-filter="low-stock">
                            <i class="fas fa-exclamation-triangle me-1"></i>{% trans "مخزون منخفض" %}
                        </button>
                        <button type="button" class="filter-btn" data-filter="promotion">
                            <i class="fas fa-percentage me-1"></i>{% trans "عروض خاصة" %}
                        </button>
                    </div>
                </div>

                <!-- جدول المنتجات -->
                <div class="product-table-container">
                    <table class="table table-hover mb-0" id="productsSearchTable">
                        <thead class="table-dark">
                            <tr>
                                <th style="width: 80px;">{% trans "صورة" %}</th>
                                <th style="width: 100px;">{% trans "الكود" %}</th>
                                <th>{% trans "اسم المنتج" %}</th>
                                <th style="width: 120px;">{% trans "الفئة" %}</th>
                                <th style="width: 80px;">{% trans "المخزون" %}</th>
                                <th style="width: 120px;">{% trans "السعر" %}</th>
                                <th style="width: 100px;">{% trans "إضافة" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for product in products %}
                            <tr class="product-item {% if product.has_active_promotion %}has-promotion{% endif %}">
                                <td class="text-center">
                                    {% if product.image %}
                                        <img src="{{ product.image.url }}" alt="{{ product.name }}" class="product-image-tiny">
                                    {% else %}
                                        <div class="product-image-tiny d-flex align-items-center justify-content-center">
                                            <i class="fas fa-image text-muted"></i>
                                        </div>
                                    {% endif %}
                                </td>
                                <td>
                                    <strong>{{ product.code }}</strong>
                                    {% if product.barcode %}
                                        <br><small class="text-muted">{{ product.barcode }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <div>
                                        <strong>{{ product.name }}</strong>
                                        {% if product.description %}
                                            <br><small class="text-muted">{{ product.description|truncatewords:8 }}</small>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ product.category.name }}</span>
                                </td>
                                <td class="text-center">
                                    {% if product.quantity > 0 %}
                                        <span class="badge bg-success">{{ product.quantity }}</span>
                                    {% else %}
                                        <span class="badge bg-danger">نفد</span>
                                    {% endif %}
                                </td>
                                <td class="price-container">
                                    {% if product.has_active_promotion %}
                                        <div>
                                            <span class="text-decoration-line-through text-muted small">{{ product.sale_price }} د.م</span>
                                            <br>
                                            <span class="fw-bold fs-6 text-success">{{ product.promotion_price }} د.م</span>
                                            <span class="badge bg-success discount-badge">
                                                <i class="fas fa-percentage me-1"></i>{{ product.discount_percentage }}%
                                            </span>
                                        </div>
                                    {% else %}
                                        <span class="fw-bold fs-6 text-primary">{{ product.sale_price }} د.م</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <button type="button" class="btn btn-sm select-product"
                                            data-id="{{ product.id }}"
                                            data-code="{{ product.code }}"
                                            data-name="{{ product.name }}"
                                            data-price="{% if product.has_active_promotion %}{{ product.promotion_price }}{% else %}{{ product.sale_price }}{% endif %}"
                                            data-stock="{{ product.quantity }}"
                                            data-image="{% if product.image %}{{ product.image.url }}{% endif %}"

                                            {% if product.quantity == 0 %}disabled{% endif %}>
                                        {% if product.quantity == 0 %}
                                            <i class="fas fa-ban me-1"></i>نفد
                                        {% else %}
                                            <i class="fas fa-plus me-1"></i>إضافة
                                        {% endif %}
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إغلاق" %}</button>
            </div>
        </div>
    </div>
</div>

<!-- Barcode Scanner Modal -->
<div class="modal fade" id="scannerModal" tabindex="-1" aria-labelledby="scannerModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="scannerModalLabel">{% trans "مسح الباركود" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="scanner-container">
                    <video id="scanner-video"></video>
                </div>
                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle me-1"></i>
                    {% trans "قم بتوجيه الكاميرا نحو الباركود للمسح التلقائي." %}
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إغلاق" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

<!-- Quagga JS for barcode scanning -->
<script src="https://cdn.jsdelivr.net/npm/quagga@0.12.1/dist/quagga.min.js"></script>
<script>
    $(document).ready(function() {
        // إعداد CSRF token لجميع AJAX requests
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        const csrftoken = getCookie('csrftoken');

        // إعداد CSRF token لجميع AJAX requests
        $.ajaxSetup({
            beforeSend: function(xhr, settings) {
                if (!this.crossDomain && !/^(GET|HEAD|OPTIONS|TRACE)$/i.test(settings.type) && !this.crossDomain) {
                    xhr.setRequestHeader("X-CSRFToken", csrftoken);
                }
            }
        });

        // Initialize DataTable for products search
        var searchTable = $('#productsSearchTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json"
            },
            "pageLength": 5
        });

        // Product search
        $('#productSearch').keyup(function() {
            searchTable.search($(this).val()).draw();
        });

        // Select Product
        $(document).on('click', '.select-product', function() {
            var productId = $(this).data('id');
            var productCode = $(this).data('code');
            var productName = $(this).data('name');
            var productPrice = $(this).data('price');
            var productStock = $(this).data('stock');
            var productImage = $(this).data('image');

            // التحقق من توفر المنتج في المخزون
            if (productStock <= 0) {
                alert('{% trans "المنتج غير متوفر في المخزون" %}');
                return;
            }

            // Check if product already exists in the table
            var existingRow = $('input[name="product_ids[]"][value="' + productId + '"]').closest('tr');

            if (existingRow.length > 0) {
                // Product already exists, increment quantity
                var quantityInput = existingRow.find('.quantity-input');
                var currentQuantity = parseInt(quantityInput.val());

                // التحقق من أن الكمية الجديدة لا تتجاوز المخزون
                if (currentQuantity + 1 > productStock) {
                    alert('{% trans "الكمية المطلوبة تتجاوز المخزون المتاح!" %}');
                    return;
                }

                quantityInput.val(currentQuantity + 1).trigger('input');

                // Show notification
                alert('{% trans "تم زيادة كمية المنتج" %}');
            } else {
                // Remove "no products" row if exists
                $('#noProductsRow').remove();

                // Add product to table
                var hasPromotion = $(this).data('has-promotion') === true;
                var regularPrice = $(this).data('regular-price');
                var promotionPrice = $(this).data('promotion-price');
                var promotionClass = hasPromotion ? 'table-success' : '';
                var promotionBadge = hasPromotion ? `<span class="badge bg-danger ms-2">{% trans "عرض" %}</span>` : '';
                var priceDisplay = hasPromotion ?
                    `<div class="small text-muted"><del>${regularPrice}</del> <span class="text-danger fw-bold">${productPrice}</span> د.م</div>` :
                    '';

                var newRow = `
                    <tr class="product-row ${promotionClass}">
                        <td>
                            ${productImage ?
                                `<img src="${productImage}" alt="${productName}" class="product-image-small">` :
                                `<div class="text-center"><i class="fas fa-box text-muted"></i></div>`
                            }
                        </td>
                        <td>
                            <input type="hidden" name="product_ids[]" value="${productId}">
                            <input type="hidden" name="item_ids[]" value="">
                            <strong>${productCode}</strong> - ${productName} ${promotionBadge}
                            <div class="small text-muted">{% trans "المخزون المتاح:" %} ${productStock}</div>
                            ${priceDisplay}
                        </td>
                        <td>
                            <input type="number" class="form-control form-control-sm quantity-input" name="quantities[]" value="1" min="1" max="${productStock}" required data-stock="${productStock}" data-original="0">
                        </td>
                        <td>
                            <div class="input-group input-group-sm">
                                <input type="number" class="form-control form-control-sm unit-price-input" name="unit_prices[]" value="${productPrice}" step="0.01" min="0" required>
                                <span class="input-group-text">د.م</span>
                            </div>
                        </td>
                        <td>
                            <span class="subtotal">${productPrice}</span> د.م
                        </td>
                        <td>
                            <button type="button" class="btn btn-sm btn-danger remove-product">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;

                $('#productsTableBody').append(newRow);
            }

            // Close modal
            $('#addProductModal').modal('hide');

            // Update summary
            updateSummary();
        });

        // Remove Product
        $(document).on('click', '.remove-product', function() {
            $(this).closest('tr').remove();

            // If no products left, add "no products" row
            if ($('#productsTableBody tr').length === 0) {
                $('#productsTableBody').html('<tr id="noProductsRow"><td colspan="6" class="text-center">{% trans "لم يتم إضافة منتجات بعد" %}</td></tr>');
            }

            // Update summary
            updateSummary();
        });

        // Update subtotal when quantity or unit price changes
        $(document).on('input', '.quantity-input, .unit-price-input', function() {
            updateRowSubtotal($(this).closest('tr'));
        });

        // Function to update row subtotal
        function updateRowSubtotal(row) {
            var quantity = parseFloat(row.find('.quantity-input').val()) || 0;
            var unitPrice = parseFloat(row.find('.unit-price-input').val()) || 0;
            var stock = parseInt(row.find('.quantity-input').data('stock'));

            // Check if quantity exceeds stock
            if (quantity > stock) {
                alert('{% trans "الكمية المطلوبة تتجاوز المخزون المتاح!" %}');
                row.find('.quantity-input').val(stock);
                quantity = stock;
            }

            var subtotal = quantity * unitPrice;
            row.find('.subtotal').text(subtotal.toFixed(2));

            // Update summary
            updateSummary();
        }

        // Update summary
        function updateSummary() {
            var subtotal = 0;

            // Calculate subtotal
            $('.subtotal').each(function() {
                subtotal += parseFloat($(this).text()) || 0;
            });

            // Calculate tax
            var taxRate = parseFloat({{ tax.rate|default:"0" }}); // VAT rate from settings
            var tax = subtotal * (taxRate / 100);

            // Get discount
            var discount = parseFloat($('#discount').val()) || 0;

            // Calculate total
            var total = subtotal + tax - discount;
            if (total < 0) total = 0;

            // Get amount paid
            var amountPaid = parseFloat($('#amount_paid').val()) || 0;

            // Calculate remaining amount
            var remaining = total - amountPaid;

            // Update summary
            $('#subtotalSummary').text(subtotal.toFixed(2));
            $('#taxSummary').text(tax.toFixed(2));
            $('#totalSummary').text(total.toFixed(2));
            $('#remainingAmount').text(remaining.toFixed(2));

            // Update amount paid if payment method is cash and total changed
            if ($('#payment_method').val() === 'cash') {
                $('#amount_paid').val(total.toFixed(2));
                $('#remainingAmount').text('0.00');
            }
        }

        // Update discount
        $('#discount').on('input', function() {
            updateSummary();
        });

        // Update amount paid
        $('#amount_paid').on('input', function() {
            updateSummary();
        });

        // Change payment method
        $('#payment_method').change(function() {
            var method = $(this).val();
            var total = parseFloat($('#totalSummary').text()) || 0;

            if (method === 'cash') {
                $('#amount_paid').val(total.toFixed(2));
                $('#remainingAmount').text('0.00');
            } else if (method === 'credit') {
                $('#amount_paid').val('0.00');
                $('#remainingAmount').text(total.toFixed(2));
            }
        });

        // Barcode input handling
        $('#barcodeInput').keypress(function(e) {
            if (e.which === 13) { // Enter key
                e.preventDefault();
                var barcode = $(this).val().trim();
                if (barcode) {
                    findProductByBarcode(barcode);
                    $(this).val('');
                }
            }
        });

        // Find product by barcode
        function findProductByBarcode(barcode) {
            console.log('DEBUG: Starting barcode search for:', barcode);

            $.ajax({
                url: "{% url 'inventory:barcode:scan_barcode' %}",
                type: 'POST',
                data: {
                    'barcode_number': barcode,
                    'csrfmiddlewaretoken': '{{ csrf_token }}'
                },
                success: function(data) {
                    if (data.product) {
                        // تم العثور على المنتج
                        console.log('Product found:', data.product);

                        // إضافة المنتج إلى السلة تلقائيًا
                        addProductToCartByBarcode(data.product);

                        // عرض رسالة نجاح
                        alert('تم إضافة "' + data.product.name + '" إلى السلة');

                        $('#barcodeInput').val(''); // مسح حقل الإدخال
                    } else if (data.error) {
                        // لم يتم العثور على المنتج أو حدث خطأ آخر
                        alert(data.error);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX Error:', status, error);
                    alert('{% trans "حدث خطأ أثناء البحث عن المنتج. يرجى المحاولة مرة أخرى." %}');
                }
            });
        }

        // وظيفة إضافة منتج إلى السلة عبر الباركود
        function addProductToCartByBarcode(product) {
            // التحقق من وجود المنتج في الجدول
            var existingRow = $('input[name="product_ids[]"][value="' + product.id + '"]').closest('tr');

            if (existingRow.length > 0) {
                // المنتج موجود بالفعل، زيادة الكمية
                var quantityInput = existingRow.find('.quantity-input');
                var currentQuantity = parseInt(quantityInput.val());
                var maxStock = parseInt(quantityInput.data('stock'));

                if (currentQuantity < maxStock) {
                    quantityInput.val(currentQuantity + 1).trigger('input');
                    alert('تم زيادة كمية "' + product.name + '"');
                } else {
                    alert('لا يمكن زيادة الكمية. الكمية المتاحة في المخزون: ' + maxStock);
                }
            } else {
                // منتج جديد، إضافته إلى الجدول
                $('#noProductsRow').remove();

                var promotionClass = product.has_promotion ? 'table-success' : '';
                var promotionBadge = product.has_promotion ? `<span class="badge bg-danger ms-2">عرض</span>` : '';
                var priceDisplay = product.has_promotion ?
                    `<div class="small text-muted"><del>${product.regular_price}</del> <span class="text-danger fw-bold">${product.price}</span> د.م</div>` :
                    '';

                var newRow = `
                    <tr class="product-row ${promotionClass}">
                        <td>
                            ${product.image_url ?
                                `<img src="${product.image_url}" alt="${product.name}" class="product-image-small">` :
                                `<div class="text-center"><i class="fas fa-box text-muted"></i></div>`
                            }
                        </td>
                        <td>
                            <input type="hidden" name="product_ids[]" value="${product.id}">
                            <input type="hidden" name="item_ids[]" value="">
                            <strong>${product.code}</strong> - ${product.name} ${promotionBadge}
                            <div class="small text-muted">المخزون المتاح: ${product.stock}</div>
                            ${priceDisplay}
                        </td>
                        <td>
                            <input type="number" class="form-control form-control-sm quantity-input" name="quantities[]" value="1" min="1" max="${product.stock}" required data-stock="${product.stock}" data-original="0">
                        </td>
                        <td>
                            <div class="input-group input-group-sm">
                                <input type="number" class="form-control form-control-sm unit-price-input" name="unit_prices[]" value="${product.price}" step="0.01" min="0" required>
                                <span class="input-group-text">د.م</span>
                            </div>
                        </td>
                        <td>
                            <span class="subtotal">${product.price}</span> د.م
                        </td>
                        <td>
                            <button type="button" class="btn btn-sm btn-danger remove-product">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;

                $('#productsTableBody').append(newRow);
                updateSummary();
            }
        }

        // Barcode Scanner Functions
        var initTimeout;

        function startScanner() {
            // إظهار حاوي الماسح
            $('#scanner-container').html(`
                <div id="scanner-video-container" class="d-none">
                    <video id="scanner-video" autoplay muted playsinline></video>
                    <div class="barcode-scanner-border"></div>
                </div>
                <div class="text-center mt-3">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">{% trans "جاري تحميل الكاميرا..." %}</span>
                    </div>
                    <p class="mt-2">{% trans "جاري تهيئة الكاميرا..." %}</p>
                </div>
                <div class="mt-3">
                    <label for="manual-barcode-input" class="form-label">{% trans "أو أدخل الباركود يدوياً:" %}</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="manual-barcode-input" placeholder="{% trans 'أدخل رقم الباركود...' %}">
                        <button class="btn btn-primary" type="button" id="manual-barcode-btn">{% trans "إدخال" %}</button>
                    </div>
                </div>
            `);

            // التحقق من وجود مكتبة Quagga
            if (typeof Quagga === 'undefined') {
                showScannerError('{% trans "لم يتم تحميل مكتبة ماسح الباركود بشكل صحيح" %}');
                return;
            }

            // إضافة مؤقت للتوقف بعد 10 ثوانٍ إذا لم تتم تهيئة الكاميرا
            initTimeout = setTimeout(function() {
                if (Quagga) {
                    try {
                        Quagga.stop();
                    } catch (e) {
                        console.log("خطأ عند محاولة إيقاف Quagga:", e);
                    }
                }
                showScannerError('{% trans "انتهت مهلة تهيئة الكاميرا. يرجى المحاولة مرة أخرى أو استخدام الإدخال اليدوي." %}', true);
            }, 10000); // 10 ثوانٍ كحد أقصى للانتظار

            // تهيئة Quagga
            try {
                Quagga.init({
                    inputStream: {
                        name: "Live",
                        type: "LiveStream",
                        target: document.querySelector('#scanner-video'),
                        constraints: {
                            width: 640,
                            height: 480,
                            facingMode: "environment"
                        }
                    },
                    locator: {
                        patchSize: "medium",
                        halfSample: true
                    },
                    numOfWorkers: 2,
                    decoder: {
                        readers: ["code_128_reader", "ean_reader", "ean_8_reader", "code_39_reader"]
                    },
                    locate: true
                }, function(err) {
                    if (err) {
                        console.error("خطأ في تهيئة ماسح الباركود:", err);
                        var errorMessage = '{% trans "لا يمكن الوصول إلى الكاميرا. يرجى التأكد من منح الإذن للموقع للوصول إلى الكاميرا." %}';

                        if (err.name === 'NotAllowedError') {
                            errorMessage = '{% trans "تم رفض الإذن للوصول إلى الكاميرا. يرجى السماح للموقع بالوصول إلى الكاميرا وإعادة المحاولة." %}';
                        } else if (err.name === 'NotFoundError') {
                            errorMessage = '{% trans "لم يتم العثور على كاميرا. يرجى التأكد من وجود كاميرا متصلة بالجهاز." %}';
                        }

                        showScannerError(errorMessage, true);
                        return;
                    }

                    try {
                        Quagga.start();
                        // إظهار عنصر الفيديو بعد التهيئة الناجحة
                        $('#scanner-video-container').removeClass('d-none');
                    } catch (error) {
                        console.error("خطأ في بدء ماسح الباركود:", error);
                        showScannerError('{% trans "حدث خطأ أثناء تشغيل ماسح الباركود" %}', true);
                    }
                });
            } catch (e) {
                console.error("خطأ غير متوقع:", e);
                showScannerError('{% trans "حدث خطأ غير متوقع أثناء تهيئة الماسح" %}', true);
            }

            // عند اكتشاف باركود
            Quagga.onDetected(function(result) {
                var code = result.codeResult.code;

                // إضافة تأثير مرئي عند نجاح المسح
                $('.barcode-scanner-border').addClass('barcode-scanned');

                // إيقاف الماسح مؤقتاً لمنع المسح المتكرر
                Quagga.stop();

                // البحث عن المنتج
                findProductByBarcode(code);

                // إغلاق النافذة بعد ثانية واحدة
                setTimeout(function() {
                    $('#scannerModal').modal('hide');
                }, 1000);
            });

            // مسح الحدود المؤقتة
            clearTimeout(initTimeout);
        }

        function showScannerError(message, showManualInput = false) {
            var content = `
                <div class="alert alert-warning text-center">
                    <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                    <p>${message}</p>
                </div>
            `;

            if (showManualInput) {
                content += `
                    <div class="mt-3">
                        <label for="manual-barcode-input" class="form-label">{% trans "أدخل الباركود يدوياً:" %}</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="manual-barcode-input" placeholder="{% trans 'أدخل رقم الباركود...' %}">
                            <button class="btn btn-primary" type="button" id="manual-barcode-btn">{% trans "إدخال" %}</button>
                        </div>
                    </div>
                `;
            }

            $('#scanner-container').html(content);
        }

        // بدء الماسح عند فتح النافذة
        $('#scannerModal').on('shown.bs.modal', function() {
            startScanner();
        });

        $('#scannerModal').on('hidden.bs.modal', function() {
            // إيقاف الكاميرا عند إغلاق النافذة
            if (Quagga) {
                try {
                    Quagga.stop();
                } catch (e) {
                    console.log("خطأ عند إيقاف الكاميرا:", e);
                }
            }

            // مسح المحتوى
            $('#scanner-container').empty();

            // مسح المؤقت إذا كان لا يزال نشطاً
            if (initTimeout) {
                clearTimeout(initTimeout);
            }
        });

        // معالج الإدخال اليدوي للباركود
        $(document).on('click', '#manual-barcode-btn', function() {
            var barcode = $('#manual-barcode-input').val().trim();
            if (barcode) {
                findProductByBarcode(barcode);
                $('#scannerModal').modal('hide');
            } else {
                alert('{% trans "يرجى إدخال رقم الباركود" %}');
            }
        });

        $(document).on('keypress', '#manual-barcode-input', function(e) {
            if (e.which === 13) { // Enter key
                $('#manual-barcode-btn').click();
            }
        });

        // Initialize summary on page load
        updateSummary();
    });
</script>
{% endblock %}
