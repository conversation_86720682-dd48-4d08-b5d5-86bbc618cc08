{% extends 'base.html' %}
{% load i18n %}
{% load purchase_tags %}

{% block title %}{% trans "إنشاء طلب شراء جديد" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .required-field::after {
        content: " *";
        color: red;
    }

    .form-section {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .form-section-title {
        border-bottom: 1px solid #dee2e6;
        padding-bottom: 10px;
        margin-bottom: 20px;
    }

    .product-row {
        transition: all 0.3s;
    }

    .product-row:hover {
        background-color: #f8f9fa;
    }

    .product-image-small {
        width: 40px;
        height: 40px;
        object-fit: contain;
        border-radius: 4px;
    }

    .summary-card {
        position: sticky;
        top: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0 text-gray-800">{% trans "إنشاء طلب شراء جديد" %}</h1>
    <a href="{% url 'purchases:index' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-1"></i> {% trans "العودة إلى المشتريات" %}
    </a>
</div>

{% if messages %}
    {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    {% endfor %}
{% endif %}

<form method="post" id="purchaseForm">
    {% csrf_token %}

    <div class="row">
        <div class="col-md-8">
            <!-- Basic Information Section -->
            <div class="form-section">
                <h5 class="form-section-title">{% trans "المعلومات الأساسية" %}</h5>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="supplier" class="form-label required-field">{% trans "المورد" %}</label>
                        <select class="form-select" id="supplier" name="supplier" required>
                            <option value="">{% trans "اختر المورد" %}</option>
                            {% for supplier in suppliers %}
                            <option value="{{ supplier.id }}">{{ supplier.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="date" class="form-label required-field">{% trans "تاريخ الطلب" %}</label>
                        <input type="date" class="form-control" id="date" name="date" value="{{ today|date:'Y-m-d' }}" required>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="expected_delivery_date" class="form-label">{% trans "تاريخ التسليم المتوقع" %}</label>
                        <input type="date" class="form-control" id="expected_delivery_date" name="expected_delivery_date">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="shipping_cost" class="form-label">{% trans "تكلفة الشحن" %}</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="shipping_cost" name="shipping_cost" value="0" inputmode="numeric" pattern="[0-9]+(\.[0-9]+)?">
                            <span class="input-group-text">ر.س</span>
                        </div>
                        <div class="form-text text-danger" id="shipping_cost_error" style="display: none;">{% trans "الرجاء إدخال رقم صحيح" %}</div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="notes" class="form-label">{% trans "ملاحظات" %}</label>
                    <textarea class="form-control" id="notes" name="notes" rows="2"></textarea>
                </div>
            </div>

            <!-- Products Section -->
            <div class="form-section">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="form-section-title mb-0">{% trans "المنتجات" %}</h5>
                    <a href="#" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addProductModal">
                        <i class="fas fa-plus me-1"></i> {% trans "إضافة منتج" %}
                    </a>
                </div>

                <div class="table-responsive">
                    <table class="table table-bordered" id="productsTable">
                        <thead>
                            <tr>
                                <th width="50px">{% trans "صورة" %}</th>
                                <th>{% trans "المنتج" %}</th>
                                <th width="100px">{% trans "الكمية" %}</th>
                                <th width="150px">{% trans "سعر الوحدة" %}</th>
                                <th width="150px">{% trans "المجموع" %}</th>
                                <th width="50px">{% trans "الإجراءات" %}</th>
                            </tr>
                        </thead>
                        <tbody id="productsTableBody">
                            {% if initial_items %}
                                {% for item in initial_items %}
                                <tr class="product-row">
                                    <td>
                                        {% if item.product.image %}
                                        <img src="{{ item.product.image.url }}" alt="{{ item.product.name }}" class="product-image-small">
                                        {% else %}
                                        <div class="text-center">
                                            <i class="fas fa-image text-muted"></i>
                                        </div>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <input type="hidden" name="product_ids[]" value="{{ item.product.id }}">
                                        <strong>{{ item.product.code }}</strong> - {{ item.product.name }}
                                    </td>
                                    <td>
                                        <input type="number" class="form-control form-control-sm quantity-input" name="quantities[]" value="{{ item.quantity }}" min="1" required>
                                    </td>
                                    <td>
                                        <div class="input-group input-group-sm">
                                            <input type="number" class="form-control form-control-sm unit-price-input" name="unit_prices[]" value="{{ item.unit_price }}" step="0.01" min="0" required>
                                            <span class="input-group-text">ر.س</span>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="subtotal">{{ item.quantity|mul:item.unit_price }}</span> ر.س
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-danger remove-product">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            {% else %}
                                <tr id="noProductsRow">
                                    <td colspan="6" class="text-center">{% trans "لم يتم إضافة منتجات بعد" %}</td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>

                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle me-1"></i>
                    {% trans "يمكنك إضافة المنتجات باستخدام زر 'إضافة منتج' أعلاه. تأكد من إضافة منتج واحد على الأقل قبل حفظ طلب الشراء." %}
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Summary Section -->
            <div class="card shadow summary-card">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "ملخص الطلب" %}</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>{% trans "المجموع الفرعي:" %}</span>
                            <span id="subtotalSummary">0.00</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>{% trans "ضريبة القيمة المضافة (15%):" %}</span>
                            <span id="taxSummary">0.00</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>{% trans "تكلفة الشحن:" %}</span>
                            <span id="shippingSummary">0.00</span>
                        </div>
                    </div>
                    <hr>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <strong>{% trans "المجموع الكلي:" %}</strong>
                            <strong id="totalSummary">0.00</strong>
                        </div>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary" id="saveBtn">
                            <i class="fas fa-save me-1"></i> {% trans "حفظ طلب الشراء" %}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

<!-- Add Product Modal -->
<div class="modal fade" id="addProductModal" tabindex="-1" aria-labelledby="addProductModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addProductModalLabel">{% trans "إضافة منتج" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="productSearch" class="form-label">{% trans "بحث عن منتج" %}</label>
                    <input type="text" class="form-control" id="productSearch" placeholder="{% trans 'اكتب اسم المنتج أو الكود...' %}">
                </div>

                <div class="table-responsive mt-3">
                    <table class="table table-bordered table-hover" id="productsSearchTable">
                        <thead>
                            <tr>
                                <th>{% trans "الكود" %}</th>
                                <th>{% trans "اسم المنتج" %}</th>
                                <th>{% trans "الفئة" %}</th>
                                <th>{% trans "الكمية المتوفرة" %}</th>
                                <th>{% trans "سعر الشراء" %}</th>
                                <th>{% trans "الإجراءات" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for product in products %}
                            <tr>
                                <td>{{ product.code }}</td>
                                <td>{{ product.name }}</td>
                                <td>{{ product.category.name }}</td>
                                <td>{{ product.quantity }}</td>
                                <td>{{ product.purchase_price }} ر.س</td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-primary select-product"
                                            data-id="{{ product.id }}"
                                            data-code="{{ product.code }}"
                                            data-name="{{ product.name }}"
                                            data-price="{{ product.purchase_price }}"
                                            data-image="{% if product.image %}{{ product.image.url }}{% endif %}">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إغلاق" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
    $(document).ready(function() {
        // Initialize DataTable for products search
        var searchTable = $('#productsSearchTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json"
            },
            "pageLength": 5
        });

        // Product search
        $('#productSearch').keyup(function() {
            searchTable.search($(this).val()).draw();
        });

        // Modal is now handled by data-bs-toggle and data-bs-target attributes

        // Select Product
        $(document).on('click', '.select-product', function() {
            var productId = $(this).data('id');
            var productCode = $(this).data('code');
            var productName = $(this).data('name');
            var productPrice = $(this).data('price');
            var productImage = $(this).data('image');

            // Remove "no products" row if exists
            $('#noProductsRow').remove();

            // Add product to table
            var newRow = `
                <tr class="product-row">
                    <td>
                        ${productImage ?
                            `<img src="${productImage}" alt="${productName}" class="product-image-small">` :
                            `<div class="text-center"><i class="fas fa-image text-muted"></i></div>`
                        }
                    </td>
                    <td>
                        <input type="hidden" name="product_ids[]" value="${productId}">
                        <strong>${productCode}</strong> - ${productName}
                    </td>
                    <td>
                        <input type="number" class="form-control form-control-sm quantity-input" name="quantities[]" value="1" min="1" required>
                    </td>
                    <td>
                        <div class="input-group input-group-sm">
                            <input type="number" class="form-control form-control-sm unit-price-input" name="unit_prices[]" value="${productPrice}" step="0.01" min="0" required>
                            <span class="input-group-text">ر.س</span>
                        </div>
                    </td>
                    <td>
                        <span class="subtotal">${productPrice}</span> ر.س
                    </td>
                    <td>
                        <button type="button" class="btn btn-sm btn-danger remove-product">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `;

            $('#productsTableBody').append(newRow);

            // Close modal
            $('#addProductModal').modal('hide');

            // Update summary
            updateSummary();
        });

        // Remove Product
        $(document).on('click', '.remove-product', function() {
            $(this).closest('tr').remove();

            // If no products left, add "no products" row
            if ($('#productsTableBody tr').length === 0) {
                $('#productsTableBody').html('<tr id="noProductsRow"><td colspan="6" class="text-center">لم يتم إضافة منتجات بعد</td></tr>');
            }

            // Update summary
            updateSummary();
        });

        // Update subtotal when quantity or unit price changes
        $(document).on('input', '.quantity-input, .unit-price-input', function() {
            var row = $(this).closest('tr');
            var quantity = parseFloat(row.find('.quantity-input').val()) || 0;
            var unitPrice = parseFloat(row.find('.unit-price-input').val()) || 0;
            var subtotal = quantity * unitPrice;

            row.find('.subtotal').text(subtotal.toFixed(2));

            // Update summary
            updateSummary();
        });

        // Update shipping cost with better validation
        $('#shipping_cost').on('input', function() {
            var value = $(this).val();
            var isValid = /^\d*\.?\d*$/.test(value);

            if (isValid) {
                $('#shipping_cost_error').hide();
                updateSummary();
            } else {
                $('#shipping_cost_error').show();
            }
        });

        // Make sure shipping cost updates properly on change
        $('#shipping_cost').change(function() {
            var value = $(this).val();
            if (value === '' || isNaN(parseFloat(value))) {
                $(this).val('0');
            }
            updateSummary();
        });

        // Update summary
        function updateSummary() {
            var subtotal = 0;

            // Calculate subtotal
            $('.subtotal').each(function() {
                subtotal += parseFloat($(this).text()) || 0;
            });

            // Calculate tax
            var taxRate = 15; // 15% VAT
            var tax = subtotal * (taxRate / 100);

            // Get shipping cost - make sure to handle empty or invalid values
            var shippingInput = $('#shipping_cost').val();
            var shipping = (shippingInput && !isNaN(parseFloat(shippingInput))) ? parseFloat(shippingInput) : 0;

            // Calculate total
            var total = subtotal + tax + shipping;

            // Update summary
            $('#subtotalSummary').text(subtotal.toFixed(2));
            $('#taxSummary').text(tax.toFixed(2));
            $('#shippingSummary').text(shipping.toFixed(2));
            $('#totalSummary').text(total.toFixed(2));
        }

        // Form validation
        $('#purchaseForm').submit(function(e) {
            if ($('#productsTableBody tr').not('#noProductsRow').length === 0) {
                e.preventDefault();
                alert('{% trans "يرجى إضافة منتج واحد على الأقل" %}');
                return false;
            }

            return true;
        });

        // Initialize summary
        updateSummary();
    });
</script>
{% endblock %}
