{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block styles %}
<style>
    .barcode-type-card {
        border-radius: 15px; /* زيادة استدارة الحواف */
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08); /* ظل أنعم وأعمق قليلاً */
        transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1); /* انتقال أكثر سلاسة */
        background-color: #fff; /* خلفية بيضاء للبطاقة */
    }
    .barcode-type-card:hover {
        transform: translateY(-8px); /* رفع البطاقة أكثر عند المرور */
        box-shadow: 0 12px 24px rgba(0, 0, 0, 0.12); /* ظل أوضح عند المرور */
    }
    .barcode-type-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px 25px; /* زيادة الحشوة */
        border-bottom: 1px solid #f0f0f0; /* خط فاصل أنعم */
        background-color: #f7f9fc; /* لون خلفية خفيف للرأس */
        border-top-left-radius: 15px; /* استدارة الحواف العلوية */
        border-top-right-radius: 15px;
    }
    .barcode-type-header h5 {
        font-weight: 600; /* خط أثقل لاسم النوع */
        color: #333; /* لون أغمق للاسم */
    }
    .barcode-type-body {
        padding: 25px; /* زيادة الحشوة */
    }
    .barcode-type-footer {
        padding: 20px 25px; /* زيادة الحشوة */
        background-color: #f7f9fc; /* لون خلفية خفيف للتذييل */
        border-top: 1px solid #f0f0f0; /* خط فاصل أنعم */
        border-bottom-left-radius: 15px; /* استدارة الحواف السفلية */
        border-bottom-right-radius: 15px;
    }
    .barcode-type-code {
        font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace; /* خط أفضل للرموز */
        background-color: #e9ecef; /* خلفية أغمق قليلاً للرمز */
        padding: 4px 8px; /* زيادة الحشوة */
        border-radius: 6px; /* زيادة استدارة الحواف */
        font-size: 0.95rem; /* حجم خط أكبر قليلاً */
        color: #495057; /* لون أغمق للرمز */
    }
    .status-badge {
        padding: 6px 12px; /* زيادة الحشوة */
        border-radius: 20px; /* استدارة أكبر */
        font-size: 0.75rem; /* حجم خط أصغر قليلاً للحالة */
        font-weight: 700; /* خط أثقل للحالة */
        text-transform: uppercase; /* حروف كبيرة للحالة */
    }
    .status-active {
        background-color: rgba(40, 167, 69, 0.1); /* خلفية شفافة أكثر */
        color: #28a745; /* لون أخضر أوضح */
    }
    .status-inactive {
        background-color: rgba(220, 53, 69, 0.1); /* خلفية شفافة أكثر */
        color: #dc3545; /* لون أحمر أوضح */
    }
    .barcode-sample {
        text-align: center;
        margin-top: 15px; /* زيادة الهامش العلوي */
        padding: 15px; /* زيادة الحشوة */
        border: 1px solid #e0e0e0; /* إطار أنعم */
        border-radius: 8px; /* زيادة استدارة الحواف */
        background-color: #fdfdfd; /* خلفية فاتحة جدًا للعينة */
    }
    .barcode-sample img {
        max-width: 100%;
        height: auto;
        display: block; /* إزالة أي مسافات إضافية */
        margin: 0 auto; /* توسيط الصورة */
    }
    .page-title {
        font-weight: 600; /* خط أثقل لعنوان الصفحة */
    }
    .card-title {
        font-weight: 500; /* خط متوسط لعنوان البطاقة الرئيسية */
    }
    .btn-primary {
        background-color: #007bff; /* لون أزرق أساسي مميز */
        border-color: #007bff;
    }
    .btn-primary:hover {
        background-color: #0056b3; /* لون أغمق عند المرور */
        border-color: #0056b3;
    }
    .btn-light {
        background-color: #f8f9fa; /* لون فاتح للزر الثانوي */
        border-color: #ced4da;
        color: #212529;
    }
    .btn-light:hover {
        background-color: #e2e6ea;
        border-color: #dae0e5;
    }
    /* تحسين مظهر حقول الوصف */
    .barcode-type-body strong {
        color: #555; /* لون أغمق قليلاً للعناوين الفرعية */
    }
    .barcode-type-body p {
        color: #666; /* لون رمادي داكن للنصوص */
        line-height: 1.6; /* تحسين قابلية القراءة */
    }
    /* تحسين مظهر رسالة عدم وجود أنواع باركود */
    .empty-state-card .card-body {
        padding: 40px !important; /* زيادة الحشوة */
    }
    .empty-state-card img {
        opacity: 0.7; /* جعل الصورة أقل بروزًا */
    }
    .empty-state-card h4 {
        color: #555; /* لون أغمق للعنوان */
        margin-top: 20px; /* زيادة الهامش العلوي */
    }
    .empty-state-card p {
        color: #777; /* لون رمادي متوسط للوصف */
        max-width: 400px; /* تحديد عرض النص */
        margin-left: auto;
        margin-right: auto;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="/">{% trans "لوحة التحكم" %}</a></li>
                        <li class="breadcrumb-item"><a href="/inventory/">{% trans "المخزون" %}</a></li>
                        <li class="breadcrumb-item active">{% trans "أنواع الباركود" %}</li>
                    </ol>
                </div>
                <h4 class="page-title">{% trans "أنواع الباركود" %}</h4>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h5 class="card-title">{% trans "إدارة أنواع الباركود" %}</h5>
                            <p class="text-muted">{% trans "يمكنك إضافة وتعديل وحذف أنواع الباركود المدعومة في النظام" %}</p>
                        </div>
                        <div class="col-md-6 text-md-end">
                            <a href="/inventory/barcode/types/add/" class="btn btn-primary">
                                <i class="mdi mdi-plus-circle me-1"></i> {% trans "إضافة نوع باركود جديد" %}
                            </a>
                            <a href="/inventory/barcode/settings/" class="btn btn-light ms-2">
                                <i class="mdi mdi-cog me-1"></i> {% trans "إعدادات الباركود" %}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        {% if barcode_types %}
            {% for barcode_type in barcode_types %}
                <div class="col-md-4 mb-4">
                    <div class="card barcode-type-card">
                        <div class="barcode-type-header">
                            <h5 class="mb-0">{{ barcode_type.name }}</h5>
                            <span class="status-badge {% if barcode_type.is_active %}status-active{% else %}status-inactive{% endif %}">
                                {% if barcode_type.is_active %}
                                    {% trans "نشط" %}
                                {% else %}
                                    {% trans "غير نشط" %}
                                {% endif %}
                            </span>
                        </div>
                        <div class="barcode-type-body">
                            <div class="mb-3">
                                <strong>{% trans "الرمز:" %}</strong>
                                <span class="barcode-type-code">{{ barcode_type.code }}</span>
                            </div>
                            
                            {% if barcode_type.description %}
                                <div class="mb-3">
                                    <strong>{% trans "الوصف:" %}</strong>
                                    <p class="mb-0 mt-1">{{ barcode_type.description }}</p>
                                </div>
                            {% endif %}
                            
                            <div class="barcode-sample">
                                <img src="https://barcode.tec-it.com/barcode.ashx?data=Example-{{ barcode_type.code }}&code=Code128&multiplebarcodes=false&translate-esc=false&unit=Fit&dpi=96&imagetype=Gif&rotation=0&color=%23000000&bgcolor=%23ffffff&codepage=Default&qunit=Mm&quiet=0" alt="{{ barcode_type.name }} Sample">
                                <div class="mt-2 small text-muted">{% trans "نموذج باركود" %}</div>
                            </div>
                        </div>
                        <div class="barcode-type-footer">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <small class="text-muted">{% trans "تاريخ الإنشاء:" %} {{ barcode_type.created_at|date:"Y-m-d" }}</small>
                                </div>
                                <div>
                                    <a href="/inventory/barcode/types/edit/{{ barcode_type.id }}/" class="btn btn-sm btn-outline-primary me-1">
                                        <i class="mdi mdi-pencil"></i>
                                    </a>
                                    <a href="/inventory/barcode/types/delete/{{ barcode_type.id }}/" class="btn btn-sm btn-outline-danger delete-barcode-type" data-barcode-type-id="{{ barcode_type.id }}" data-barcode-type-name="{{ barcode_type.name }}">
                                        <i class="mdi mdi-delete"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        {% else %}
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center py-5">
                        <img src="{% static 'img/empty-box.svg' %}" alt="No Barcode Types" class="mb-3" style="width: 120px; height: auto;">
                        <h4>{% trans "لا توجد أنواع باركود" %}</h4>
                        <p class="text-muted">{% trans "لم يتم إضافة أي نوع باركود بعد. يمكنك إضافة أنواع الباركود التي تحتاجها في نظامك." %}</p>
                        <a href="/inventory/barcode/types/add/" class="btn btn-primary mt-2">
                            <i class="mdi mdi-plus-circle me-1"></i> {% trans "إضافة نوع باركود جديد" %}
                        </a>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
</div>

<!-- Modal حذف نوع الباركود -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">{% trans "تأكيد الحذف" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>{% trans "هل أنت متأكد من رغبتك في حذف نوع الباركود" %} <strong id="barcode-type-name"></strong>؟</p>
                <p class="text-danger">{% trans "تحذير: لا يمكن حذف نوع الباركود إذا كان مرتبطًا بباركودات موجودة." %}</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                <a href="#" id="confirm-delete" class="btn btn-danger">{% trans "حذف" %}</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // تفعيل مودال حذف نوع الباركود
        $('.delete-barcode-type').click(function(e) {
            e.preventDefault();
            const barcodeTypeId = $(this).data('barcode-type-id');
            const barcodeTypeName = $(this).data('barcode-type-name');
            
            $('#barcode-type-name').text(barcodeTypeName);
            $('#confirm-delete').attr('href', `/inventory/barcode/types/delete/${barcodeTypeId}/`);
            
            $('#deleteModal').modal('show');
        });
    });
</script>
{% endblock %}