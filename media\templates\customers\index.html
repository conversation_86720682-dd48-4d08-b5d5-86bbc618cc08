{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "إدارة العملاء" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<!-- DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
<style>
    .stats-card {
        transition: all 0.3s;
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .customer-row {
        transition: all 0.2s;
    }

    .customer-row:hover {
        background-color: rgba(0, 123, 255, 0.05);
    }

    .badge-active {
        background-color: #28a745;
    }

    .badge-inactive {
        background-color: #dc3545;
    }

    .search-box {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .top-customers-card {
        height: 100%;
    }

    .action-buttons .btn {
        margin-right: 5px;
    }

    @media (max-width: 768px) {
        .action-buttons .btn {
            margin-bottom: 5px;
            width: 100%;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0 text-gray-800">{% trans "إدارة العملاء" %}</h1>
    <div>
        <a href="{% url 'customers:export_customers' %}" class="btn btn-outline-primary me-2">
            <i class="fas fa-file-export me-1"></i> {% trans "تصدير" %}
        </a>
        <a href="{% url 'customers:import_customers' %}" class="btn btn-outline-success me-2">
            <i class="fas fa-file-import me-1"></i> {% trans "استيراد" %}
        </a>
        <a href="{% url 'customers:add_customer' %}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i> {% trans "إضافة عميل جديد" %}
        </a>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3 mb-4">
        <div class="card border-right-primary shadow h-100 py-2 stats-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            {% trans "إجمالي العملاء" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_customers }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-4">
        <div class="card border-right-success shadow h-100 py-2 stats-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            {% trans "العملاء النشطين" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ active_customers }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-user-check fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-4">
        <div class="card border-right-danger shadow h-100 py-2 stats-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                            {% trans "العملاء غير النشطين" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ inactive_customers }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-user-times fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-4">
        <div class="card border-right-info shadow h-100 py-2 stats-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            {% trans "متوسط المشتريات" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {% if total_customers > 0 %}
                                {{ total_amount|default:0|floatformat:2 }} د.م
                            {% else %}
                                0.00 د.م
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <!-- بحث وتصفية -->
    <div class="col-md-8 mb-4">
        <div class="card shadow">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">{% trans "بحث وتصفية" %}</h6>
                <button class="btn btn-sm btn-outline-primary" type="button" data-bs-toggle="collapse" data-bs-target="#searchCollapse">
                    <i class="fas fa-filter me-1"></i> {% trans "عرض/إخفاء" %}
                </button>
            </div>
            <div class="card-body collapse show" id="searchCollapse">
                <form method="get" id="searchForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="search" class="form-label">{% trans "بحث" %}</label>
                            <input type="text" class="form-control" id="search" name="search" value="{{ search_form.search.value|default:'' }}" placeholder="{% trans 'اسم، رقم هاتف، أو بريد إلكتروني' %}">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="category" class="form-label">{% trans "الفئة" %}</label>
                            {{ search_form.category }}
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="is_active" class="form-label">{% trans "الحالة" %}</label>
                            {{ search_form.is_active }}
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="has_purchases" class="form-label">{% trans "المشتريات" %}</label>
                            {{ search_form.has_purchases }}
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="city" class="form-label">{% trans "المدينة" %}</label>
                            {{ search_form.city }}
                        </div>
                    </div>
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i> {% trans "بحث" %}
                        </button>
                        <a href="{% url 'customers:index' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-redo me-1"></i> {% trans "إعادة تعيين" %}
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- العملاء الأكثر شراءً -->
    <div class="col-md-4 mb-4">
        <div class="card shadow top-customers-card">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">{% trans "العملاء الأكثر شراءً" %}</h6>
            </div>
            <div class="card-body">
                {% if top_customers %}
                    <div class="list-group">
                        {% for customer in top_customers %}
                            <a href="{% url 'customers:view_customer' customer_id=customer.id %}" class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">{{ customer.name }}</h6>
                                    <small>{{ customer.total_spent|floatformat:2 }} د.م</small>
                                </div>
                                <small class="text-muted">{{ customer.purchase_count }} {% trans "عملية شراء" %}</small>
                            </a>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                        <p>{% trans "لا توجد مشتريات بعد" %}</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- قائمة العملاء -->
<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex justify-content-between align-items-center">
        <h6 class="m-0 font-weight-bold text-primary">{% trans "قائمة العملاء" %}</h6>
        <div class="dropdown">
            <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" id="sortDropdown" data-bs-toggle="dropdown">
                <i class="fas fa-sort me-1"></i> {% trans "ترتيب حسب" %}
            </button>
            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="sortDropdown">
                <li>
                    <a class="dropdown-item {% if sort_by == 'name' and sort_order == 'asc' %}active{% endif %}"
                       href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'sort_by' and key != 'sort_order' and key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}sort_by=name&sort_order=asc">
                        <i class="fas fa-sort-alpha-down me-1"></i> {% trans "الاسم (تصاعدي)" %}
                    </a>
                </li>
                <li>
                    <a class="dropdown-item {% if sort_by == 'name' and sort_order == 'desc' %}active{% endif %}"
                       href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'sort_by' and key != 'sort_order' and key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}sort_by=name&sort_order=desc">
                        <i class="fas fa-sort-alpha-up me-1"></i> {% trans "الاسم (تنازلي)" %}
                    </a>
                </li>
                <li>
                    <a class="dropdown-item {% if sort_by == 'created_at' and sort_order == 'desc' %}active{% endif %}"
                       href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'sort_by' and key != 'sort_order' and key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}sort_by=created_at&sort_order=desc">
                        <i class="fas fa-calendar-alt me-1"></i> {% trans "تاريخ الإضافة (الأحدث)" %}
                    </a>
                </li>
                <li>
                    <a class="dropdown-item {% if sort_by == 'created_at' and sort_order == 'asc' %}active{% endif %}"
                       href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'sort_by' and key != 'sort_order' and key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}sort_by=created_at&sort_order=asc">
                        <i class="fas fa-calendar-alt me-1"></i> {% trans "تاريخ الإضافة (الأقدم)" %}
                    </a>
                </li>
                <li>
                    <a class="dropdown-item {% if sort_by == 'last_purchase' and sort_order == 'desc' %}active{% endif %}"
                       href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'sort_by' and key != 'sort_order' and key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}sort_by=last_purchase&sort_order=desc">
                        <i class="fas fa-shopping-cart me-1"></i> {% trans "آخر شراء (الأحدث)" %}
                    </a>
                </li>
            </ul>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive" id="customerTableContainer">
            {% include 'customers/partials/customer_list.html' %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

<script>
    $(document).ready(function() {
        // تهيئة DataTable
        var table = $('#customersTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json",
                "lengthMenu": "إظهار _MENU_ من العناصر",
                "info": "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل"
            },
            "pageLength": 10,
            "lengthMenu": [[5, 10, 15, 20, 25, 50, -1], [5, 10, 15, 20, 25, 50, "الكل"]],
            "ordering": false, // تعطيل الترتيب لأننا نستخدم ترتيب الخادم
            "searching": false, // تعطيل البحث لأننا نستخدم نموذج البحث الخاص بنا
            "info": false, // إخفاء معلومات الصفحات لأننا نستخدم ترقيم الصفحات الخاص بنا
            "paging": false // تعطيل تقسيم الصفحات لأننا نستخدم ترقيم الصفحات الخاص بنا
        });

        // تحديث الجدول عند تغيير الفلتر
        $('#searchForm').on('submit', function(e) {
            e.preventDefault();

            var formData = $(this).serialize();
            var url = "{% url 'customers:index' %}?" + formData;

            // تحديث عنوان URL
            history.pushState(null, '', url);

            // تحديث الجدول باستخدام AJAX
            $.ajax({
                url: url,
                type: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                success: function(data) {
                    $('#customerTableContainer').html(data.html);
                }
            });
        });

        // حذف العميل
        $(document).on('click', '.delete-customer', function(e) {
            e.preventDefault();

            var customerId = $(this).data('id');
            var customerName = $(this).data('name');

            if (confirm("{% trans 'هل أنت متأكد من حذف العميل: ' %}" + customerName + "؟")) {
                $.ajax({
                    url: "{% url 'customers:index' %}" + "delete/" + customerId + "/",
                    type: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRFToken': '{{ csrf_token }}'
                    },
                    success: function(data) {
                        alert(data.message);
                        location.reload();
                    },
                    error: function(xhr) {
                        var response = JSON.parse(xhr.responseText);
                        alert(response.message);
                    }
                });
            }
        });
    });
</script>
{% endblock %}
