{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "إنشاء بيع جديد" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<!-- DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
<!-- Google Fonts -->
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
<!-- Animate.css -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
<style>
    :root {
        --primary-color: #4f46e5;
        --primary-dark: #3730a3;
        --secondary-color: #f8fafc;
        --accent-color: #06b6d4;
        --success-color: #10b981;
        --warning-color: #f59e0b;
        --danger-color: #ef4444;
        --text-primary: #1e293b;
        --text-secondary: #64748b;
        --border-color: #e2e8f0;
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
        --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    }

    * {
        font-family: 'Cairo', sans-serif;
    }

    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
    }

    .main-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        box-shadow: var(--shadow-xl);
        margin: 20px;
        padding: 30px;
        animation: fadeInUp 0.6s ease-out;
    }

    .page-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
        color: white;
        padding: 25px 30px;
        border-radius: 15px;
        margin-bottom: 30px;
        box-shadow: var(--shadow-lg);
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        pointer-events: none;
    }

    .page-title {
        font-size: 2rem;
        font-weight: 700;
        margin: 0;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .required-field::after {
        content: " *";
        color: var(--danger-color);
        font-weight: bold;
    }

    .form-section {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: var(--shadow-md);
        border: 1px solid var(--border-color);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .form-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 4px;
        background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    }

    .form-section:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .form-section-title {
        color: var(--text-primary);
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 2px solid var(--border-color);
        position: relative;
    }

    .form-section-title::before {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 50px;
        height: 2px;
        background: var(--primary-color);
    }

    .form-control, .form-select {
        border: 2px solid var(--border-color);
        border-radius: 10px;
        padding: 12px 15px;
        font-size: 0.95rem;
        transition: all 0.3s ease;
        background: white;
    }

    .form-control:focus, .form-select:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        transform: translateY(-1px);
    }

    .btn {
        border-radius: 10px;
        padding: 12px 20px;
        font-weight: 600;
        transition: all 0.3s ease;
        border: none;
        position: relative;
        overflow: hidden;
    }

    .btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .btn:hover::before {
        left: 100%;
    }

    .btn-primary {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        color: white;
        box-shadow: var(--shadow-md);
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .btn-add-product {
        background: linear-gradient(135deg, var(--success-color), #059669);
        color: white;
        box-shadow: var(--shadow-md);
    }

    .btn-add-product:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .product-row {
        transition: all 0.3s ease;
        border-radius: 10px;
    }

    .product-row:hover {
        background: linear-gradient(135deg, #f8fafc, #f1f5f9);
        transform: translateX(5px);
    }

    .product-image-small {
        width: 55px;
        height: 55px;
        object-fit: contain;
        border-radius: 10px;
        border: 2px solid var(--border-color);
        padding: 4px;
        background: white;
        transition: all 0.3s ease;
        box-shadow: var(--shadow-sm);
    }

    .product-image-small:hover {
        transform: scale(1.1) rotate(2deg);
        box-shadow: var(--shadow-md);
        z-index: 100;
    }

    /* تنسيقات ماسح الباركود */
    .barcode-scanner {
        position: relative;
        margin-bottom: 20px;
    }

    .barcode-scanner .scan-icon {
        position: absolute;
        top: 50%;
        left: 15px;
        transform: translateY(-50%);
        cursor: pointer;
        color: var(--primary-color);
        font-size: 1.2rem;
        transition: all 0.3s ease;
        z-index: 10;
    }

    .barcode-scanner .scan-icon:hover {
        color: var(--primary-dark);
        transform: translateY(-50%) scale(1.1);
    }

    .barcode-scanner .form-control {
        padding-left: 50px;
        background: linear-gradient(135deg, #f8fafc, white);
        border: 2px solid var(--border-color);
        border-radius: 15px;
        font-size: 1rem;
        height: 55px;
        transition: all 0.3s ease;
    }

    .barcode-scanner .form-control:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        background: white;
    }

    #scanner-container {
        width: 100%;
        height: 350px;
        overflow: hidden;
        position: relative;
        background: linear-gradient(135deg, #1e293b, #334155);
        border-radius: 15px;
        box-shadow: var(--shadow-lg);
    }

    #scanner-video-container {
        width: 100%;
        height: 100%;
        position: relative;
    }

    #scanner-video {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 15px;
    }

    /* حدود منطقة مسح الباركود */
    .barcode-scanner-border {
        position: absolute;
        top: 50%;
        left: 50%;
        width: 70%;
        height: 30%;
        transform: translate(-50%, -50%);
        border: 3px solid var(--accent-color);
        border-radius: 15px;
        box-shadow: 0 0 0 100vw rgba(0, 0, 0, 0.6);
        pointer-events: none;
        z-index: 10;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { box-shadow: 0 0 0 100vw rgba(0, 0, 0, 0.6), 0 0 0 0 var(--accent-color); }
        50% { box-shadow: 0 0 0 100vw rgba(0, 0, 0, 0.6), 0 0 0 10px rgba(6, 182, 212, 0.3); }
        100% { box-shadow: 0 0 0 100vw rgba(0, 0, 0, 0.6), 0 0 0 0 var(--accent-color); }
    }

    /* تأثير عند مسح الباركود */
    .barcode-scanned {
        animation: barcode-success 0.8s ease-out;
    }

    @keyframes barcode-success {
        0% {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 100vw rgba(0, 0, 0, 0.6);
        }
        50% {
            border-color: var(--success-color);
            box-shadow: 0 0 0 100vw rgba(16, 185, 129, 0.3);
            transform: translate(-50%, -50%) scale(1.1);
        }
        100% {
            border-color: var(--success-color);
            box-shadow: 0 0 0 100vw rgba(0, 0, 0, 0.6);
            transform: translate(-50%, -50%) scale(1);
        }
    }

    /* تنسيقات النوافذ المنبثقة */
    .modal-content {
        border: none;
        border-radius: 20px;
        overflow: hidden;
        box-shadow: var(--shadow-xl);
        backdrop-filter: blur(10px);
        background: rgba(255, 255, 255, 0.98);
    }

    .modal-header {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        color: white;
        padding: 20px 25px;
        border: none;
        position: relative;
    }

    .modal-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
        pointer-events: none;
    }

    .modal-title {
        font-weight: 700;
        font-size: 1.3rem;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .modal-body {
        padding: 25px;
        background: white;
    }

    .modal-footer {
        background: var(--secondary-color);
        border: none;
        padding: 20px 25px;
    }

    /* تنسيقات نافذة إضافة المنتج */
    #addProductModal .search-container {
        position: relative;
        margin-bottom: 25px;
    }

    #addProductModal .input-group {
        border-radius: 15px;
        overflow: hidden;
        box-shadow: var(--shadow-md);
    }

    #addProductModal .input-group-text {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        color: white;
        border: none;
        padding: 15px 20px;
    }

    #addProductModal .product-image-tiny {
        width: 45px;
        height: 45px;
        object-fit: contain;
        background: var(--secondary-color);
        border-radius: 8px;
        transition: all 0.3s ease;
        border: 2px solid var(--border-color);
    }

    #addProductModal .product-item {
        transition: all 0.3s ease;
        border-radius: 10px;
        margin-bottom: 5px;
    }

    #addProductModal .product-item:hover {
        background: linear-gradient(135deg, #f8fafc, #f1f5f9);
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }

    #addProductModal .has-promotion {
        background: linear-gradient(135deg, rgba(16, 185, 129, 0.05), rgba(5, 150, 105, 0.05));
        border-left: 4px solid var(--success-color);
    }

    #addProductModal .filter-btn {
        border-radius: 25px;
        padding: 8px 20px;
        font-weight: 600;
        transition: all 0.3s ease;
        border: 2px solid var(--border-color);
        background: white;
        color: var(--text-secondary);
    }

    #addProductModal .filter-btn:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
        border-color: var(--primary-color);
        color: var(--primary-color);
    }

    #addProductModal .filter-btn.active {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        color: white;
        border-color: var(--primary-color);
        box-shadow: var(--shadow-md);
    }

    #addProductModal .price-container {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
    }

    #addProductModal .discount-badge {
        margin-top: 5px;
    }

    #addProductModal .product-table-container {
        max-height: 450px;
        overflow-y: auto;
        border-radius: 15px;
        border: 2px solid var(--border-color);
        background: white;
        box-shadow: var(--shadow-sm);
    }

    #addProductModal .select-product {
        transition: all 0.3s ease;
        border-radius: 20px;
        padding: 8px 16px;
        font-weight: 600;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        border: none;
        color: white;
    }

    #addProductModal .select-product:hover:not([disabled]) {
        transform: scale(1.05) translateY(-2px);
        box-shadow: var(--shadow-md);
    }

    #addProductModal .select-product:disabled {
        background: var(--text-secondary);
        opacity: 0.6;
        cursor: not-allowed;
    }

    /* بطاقة الملخص */
    .summary-card {
        position: sticky;
        top: 20px;
        background: white;
        border-radius: 20px;
        box-shadow: var(--shadow-xl);
        border: none;
        overflow: hidden;
        animation: slideInRight 0.6s ease-out;
    }

    .summary-card .card-header {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        color: white;
        padding: 20px 25px;
        border: none;
        position: relative;
    }

    .summary-card .card-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="lines" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 0,10 l 10,-10 M -2.5,2.5 l 5,-5 M 7.5,12.5 l 5,-5" stroke="white" stroke-width="0.5" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23lines)"/></svg>');
        pointer-events: none;
    }

    .summary-card .card-header h6 {
        font-weight: 700;
        font-size: 1.1rem;
        margin: 0;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .summary-card .card-body {
        padding: 25px;
        background: white;
    }

    .summary-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid var(--border-color);
        transition: all 0.3s ease;
    }

    .summary-item:last-child {
        border-bottom: none;
        font-weight: 700;
        font-size: 1.1rem;
        color: var(--primary-color);
        background: linear-gradient(135deg, rgba(79, 70, 229, 0.05), rgba(55, 48, 163, 0.05));
        margin: 15px -25px -25px -25px;
        padding: 20px 25px;
    }

    .summary-item:hover {
        background: var(--secondary-color);
        padding-left: 10px;
        padding-right: 10px;
        border-radius: 8px;
    }

    /* تنسيقات الجداول */
    .table {
        border-radius: 15px;
        overflow: hidden;
        box-shadow: var(--shadow-md);
        border: none;
        background: white;
    }

    .table thead th {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        color: white;
        border: none;
        padding: 15px 12px;
        font-weight: 600;
        text-align: center;
        position: relative;
    }

    .table thead th::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="white" stroke-width="0.5" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        pointer-events: none;
    }

    .table tbody td {
        padding: 15px 12px;
        border: none;
        border-bottom: 1px solid var(--border-color);
        vertical-align: middle;
        text-align: center;
    }

    .table tbody tr {
        transition: all 0.3s ease;
    }

    .table tbody tr:hover {
        background: linear-gradient(135deg, #f8fafc, #f1f5f9);
        transform: scale(1.01);
        box-shadow: var(--shadow-sm);
    }

    #scannerModal .modal-body {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 30px;
    }

    #scannerModal video {
        width: 100%;
        max-width: 400px;
        border-radius: 15px;
        margin-bottom: 20px;
        box-shadow: var(--shadow-lg);
    }

    /* تنسيقات DataTables */
    .dataTables_wrapper {
        padding: 20px;
        background: white;
        border-radius: 15px;
        box-shadow: var(--shadow-md);
        margin-bottom: 20px;
    }

    .dataTables_length {
        margin-bottom: 20px;
        position: relative;
        display: inline-block;
    }

    .dataTables_length label {
        display: flex;
        align-items: center;
        font-weight: 600;
        font-size: 0.95rem;
        color: var(--text-primary);
        position: relative;
        padding: 12px 20px;
        background: linear-gradient(135deg, var(--secondary-color), white);
        border-radius: 12px;
        box-shadow: var(--shadow-sm);
        transition: all 0.3s ease;
        border: 2px solid var(--border-color);
    }

    .dataTables_length label:hover {
        background: linear-gradient(135deg, #e2e8f0, var(--secondary-color));
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }

    .dataTables_length select {
        padding: 8px 35px 8px 15px;
        margin: 0 10px;
        font-size: 0.95rem;
        font-weight: 600;
        line-height: 1.5;
        color: var(--text-primary);
        background: white;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%234f46e5' class='bi bi-chevron-down' viewBox='0 0 16 16'%3E%3Cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: right 12px center;
        background-size: 16px 12px;
        border: 2px solid var(--border-color);
        border-radius: 10px;
        box-shadow: var(--shadow-sm);
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        transition: all 0.3s ease;
    }

    .dataTables_length select:focus {
        border-color: var(--primary-color);
        outline: 0;
        box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
    }

    .dataTables_length select:hover {
        border-color: var(--primary-color);
        cursor: pointer;
        transform: translateY(-1px);
        box-shadow: var(--shadow-md);
    }

    .dataTables_length::before {
        content: '\f0b0';
        font-family: 'Font Awesome 5 Free';
        font-weight: 900;
        margin-right: 8px;
        color: var(--primary-color);
        font-size: 1rem;
    }

    .dataTables_filter {
        margin-bottom: 20px;
    }

    .dataTables_filter input {
        border: 2px solid var(--border-color);
        border-radius: 10px;
        padding: 10px 15px;
        font-size: 0.95rem;
        transition: all 0.3s ease;
        background: white;
    }

    .dataTables_filter input:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
    }

    .dataTables_info {
        color: var(--text-secondary);
        font-weight: 500;
    }

    .dataTables_paginate .paginate_button {
        padding: 8px 12px;
        margin: 0 2px;
        border-radius: 8px;
        border: 2px solid var(--border-color);
        background: white;
        color: var(--text-primary);
        transition: all 0.3s ease;
        font-weight: 600;
    }

    .dataTables_paginate .paginate_button:hover {
        background: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }

    .dataTables_paginate .paginate_button.current {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        color: white;
        border-color: var(--primary-color);
        box-shadow: var(--shadow-md);
    }

    /* الرسوم المتحركة */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(30px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    @keyframes bounceIn {
        0% {
            opacity: 0;
            transform: scale(0.3);
        }
        50% {
            opacity: 1;
            transform: scale(1.05);
        }
        70% {
            transform: scale(0.9);
        }
        100% {
            opacity: 1;
            transform: scale(1);
        }
    }

    .animate-bounce-in {
        animation: bounceIn 0.6s ease-out;
    }

    /* تحسينات إضافية */
    .badge {
        border-radius: 20px;
        padding: 6px 12px;
        font-weight: 600;
        font-size: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .alert {
        border-radius: 15px;
        border: none;
        padding: 20px;
        box-shadow: var(--shadow-md);
        margin-bottom: 20px;
    }

    .alert-info {
        background: linear-gradient(135deg, rgba(6, 182, 212, 0.1), rgba(14, 165, 233, 0.1));
        color: var(--accent-color);
        border-left: 4px solid var(--accent-color);
    }

    .alert-success {
        background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(5, 150, 105, 0.1));
        color: var(--success-color);
        border-left: 4px solid var(--success-color);
    }

    .alert-danger {
        background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 38, 0.1));
        color: var(--danger-color);
        border-left: 4px solid var(--danger-color);
    }

    /* التصميم المتجاوب */
    @media (max-width: 1199.98px) {
        .main-container {
            margin: 10px;
            padding: 20px;
        }

        .page-header {
            padding: 20px 25px;
        }

        .form-section {
            padding: 20px;
        }
    }

    @media (max-width: 991.98px) {
        .summary-card {
            position: relative;
            top: 0;
            margin-top: 30px;
        }

        .page-title {
            font-size: 1.75rem;
        }
    }

    @media (max-width: 767.98px) {
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .main-container {
            margin: 5px;
            padding: 15px;
            border-radius: 15px;
        }

        .page-header {
            padding: 15px 20px;
            margin-bottom: 20px;
        }

        .page-title {
            font-size: 1.5rem;
        }

        .form-section {
            padding: 15px;
            margin-bottom: 15px;
        }

        .dataTables_length {
            width: 100%;
            margin-bottom: 15px;
            text-align: center;
        }

        .dataTables_length label {
            justify-content: center;
            width: 100%;
            padding: 10px 15px;
        }

        .dataTables_length select {
            max-width: 120px;
        }

        .summary-card {
            margin-top: 20px;
        }

        .fixed-bottom-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            padding: 15px;
            box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            border-top: 1px solid var(--border-color);
        }

        #productsTable {
            font-size: 0.85rem;
        }

        #productsTable th:nth-child(1),
        #productsTable td:nth-child(1) {
            width: 35px;
            padding: 8px 4px;
        }

        #productsTable th:nth-child(6),
        #productsTable td:nth-child(6) {
            width: 35px;
            padding: 8px 4px;
        }

        .product-image-small {
            width: 35px;
            height: 35px;
        }

        .btn-sm {
            padding: 8px 12px;
            font-size: 0.85rem;
        }

        main {
            padding-bottom: 80px;
        }

        .modal-dialog {
            margin: 10px;
        }

        .modal-content {
            border-radius: 15px;
        }

        .barcode-scanner .form-control {
            height: 50px;
            font-size: 0.9rem;
        }
    }

    @media (max-width: 575.98px) {
        .main-container {
            margin: 0;
            border-radius: 0;
        }

        .btn {
            padding: 10px 15px;
            font-size: 0.9rem;
        }

        .form-control, .form-select {
            padding: 10px 12px;
            font-size: 0.9rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="main-container animate__animated animate__fadeInUp">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="page-title mb-2">
                    <i class="fas fa-cash-register me-3"></i>
                    {% trans "إنشاء عملية بيع جديدة" %}
                </h1>
                <p class="mb-0 opacity-75">{% trans "قم بإضافة المنتجات وتحديد تفاصيل البيع" %}</p>
            </div>
            <a href="{% url 'sales:index' %}" class="btn btn-light btn-lg">
                <i class="fas fa-arrow-right me-2"></i> {% trans "العودة إلى المبيعات" %}
            </a>
        </div>
    </div>

    <!-- رسائل التنبيه -->
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show animate__animated animate__bounceIn" role="alert">
                <i class="fas fa-info-circle me-2"></i>
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

<form method="post" id="saleForm">
    {% csrf_token %}

    <div class="row">
        <div class="col-lg-8">
            <!-- قسم المعلومات الأساسية -->
            <div class="form-section animate__animated animate__fadeInLeft">
                <h5 class="form-section-title">
                    <i class="fas fa-info-circle me-2 text-primary"></i>
                    {% trans "المعلومات الأساسية" %}
                </h5>
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <label for="customer" class="form-label required-field">
                            <i class="fas fa-user me-2"></i>{% trans "العميل" %}
                        </label>
                        <select class="form-select" id="customer" name="customer" required>
                            <option value="">{% trans "اختر العميل..." %}</option>
                            {% for customer in customers %}
                            <option value="{{ customer.id }}">{{ customer.name }}</option>
                            {% endfor %}
                        </select>
                        <div class="form-text mt-2">
                            <a href="#" class="text-decoration-none text-primary fw-bold" data-bs-toggle="modal" data-bs-target="#quickAddCustomerModal">
                                <i class="fas fa-plus-circle me-1"></i> {% trans "إضافة عميل جديد" %}
                            </a>
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <label for="date" class="form-label required-field">
                            <i class="fas fa-calendar-alt me-2"></i>{% trans "تاريخ البيع" %}
                        </label>
                        <input type="date" class="form-control" id="date" name="date" value="{{ today|date:'Y-m-d' }}" required>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-4">
                        <label for="payment_method" class="form-label required-field">
                            <i class="fas fa-credit-card me-2"></i>{% trans "طريقة الدفع" %}
                        </label>
                        <select class="form-select" id="payment_method" name="payment_method" required>
                            <option value="cash">💰 {% trans "نقدي" %}</option>
                            <option value="card">💳 {% trans "بطاقة ائتمان" %}</option>
                            <option value="transfer">🏦 {% trans "تحويل بنكي" %}</option>
                            <option value="check">📄 {% trans "شيك" %}</option>
                            <option value="credit">📋 {% trans "آجل" %}</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-4">
                        <label for="status" class="form-label required-field">
                            <i class="fas fa-flag me-2"></i>{% trans "الحالة" %}
                        </label>
                        <select class="form-select" id="status" name="status" required>
                            <option value="completed">✅ {% trans "مكتمل" %}</option>
                            <option value="pending">⏳ {% trans "معلق" %}</option>
                        </select>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="notes" class="form-label">
                        <i class="fas fa-sticky-note me-2"></i>{% trans "ملاحظات" %}
                    </label>
                    <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="{% trans 'أضف أي ملاحظات إضافية هنا...' %}"></textarea>
                </div>
            </div>

            <!-- قسم المنتجات -->
            <div class="form-section animate__animated animate__fadeInLeft animate__delay-1s">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h5 class="form-section-title mb-0">
                        <i class="fas fa-shopping-cart me-2 text-primary"></i>
                        {% trans "المنتجات" %}
                    </h5>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-add-product" data-bs-toggle="modal" data-bs-target="#addProductModal">
                            <i class="fas fa-plus me-2"></i> {% trans "إضافة منتج" %}
                        </button>
                        <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#scannerModal">
                            <i class="fas fa-barcode me-2"></i> {% trans "مسح الباركود" %}
                        </button>
                        <button type="button" class="btn btn-danger" id="resetCartBtn">
                            <i class="fas fa-trash me-2"></i> {% trans "تفريغ السلة" %}
                        </button>
                    </div>
                </div>

                <!-- شريط البحث بالباركود -->
                <div class="barcode-scanner mb-4">
                    <input type="text" class="form-control" id="barcodeInput"
                           placeholder="{% trans 'أدخل الباركود أو اضغط على أيقونة المسح للبحث السريع...' %}">
                    <span class="scan-icon" data-bs-toggle="modal" data-bs-target="#scannerModal" title="{% trans 'مسح الباركود' %}">
                        <i class="fas fa-barcode"></i>
                    </span>
                </div>

                <!-- جدول المنتجات -->
                <div class="table-responsive">
                    <table class="table" id="productsTable">
                        <thead>
                            <tr>
                                <th width="60px">
                                    <i class="fas fa-image me-1"></i>{% trans "صورة" %}
                                </th>
                                <th>
                                    <i class="fas fa-box me-1"></i>{% trans "المنتج" %}
                                </th>
                                <th width="120px">
                                    <i class="fas fa-sort-numeric-up me-1"></i>{% trans "الكمية" %}
                                </th>
                                <th width="150px">
                                    <i class="fas fa-tag me-1"></i>{% trans "سعر الوحدة" %}
                                </th>
                                <th width="150px">
                                    <i class="fas fa-calculator me-1"></i>{% trans "المجموع" %}
                                </th>
                                <th width="80px">
                                    <i class="fas fa-cogs me-1"></i>{% trans "الإجراءات" %}
                                </th>
                            </tr>
                        </thead>
                        <tbody id="productsTableBody">
                            <tr id="noProductsRow">
                                <td colspan="6" class="text-center py-5">
                                    <div class="text-muted">
                                        <i class="fas fa-shopping-cart fa-3x mb-3 opacity-50"></i>
                                        <h5>{% trans "لم يتم إضافة منتجات بعد" %}</h5>
                                        <p class="mb-0">{% trans "ابدأ بإضافة المنتجات لإنشاء الفاتورة" %}</p>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- معلومات مساعدة -->
                <div class="alert alert-info">
                    <div class="d-flex align-items-start">
                        <i class="fas fa-lightbulb fa-2x me-3 text-warning"></i>
                        <div>
                            <h6 class="alert-heading mb-2">{% trans "نصائح سريعة:" %}</h6>
                            <ul class="mb-0 small">
                                <li>{% trans "استخدم زر 'إضافة منتج' للبحث واختيار المنتجات" %}</li>
                                <li>{% trans "امسح الباركود مباشرة لإضافة المنتج تلقائياً" %}</li>
                                <li>{% trans "يمكنك تعديل الكمية والسعر لكل منتج" %}</li>
                                <li>{% trans "تأكد من إضافة منتج واحد على الأقل قبل الحفظ" %}</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- زر الحفظ للجوال -->
                <div class="d-grid d-lg-none mt-4">
                    <button type="button" class="btn btn-primary btn-lg" id="mobileSaveBtn">
                        <i class="fas fa-save me-2"></i> {% trans "حفظ الفاتورة" %}
                    </button>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- بطاقة ملخص الفاتورة -->
            <div class="card summary-card animate__animated animate__fadeInRight">
                <div class="card-header">
                    <h6 class="m-0">
                        <i class="fas fa-receipt me-2"></i>
                        {% trans "ملخص الفاتورة" %}
                    </h6>
                </div>
                <div class="card-body">
                    <!-- المجموع الفرعي -->
                    <div class="summary-item">
                        <span>
                            <i class="fas fa-calculator me-2 text-muted"></i>
                            {% trans "المجموع الفرعي:" %}
                        </span>
                        <span class="fw-bold" id="subtotalSummary">0.00 د.م</span>
                    </div>

                    <!-- الضريبة -->
                    <div class="summary-item">
                        <span>
                            <i class="fas fa-percentage me-2 text-muted"></i>
                            {% trans "ضريبة القيمة المضافة" %} ({{ tax.rate }}%):
                        </span>
                        <span class="fw-bold text-info" id="taxSummary">0.00 د.م</span>
                    </div>

                    <!-- الخصم -->
                    <div class="mb-4">
                        <label for="discount" class="form-label fw-bold">
                            <i class="fas fa-tags me-2 text-success"></i>{% trans "الخصم:" %}
                        </label>
                        <div class="input-group">
                            <span class="input-group-text bg-success text-white">
                                <i class="fas fa-minus"></i>
                            </span>
                            <input type="number" class="form-control" id="discount" name="discount" step="0.01" min="0" value="0" placeholder="0.00">
                            <span class="input-group-text">د.م</span>
                        </div>
                    </div>

                    <!-- الخصم بالنسبة المئوية -->
                    <div class="mb-4">
                        <label for="discount_percentage" class="form-label fw-bold">
                            <i class="fas fa-percent me-2 text-warning"></i>{% trans "خصم بالنسبة المئوية:" %}
                        </label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="discount_percentage" step="0.1" min="0" max="100" value="0" placeholder="0">
                            <span class="input-group-text">%</span>
                            <button class="btn btn-warning" type="button" id="apply_percentage">
                                <i class="fas fa-check me-1"></i>{% trans "تطبيق" %}
                            </button>
                        </div>
                        <small class="form-text text-muted mt-1">
                            <i class="fas fa-info-circle me-1"></i>
                            {% trans "أدخل نسبة مئوية واضغط على تطبيق لحساب قيمة الخصم" %}
                        </small>
                    </div>

                    <!-- المجموع الكلي -->
                    <div class="summary-item">
                        <span class="fs-5">
                            <i class="fas fa-money-bill-wave me-2 text-success"></i>
                            {% trans "المجموع الكلي:" %}
                        </span>
                        <span class="fs-4 fw-bold text-primary" id="totalSummary">0.00 د.م</span>
                    </div>

                    <!-- المبلغ المدفوع -->
                    <div class="mb-4">
                        <label for="amount_paid" class="form-label fw-bold">
                            <i class="fas fa-hand-holding-usd me-2 text-primary"></i>{% trans "المبلغ المدفوع:" %}
                        </label>
                        <div class="input-group">
                            <span class="input-group-text bg-primary text-white">
                                <i class="fas fa-coins"></i>
                            </span>
                            <input type="number" class="form-control" id="amount_paid" name="amount_paid" step="0.01" min="0" value="0" placeholder="0.00">
                            <span class="input-group-text">د.م</span>
                        </div>
                    </div>

                    <!-- المبلغ المتبقي -->
                    <div class="summary-item mb-4">
                        <span>
                            <i class="fas fa-balance-scale me-2 text-muted"></i>
                            {% trans "المبلغ المتبقي:" %}
                        </span>
                        <span class="fw-bold text-danger" id="remainingAmount">0.00 د.م</span>
                    </div>

                    <!-- زر تأكيد البيع -->
                    <div class="d-grid">
                        <button type="button" class="btn btn-primary btn-lg" id="confirmSaleBtn">
                            <i class="fas fa-check-circle me-2"></i> {% trans "تأكيد البيع" %}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
</div> <!-- إغلاق main-container -->

<!-- نافذة إضافة المنتج -->
<div class="modal fade" id="addProductModal" tabindex="-1" aria-labelledby="addProductModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addProductModalLabel">
                    <i class="fas fa-box-open me-2"></i>{% trans "إضافة منتج إلى الفاتورة" %}
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- شريط البحث المتقدم -->
                <div class="search-container">
                    <div class="input-group input-group-lg">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control" id="productSearch"
                               placeholder="{% trans 'ابحث عن المنتج بالاسم، الكود، أو الوصف...' %}">
                        <button class="btn btn-outline-secondary" type="button" id="advancedSearchBtn" title="{% trans 'بحث متقدم' %}">
                            <i class="fas fa-sliders-h"></i>
                        </button>
                    </div>
                    <div class="form-text text-center mt-2">
                        <i class="fas fa-lightbulb me-1 text-warning"></i>
                        {% trans "نصيحة: يمكنك البحث بأي جزء من اسم المنتج أو الكود" %}
                    </div>
                </div>

                <!-- فلاتر سريعة -->
                <div class="quick-filters mb-4 d-flex justify-content-center flex-wrap gap-3">
                    <button class="btn filter-btn active" data-filter="all">
                        <i class="fas fa-th-large me-1"></i>{% trans "جميع المنتجات" %}
                    </button>
                    <button class="btn filter-btn" data-filter="available">
                        <i class="fas fa-check-circle me-1"></i>{% trans "متوفر" %}
                    </button>
                    <button class="btn filter-btn" data-filter="promotion">
                        <i class="fas fa-fire me-1"></i>{% trans "عروض خاصة" %}
                    </button>
                    <button class="btn filter-btn" data-filter="popular">
                        <i class="fas fa-star me-1"></i>{% trans "الأكثر مبيعاً" %}
                    </button>
                </div>

                <!-- جدول المنتجات -->
                <div class="product-table-container">
                    <table class="table table-hover align-middle" id="productsSearchTable">
                        <thead>
                            <tr>
                                <th width="80px">
                                    <i class="fas fa-qrcode me-1"></i>{% trans "الكود" %}
                                </th>
                                <th>
                                    <i class="fas fa-box me-1"></i>{% trans "المنتج" %}
                                </th>
                                <th width="120px">
                                    <i class="fas fa-layer-group me-1"></i>{% trans "الفئة" %}
                                </th>
                                <th width="120px">
                                    <i class="fas fa-warehouse me-1"></i>{% trans "المخزون" %}
                                </th>
                                <th width="140px">
                                    <i class="fas fa-tag me-1"></i>{% trans "السعر" %}
                                </th>
                                <th class="d-none">{% trans "الوصف" %}</th>
                                <th width="100px">
                                    <i class="fas fa-plus-circle me-1"></i>{% trans "إضافة" %}
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for product in products %}
                            <tr class="product-item {% if product.has_active_promotion %}has-promotion{% endif %} {% if product.quantity > 0 %}in-stock{% endif %}"
                                data-product-id="{{ product.id }}">
                                <td>
                                    <span class="badge bg-primary fs-6">{{ product.code }}</span>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        {% if product.image %}
                                        <div class="product-thumbnail me-3">
                                            <img src="{{ product.image.url }}" alt="{{ product.name }}" class="product-image-tiny">
                                        </div>
                                        {% else %}
                                        <div class="product-thumbnail me-3 bg-light rounded d-flex align-items-center justify-content-center" style="width:45px;height:45px">
                                            <i class="fas fa-box text-secondary"></i>
                                        </div>
                                        {% endif %}
                                        <div>
                                            <div class="product-name fw-bold text-dark">{{ product.name }}</div>
                                            {% if product.has_active_promotion %}
                                            <span class="badge bg-danger animate__animated animate__pulse animate__infinite">
                                                <i class="fas fa-fire me-1"></i>{% trans "عرض خاص" %}
                                            </span>
                                            {% endif %}
                                            {% if product.description %}
                                            <div class="small text-muted mt-1">{{ product.description|truncatechars:50 }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">{{ product.category.name }}</span>
                                </td>
                                <td>
                                    {% if product.quantity <= 0 %}
                                    <span class="badge bg-danger fs-6">
                                        <i class="fas fa-times me-1"></i>{% trans "نفد" %}
                                    </span>
                                    {% elif product.quantity < 5 %}
                                    <span class="badge bg-warning text-dark fs-6">
                                        <i class="fas fa-exclamation-triangle me-1"></i>{{ product.quantity }}
                                    </span>
                                    {% else %}
                                    <span class="badge bg-success fs-6">
                                        <i class="fas fa-check me-1"></i>{{ product.quantity }}
                                    </span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if product.has_active_promotion %}
                                    <div class="price-container">
                                        <div class="old-price text-decoration-line-through text-muted small">{{ product.selling_price }} د.م</div>
                                        <div class="new-price text-danger fw-bold fs-6">{{ product.promotion_price }} د.م</div>
                                        <div class="discount-badge">
                                            <span class="badge bg-success small">
                                                <i class="fas fa-percentage me-1"></i>{{ product.discount_percentage|floatformat:0 }}%
                                            </span>
                                        </div>
                                    </div>
                                    {% else %}
                                    <span class="fw-bold fs-6 text-primary">{{ product.selling_price }} د.م</span>
                                    {% endif %}
                                </td>
                                <td class="d-none">{{ product.description }}</td>
                                <td>
                                    <button type="button" class="btn btn-sm select-product"
                                            data-id="{{ product.id }}"
                                            data-code="{{ product.code }}"
                                            data-name="{{ product.name }}"
                                            data-price="{% if product.has_active_promotion %}{{ product.promotion_price }}{% else %}{{ product.selling_price }}{% endif %}"
                                            data-stock="{{ product.quantity }}"
                                            data-image="{% if product.image %}{{ product.image.url }}{% endif %}"
                                            data-has-promotion="{{ product.has_active_promotion|lower }}"
                                            data-regular-price="{{ product.selling_price }}"
                                            data-promotion-price="{% if product.has_active_promotion %}{{ product.promotion_price }}{% endif %}"
                                            {% if product.quantity <= 0 %}disabled="disabled" title="{% trans 'المنتج غير متوفر في المخزون' %}"{% endif %}>
                                        <i class="fas fa-plus me-1"></i> {% trans "إضافة" %}
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- رسالة عدم وجود نتائج -->
                <div id="noProductsFound" class="alert alert-info text-center d-none mt-4">
                    <div class="py-4">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">{% trans "لم يتم العثور على منتجات" %}</h5>
                        <p class="mb-0 text-muted">{% trans "جرب تغيير كلمات البحث أو الفلاتر" %}</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary btn-lg" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i> {% trans "إغلاق" %}
                </button>
                <div class="ms-auto">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        {% trans "اختر المنتجات المطلوبة وسيتم إضافتها للفاتورة" %}
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة مسح الباركود -->
<div class="modal fade" id="scannerModal" tabindex="-1" aria-labelledby="scannerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="scannerModalLabel">
                    <i class="fas fa-barcode me-2"></i>{% trans "مسح الباركود" %}
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- منطقة المسح -->
                <div id="scanner-container" class="mb-4">
                    <div class="text-center p-5">
                        <div class="spinner-border text-primary mb-3" role="status">
                            <span class="visually-hidden">{% trans "جاري التحميل..." %}</span>
                        </div>
                        <h5 class="text-muted">{% trans "جاري تهيئة الكاميرا..." %}</h5>
                        <p class="text-muted mb-0">{% trans "يرجى السماح بالوصول للكاميرا" %}</p>
                    </div>
                </div>

                <!-- تعليمات الاستخدام -->
                <div class="alert alert-info">
                    <div class="d-flex align-items-start">
                        <i class="fas fa-info-circle fa-2x me-3 text-info"></i>
                        <div>
                            <h6 class="alert-heading mb-2">
                                <i class="fas fa-graduation-cap me-1"></i>{% trans "تعليمات الاستخدام:" %}
                            </h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <ul class="mb-0 small">
                                        <li class="mb-1">
                                            <i class="fas fa-camera me-1 text-primary"></i>
                                            {% trans "وجه الكاميرا نحو الباركود" %}
                                        </li>
                                        <li class="mb-1">
                                            <i class="fas fa-lightbulb me-1 text-warning"></i>
                                            {% trans "تأكد من وجود إضاءة كافية" %}
                                        </li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul class="mb-0 small">
                                        <li class="mb-1">
                                            <i class="fas fa-hand-paper me-1 text-success"></i>
                                            {% trans "حافظ على ثبات الكاميرا" %}
                                        </li>
                                        <li class="mb-1">
                                            <i class="fas fa-keyboard me-1 text-secondary"></i>
                                            {% trans "أو أدخل الباركود يدوياً" %}
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إدخال يدوي للباركود -->
                <div class="card bg-light">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="fas fa-keyboard me-2"></i>{% trans "إدخال يدوي" %}
                        </h6>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-barcode"></i>
                            </span>
                            <input type="text" class="form-control" id="manualBarcodeInput"
                                   placeholder="{% trans 'أدخل رقم الباركود هنا...' %}">
                            <button class="btn btn-primary" type="button" id="manualBarcodeBtn">
                                <i class="fas fa-search me-1"></i>{% trans "بحث" %}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary btn-lg" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>{% trans "إغلاق" %}
                </button>
            </div>
        </div>
    </div>
</div>


<!-- Email Invoice Modal -->
<div class="modal fade" id="emailInvoiceModal" tabindex="-1" aria-labelledby="emailInvoiceModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="emailInvoiceModalLabel">{% trans "إرسال الفاتورة بالبريد الإلكتروني" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="emailInvoiceForm">
                    <div class="mb-3">
                        <label for="emailTo" class="form-label required-field">{% trans "البريد الإلكتروني للمستلم" %}</label>
                        <input type="email" class="form-control" id="emailTo" required>
                    </div>
                    <div class="mb-3">
                        <label for="emailSubject" class="form-label">{% trans "الموضوع" %}</label>
                        <input type="text" class="form-control" id="emailSubject" value="{% trans "فاتورة من" %} {{ company_info.name|default:"نظام إدارة متجر قطع غيار السيارات" }}">
                    </div>
                    <div class="mb-3">
                        <label for="emailMessage" class="form-label">{% trans "الرسالة" %}</label>
                        <textarea class="form-control" id="emailMessage" rows="4">{% trans "مرحباً,

نرفق لكم فاتورة المشتريات الخاصة بكم. نشكركم على تعاملكم معنا.

مع خالص التقدير,
" %}{{ company_info.name|default:"نظام إدارة متجر قطع غيار السيارات" }}</textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                <button type="button" class="btn btn-primary" id="sendEmailBtn">{% trans "إرسال" %}</button>
            </div>
        </div>
    </div>
</div>

<!-- نافذة إضافة عميل جديد -->
<div class="modal fade" id="quickAddCustomerModal" tabindex="-1" aria-labelledby="quickAddCustomerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="quickAddCustomerModalLabel">
                    <i class="fas fa-user-plus me-2"></i>{% trans "إضافة عميل جديد" %}
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="quickAddCustomerForm">
                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <label for="customerName" class="form-label required-field">
                                <i class="fas fa-user me-2"></i>{% trans "اسم العميل" %}
                            </label>
                            <input type="text" class="form-control form-control-lg" id="customerName"
                                   placeholder="{% trans 'أدخل اسم العميل الكامل...' %}" required>
                        </div>
                        <div class="col-md-6 mb-4">
                            <label for="customerPhone" class="form-label">
                                <i class="fas fa-phone me-2"></i>{% trans "رقم الهاتف" %}
                            </label>
                            <input type="tel" class="form-control form-control-lg" id="customerPhone"
                                   placeholder="{% trans 'مثال: 0612345678' %}">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <label for="customerEmail" class="form-label">
                                <i class="fas fa-envelope me-2"></i>{% trans "البريد الإلكتروني" %}
                            </label>
                            <input type="email" class="form-control form-control-lg" id="customerEmail"
                                   placeholder="{% trans 'مثال: <EMAIL>' %}">
                        </div>
                        <div class="col-md-6 mb-4">
                            <label for="customerCity" class="form-label">
                                <i class="fas fa-city me-2"></i>{% trans "المدينة" %}
                            </label>
                            <input type="text" class="form-control form-control-lg" id="customerCity"
                                   placeholder="{% trans 'أدخل اسم المدينة...' %}">
                        </div>
                    </div>

                    <div class="mb-4">
                        <label for="customerAddress" class="form-label">
                            <i class="fas fa-map-marker-alt me-2"></i>{% trans "العنوان التفصيلي" %}
                        </label>
                        <textarea class="form-control" id="customerAddress" rows="3"
                                  placeholder="{% trans 'أدخل العنوان الكامل للعميل...' %}"></textarea>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        {% trans "سيتم إضافة العميل الجديد تلقائياً إلى قائمة العملاء وسيكون متاحاً للاختيار في الفواتير المستقبلية." %}
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary btn-lg" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>{% trans "إلغاء" %}
                </button>
                <button type="button" class="btn btn-success btn-lg" id="saveCustomerBtn">
                    <i class="fas fa-save me-2"></i> {% trans "حفظ العميل" %}
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

<!-- Quagga JS for barcode scanning -->
<script src="https://cdn.jsdelivr.net/npm/quagga@0.12.1/dist/quagga.min.js"></script>
<script>
    $(document).ready(function() {
        // إعداد CSRF token لجميع AJAX requests
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        const csrftoken = getCookie('csrftoken');

        // إعداد CSRF token لجميع AJAX requests
        $.ajaxSetup({
            beforeSend: function(xhr, settings) {
                if (!this.crossDomain && !/^(GET|HEAD|OPTIONS|TRACE)$/i.test(settings.type) && !this.crossDomain) {
                    xhr.setRequestHeader("X-CSRFToken", csrftoken);
                }
            }
        });

        // Initialize DataTable for products search
        var searchTable = $('#productsSearchTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json"
            },
            "pageLength": 5
        });

        // Product search
        $('#productSearch').keyup(function() {
            searchTable.search($(this).val()).draw();
        });

        // Select Product
        $(document).on('click', '.select-product', function() {
            var productId = $(this).data('id');
            var productCode = $(this).data('code');
            var productName = $(this).data('name');
            var productPrice = $(this).data('price');
            var productStock = $(this).data('stock');
            var productImage = $(this).data('image');

            // التحقق من توفر المنتج في المخزون
            if (productStock <= 0) {
                alert('{% trans "المنتج غير متوفر في المخزون" %}');
                return;
            }

            // Check if product already exists in the table
            var existingRow = $('input[name="product_ids[]"][value="' + productId + '"]').closest('tr');

            if (existingRow.length > 0) {
                // Product already exists, increment quantity
                var quantityInput = existingRow.find('.quantity-input');
                var currentQuantity = parseInt(quantityInput.val());

                // التحقق من أن الكمية الجديدة لا تتجاوز المخزون
                if (currentQuantity + 1 > productStock) {
                    alert('{% trans "الكمية المطلوبة تتجاوز المخزون المتاح!" %}');
                    return;
                }

                quantityInput.val(currentQuantity + 1).trigger('input');

                // Show notification
                alert('{% trans "تم زيادة كمية المنتج" %}');
            } else {
                // Remove "no products" row if exists
                $('#noProductsRow').remove();

                // Add product to table
                var hasPromotion = $(this).data('has-promotion') === true;
                var regularPrice = $(this).data('regular-price');
                var promotionPrice = $(this).data('promotion-price');
                var promotionClass = hasPromotion ? 'table-success' : '';
                var promotionBadge = hasPromotion ? `<span class="badge bg-danger ms-2">{% trans "عرض" %}</span>` : '';
                var priceDisplay = hasPromotion ?
                    `<div class="small text-muted"><del>${regularPrice}</del> <span class="text-danger fw-bold">${productPrice}</span> د.م</div>` :
                    '';

                var newRow = `
                    <tr class="product-row ${promotionClass}">
                        <td>
                            ${productImage ?
                                `<img src="${productImage}" alt="${productName}" class="product-image-small">` :
                                `<div class="text-center"><i class="fas fa-box text-muted"></i></div>`
                            }
                        </td>
                        <td>
                            <input type="hidden" name="product_ids[]" value="${productId}">
                            <strong>${productCode}</strong> - ${productName} ${promotionBadge}
                            <div class="small text-muted">{% trans "المخزون المتاح:" %} ${productStock}</div>
                            ${priceDisplay}
                        </td>
                        <td>
                            <input type="number" class="form-control form-control-sm quantity-input" name="quantities[]" value="1" min="1" max="${productStock}" required data-stock="${productStock}">
                        </td>
                        <td>
                            <div class="input-group input-group-sm">
                                <input type="number" class="form-control form-control-sm unit-price-input" name="unit_prices[]" value="${productPrice}" step="0.01" min="0" required>
                                <span class="input-group-text">د.م</span>
                            </div>
                        </td>
                        <td>
                            <span class="subtotal">${productPrice}</span> د.م
                        </td>
                        <td>
                            <button type="button" class="btn btn-sm btn-danger remove-product">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;

                $('#productsTableBody').append(newRow);
            }

            // Close modal
            $('#addProductModal').modal('hide');

            // Update summary
            updateSummary();
        });

        // Remove Product
        $(document).on('click', '.remove-product', function() {
            $(this).closest('tr').remove();

            // If no products left, add "no products" row
            if ($('#productsTableBody tr').length === 0) {
                $('#productsTableBody').html('<tr id="noProductsRow"><td colspan="6" class="text-center">{% trans "لم يتم إضافة منتجات بعد" %}</td></tr>');
            }

            // Update summary
            updateSummary();
        });

        // Update subtotal when quantity or unit price changes
        $(document).on('input', '.quantity-input, .unit-price-input', function() {
            updateRowSubtotal($(this).closest('tr'));
        });

        // Quantity increase button
        $(document).on('click', '.quantity-increase', function() {
            var row = $(this).closest('tr');
            var quantityInput = row.find('.quantity-input');
            var currentQuantity = parseInt(quantityInput.val()) || 0;
            var stock = parseInt(quantityInput.data('stock'));

            if (currentQuantity < stock) {
                quantityInput.val(currentQuantity + 1);
                updateRowSubtotal(row);
            } else {
                alert('{% trans "الكمية المطلوبة تتجاوز المخزون المتاح!" %}');
            }
        });

        // Quantity decrease button
        $(document).on('click', '.quantity-decrease', function() {
            var row = $(this).closest('tr');
            var quantityInput = row.find('.quantity-input');
            var currentQuantity = parseInt(quantityInput.val()) || 0;

            if (currentQuantity > 1) {
                quantityInput.val(currentQuantity - 1);
                updateRowSubtotal(row);
            }
        });

        // Function to update row subtotal
        function updateRowSubtotal(row) {
            var quantity = parseFloat(row.find('.quantity-input').val()) || 0;
            var unitPrice = parseFloat(row.find('.unit-price-input').val()) || 0;
            var stock = parseInt(row.find('.quantity-input').data('stock'));
            var productId = row.find('input[name="product_ids[]"]').val();

            // Check if quantity exceeds stock
            if (quantity > stock) {
                alert('{% trans "الكمية المطلوبة تتجاوز المخزون المتاح!" %}');
                row.find('.quantity-input').val(stock);
                quantity = stock;
            }

            var subtotal = quantity * unitPrice;

            row.find('.subtotal').text(subtotal.toFixed(2));

            // Update summary
            updateSummary();

            // تحقق من المخزون باستخدام AJAX
            if (quantity > 1) {
                checkStockAvailability(productId, quantity, row);
            }
        }

        // وظيفة للتحقق من توفر المخزون باستخدام AJAX
        function checkStockAvailability(productId, quantity, row) {
            $.ajax({
                url: '{% url "sales:check_stock" %}',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    product_id: productId,
                    quantity: quantity
                }),
                success: function(response) {
                    if (!response.success) {
                        // الكمية غير متوفرة
                        alert(response.error);
                        row.find('.quantity-input').val(response.available || 1);
                        row.find('.quantity-input').trigger('input');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error checking stock:', error);
                }
            });
        }

        // Update discount
        $('#discount').on('input', function() {
            updateSummary();
        });

        // Apply percentage discount
        $('#apply_percentage').on('click', function() {
            var percentage = parseFloat($('#discount_percentage').val()) || 0;
            if (percentage < 0 || percentage > 100) {
                alert('{% trans "الرجاء إدخال نسبة مئوية صحيحة بين 0 و0 100" %}');
                return;
            }

            var subtotal = 0;
            $('.subtotal').each(function() {
                subtotal += parseFloat($(this).text()) || 0;
            });

            var discountAmount = subtotal * (percentage / 100);
            $('#discount').val(discountAmount.toFixed(2)).trigger('input');
        });

        // Update amount paid
        $('#amount_paid').on('input', function() {
            updateSummary();
        });

        // Update summary
        function updateSummary() {
            var subtotal = 0;

            // Calculate subtotal
            $('.subtotal').each(function() {
                subtotal += parseFloat($(this).text()) || 0;
            });

            // Calculate tax
            var taxRate = parseFloat({{ tax.rate }}); // VAT rate from settings
            var tax = subtotal * (taxRate / 100);

            // Get discount
            var discount = parseFloat($('#discount').val()) || 0;

            // Calculate total
            var total = subtotal + tax - discount;
            if (total < 0) total = 0;

            // Get amount paid
            var amountPaid = parseFloat($('#amount_paid').val()) || 0;

            // Calculate remaining amount
            var remaining = total - amountPaid;

            // Update summary
            $('#subtotalSummary').text(subtotal.toFixed(2));
            $('#taxSummary').text(tax.toFixed(2));
            $('#totalSummary').text(total.toFixed(2));
            $('#remainingAmount').text(remaining.toFixed(2));

            // Update amount paid if payment method is cash and total changed
            if ($('#payment_method').val() === 'cash') {
                $('#amount_paid').val(total.toFixed(2));
                $('#remainingAmount').text('0.00');
            }
        }

        // Change payment method
        $('#payment_method').change(function() {
            var method = $(this).val();
            var total = parseFloat($('#totalSummary').text()) || 0;

            if (method === 'cash') {
                $('#amount_paid').val(total.toFixed(2));
                $('#remainingAmount').text('0.00');
            } else if (method === 'credit') {
                $('#amount_paid').val('0.00');
                $('#remainingAmount').text(total.toFixed(2));
            }
        });

        // Barcode input handling
        $('#barcodeInput').keypress(function(e) {
            if (e.which === 13) { // Enter key
                e.preventDefault();
                var barcode = $(this).val().trim();
                if (barcode) {
                    findProductByBarcode(barcode);
                    $(this).val('');
                }
            }
        });

        // Find product by barcode
        function findProductByBarcode(barcode) {
            console.log('DEBUG: Starting barcode search for:', barcode);
            console.log('DEBUG: URL:', "{% url 'inventory:barcode:scan_barcode' %}");

            $.ajax({
                url: "{% url 'inventory:barcode:scan_barcode' %}", // تأكد من أن هذا هو الاسم الصحيح للـ URL الخاص بمسح الباركود
                type: 'POST',
                data: {
                    'barcode_number': barcode,
                    'csrfmiddlewaretoken': '{{ csrf_token }}'
                },
                beforeSend: function(xhr, settings) {
                    console.log('DEBUG: Sending AJAX request with data:', settings.data);
                },
                success: function(data) {
                    if (data.product) {
                        // تم العثور على المنتج
                        console.log('Product found:', data.product);

                        // إضافة المنتج إلى السلة تلقائيًا
                        addProductToCartByBarcode(data.product);

                        // عرض رسالة نجاح
                        showSuccessMessage('تم إضافة "' + data.product.name + '" إلى السلة');

                        $('#barcodeInput').val(''); // مسح حقل الإدخال
                    } else if (data.error) {
                        // لم يتم العثور على المنتج أو حدث خطأ آخر
                        showErrorMessage(data.error);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX Error:', status, error);
                    console.error('Response Text:', xhr.responseText);
                    console.error('Status Code:', xhr.status);
                    showErrorMessage('{% trans "حدث خطأ أثناء البحث عن المنتج. يرجى المحاولة مرة أخرى." %}');
                }
            });
        }

        // وظيفة إضافة منتج إلى السلة عبر الباركود
        function addProductToCartByBarcode(product) {
            // التحقق من وجود المنتج في الجدول
            var existingRow = $('input[name="product_ids[]"][value="' + product.id + '"]').closest('tr');

            if (existingRow.length > 0) {
                // المنتج موجود بالفعل، زيادة الكمية
                var quantityInput = existingRow.find('.quantity-input');
                var currentQuantity = parseInt(quantityInput.val());
                var maxStock = parseInt(quantityInput.data('stock'));

                if (currentQuantity < maxStock) {
                    quantityInput.val(currentQuantity + 1).trigger('input');
                    showSuccessMessage('تم زيادة كمية "' + product.name + '"');
                } else {
                    showErrorMessage('لا يمكن زيادة الكمية. الكمية المتاحة في المخزون: ' + maxStock);
                }
            } else {
                // منتج جديد، إضافته إلى الجدول

                // إزالة رسالة "لا توجد منتجات" إذا كانت موجودة
                $('#noProductsRow').remove();

                // إضافة فئة للصف إذا كان المنتج له عرض ترويجي
                var promotionClass = product.has_promotion ? 'table-success' : '';
                var promotionBadge = product.has_promotion ? `<span class="badge bg-danger ms-2">{% trans "عرض" %}</span>` : '';
                var priceDisplay = product.has_promotion ?
                    `<div class="small text-muted"><del>${product.regular_price}</del> <span class="text-danger fw-bold">${product.price}</span> د.م</div>` :
                    '';
                
                var newRow = `
                    <tr class="product-row ${promotionClass}">
                        <td>
                            ${product.image_url ? 
                                `<img src="${product.image_url}" alt="${product.name}" class="product-image-small">` : 
                                `<div class="text-center"><i class="fas fa-box text-muted"></i></div>`
                            }
                        </td>
                        <td>
                            <input type="hidden" name="product_ids[]" value="${product.id}">
                            <strong>${product.code ? product.code : ''}</strong>${product.code ? ' - ' : ''}${product.name} ${promotionBadge}
                            <div class="small text-muted">{% trans "المخزون المتاح:" %} ${product.quantity}</div>
                            ${priceDisplay}
                        </td>
                        <td>
                            <input type="number" class="form-control form-control-sm quantity-input" name="quantities[]" value="1" min="1" max="${product.quantity}" required data-stock="${product.quantity}">
                        </td>
                        <td>
                            <div class="input-group input-group-sm">
                                <input type="number" class="form-control form-control-sm unit-price-input" name="unit_prices[]" value="${product.price}" step="0.01" min="0" required>
                                <span class="input-group-text">د.م</span>
                            </div>
                        </td>
                        <td>
                            <span class="subtotal">${product.price}</span> د.م
                        </td>
                        <td>
                            <button type="button" class="btn btn-sm btn-danger remove-product">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;

                $('#productsTableBody').append(newRow);

                // تحديث الملخص
                updateSummary();
            }
        }

        // وظيفة عرض رسالة نجاح
        function showSuccessMessage(message) {
            // إزالة الرسائل السابقة
            $('.barcode-message').remove();

            var alertHtml = `
                <div class="alert alert-success alert-dismissible fade show barcode-message" role="alert">
                    <i class="fas fa-check-circle me-2"></i>${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            `;

            $('.barcode-scanner').after(alertHtml);

            // إزالة الرسالة تلقائيًا بعد 3 ثوان
            setTimeout(function() {
                $('.barcode-message').fadeOut();
            }, 3000);
        }

        // وظيفة عرض رسالة خطأ
        function showErrorMessage(message) {
            // إزالة الرسائل السابقة
            $('.barcode-message').remove();

            var alertHtml = `
                <div class="alert alert-danger alert-dismissible fade show barcode-message" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            `;

            $('.barcode-scanner').after(alertHtml);

            // إزالة الرسالة تلقائيًا بعد 5 ثوان
            setTimeout(function() {
                $('.barcode-message').fadeOut();
            }, 5000);
        }

        // Initialize barcode scanner
        var scanner = null;
        var initTimeout = null;

        $('#scannerModal').on('shown.bs.modal', function() {
            // إضافة رسالة تحميل
            $('#scanner-container').html(`
                <div class="text-center p-3">
                    <i class="fas fa-spinner fa-spin fa-2x mb-2"></i>
                    <p>{% trans "جاري تهيئة الكاميرا..." %}</p>
                </div>
                <div id="scanner-video-container" class="d-none">
                    <video id="scanner-video"></video>
                    <div class="barcode-scanner-border"></div>
                </div>
                <div id="manual-entry" class="mt-3 d-none">
                    <div class="input-group">
                        <input type="text" class="form-control" id="manual-barcode-input" placeholder="{% trans "أدخل الباركود يدوياً" %}">
                        <button class="btn btn-primary" type="button" id="manual-barcode-btn">{% trans "إدخال" %}</button>
                    </div>
                </div>
            `);

            // التحقق من وجود مكتبة Quagga
            if (typeof Quagga === 'undefined') {
                showScannerError('{% trans "لم يتم تحميل مكتبة ماسح الباركود بشكل صحيح" %}');
                return;
            }

            // إضافة مؤقت للتوقف بعد 10 ثوانٍ إذا لم تتم تهيئة الكاميرا
            initTimeout = setTimeout(function() {
                if (Quagga) {
                    try {
                        Quagga.stop();
                    } catch (e) {
                        console.log("خطأ عند محاولة إيقاف Quagga:", e);
                    }
                }
                showScannerError('{% trans "انتهت مهلة تهيئة الكاميرا. يرجى المحاولة مرة أخرى أو استخدام الإدخال اليدوي." %}', true);
            }, 10000); // 10 ثوانٍ كحد أقصى للانتظار

            // تهيئة Quagga
            try {
                Quagga.init({
                    inputStream: {
                        name: "Live",
                        type: "LiveStream",
                        target: document.querySelector('#scanner-video'),
                        constraints: {
                            width: 480,
                            height: 320,
                            facingMode: "environment"
                        },
                    },
                    locator: {
                        patchSize: "medium",
                        halfSample: true
                    },
                    numOfWorkers: 1, // تقليل عدد العمال لتحسين الأداء
                    frequency: 10,
                    decoder: {
                        readers: [
                            "code_128_reader",
                            "ean_reader",
                            "ean_8_reader",
                            "code_39_reader",
                            "upc_reader",
                            "upc_e_reader"
                        ], // تقليل عدد القراء لتحسين الأداء
                    },
                }, function(err) {
                    // إلغاء المؤقت بعد الانتهاء من المحاولة
                    clearTimeout(initTimeout);

                    if (err) {
                        console.error("خطأ في تهيئة ماسح الباركود:", err);

                        // تحديد نوع الخطأ وعرض رسالة مناسبة
                        let errorMessage = '{% trans "حدث خطأ أثناء تهيئة ماسح الباركود" %}';

                        if (err.name === 'NotAllowedError' || err.name === 'PermissionDeniedError') {
                            errorMessage = '{% trans "لم يتم السماح بالوصول إلى الكاميرا. يرجى السماح بالوصول للكاميرا في إعدادات المتصفح." %}';
                        } else if (err.name === 'NotFoundError' || err.name === 'DevicesNotFoundError') {
                            errorMessage = '{% trans "لم يتم العثور على كاميرا متصلة بجهازك." %}';
                        } else if (err.name === 'NotReadableError' || err.name === 'TrackStartError') {
                            errorMessage = '{% trans "لا يمكن الوصول إلى الكاميرا. قد تكون مستخدمة من قبل تطبيق آخر." %}';
                        } else if (err.name === 'OverconstrainedError' || err.name === 'ConstraintNotSatisfiedError') {
                            errorMessage = '{% trans "لا يمكن استخدام الكاميرا بالإعدادات المطلوبة." %}';
                        }

                        // عرض رسالة الخطأ وتفعيل الإدخال اليدوي
                        showScannerError(errorMessage, true);
                        return;
                    }

                    try {
                        Quagga.start();
                        // إظهار عنصر الفيديو بعد التهيئة الناجحة
                        $('#scanner-video-container').removeClass('d-none');
                    } catch (error) {
                        console.error("خطأ في بدء ماسح الباركود:", error);
                        showScannerError('{% trans "حدث خطأ أثناء تشغيل ماسح الباركود" %}', true);
                    }
                });
            } catch (e) {
                clearTimeout(initTimeout);
                console.error("خطأ غير متوقع:", e);
                showScannerError('{% trans "حدث خطأ غير متوقع أثناء تهيئة الماسح" %}', true);
            }

            // عند اكتشاف باركود
            Quagga.onDetected(function(result) {
                var code = result.codeResult.code;

                // إضافة تأثير مرئي عند نجاح المسح
                $('.barcode-scanner-border').addClass('barcode-scanned');

                // عرض رسالة نجاح
                $('#scanner-container').append(`
                    <div class="alert alert-success position-absolute bottom-0 start-0 end-0 m-2">
                        <i class="fas fa-check-circle me-2"></i>
                        {% trans "تم مسح الباركود بنجاح:" %} ${code}
                    </div>
                `);

                // تأخير قليل لإظهار رسالة النجاح قبل إغلاق النافذة
                setTimeout(function() {
                    $('#barcodeInput').val(code);
                    findProductByBarcode(code);
                    $('#scannerModal').modal('hide');
                }, 800);
            });

            // معالجة الإدخال اليدوي للباركود
            $(document).on('click', '#manual-barcode-btn', function() {
                var barcode = $('#manual-barcode-input').val().trim();
                if (barcode) {
                    $('#barcodeInput').val(barcode);
                    findProductByBarcode(barcode);
                    $('#scannerModal').modal('hide');
                }
            });
        });

        // دالة لعرض رسائل الخطأ في نافذة الماسح
        function showScannerError(message, showManualEntry = false) {
            var errorHtml = `<div class="alert alert-danger"><i class="fas fa-exclamation-triangle me-2"></i>${message}</div>`;
            $('#scanner-container').html(errorHtml);

            // إضافة زر إعادة المحاولة
            $('#scanner-container').append(`
                <div class="d-grid gap-2 mt-3">
                    <button class="btn btn-outline-primary" type="button" id="retry-camera-btn">
                        <i class="fas fa-sync-alt me-2"></i>{% trans "إعادة تشغيل الكاميرا" %}
                    </button>
                </div>
            `);

            // إظهار خيار الإدخال اليدوي إذا كان مطلوباً
            if (showManualEntry) {
                $('#scanner-container').append(`
                    <div class="alert alert-info mt-3">
                        <i class="fas fa-info-circle me-2"></i>
                        {% trans "يمكنك إدخال الباركود يدوياً بدلاً من ذلك:" %}
                    </div>
                    <div class="input-group mt-3">
                        <input type="text" class="form-control" id="manual-barcode-input" placeholder="{% trans "أدخل الباركود يدوياً" %}">
                        <button class="btn btn-primary" type="button" id="manual-barcode-btn">{% trans "إدخال" %}</button>
                    </div>
                `);
            }
        }

        $('#scannerModal').on('hidden.bs.modal', function() {
            // إيقاف الكاميرا عند إغلاق النافذة
            if (Quagga) {
                try {
                    Quagga.stop();
                } catch (e) {
                    console.log("خطأ عند إيقاف الكاميرا:", e);
                }
            }

            // إلغاء أي مؤقتات معلقة
            if (initTimeout) {
                clearTimeout(initTimeout);
                initTimeout = null;
            }
        });

        // إضافة زر لإعادة تشغيل الكاميرا
        $(document).on('click', '#retry-camera-btn', function() {
            $('#scannerModal').modal('hide');
            setTimeout(function() {
                $('#scannerModal').modal('show');
            }, 500);
        });

        // Quick add customer
        $('#saveCustomerBtn').click(function() {
            var name = $('#customerName').val();
            var phone = $('#customerPhone').val();
            var email = $('#customerEmail').val();
            var address = $('#customerAddress').val();

            if (!name) {
                alert('{% trans "يرجى إدخال اسم العميل" %}');
                return;
            }

            // Here you would normally make an AJAX call to your backend
            // For demo purposes, we'll just add a new option to the select
            var newOption = new Option(name, 'new_' + Date.now());
            $('#customer').append(newOption).val(newOption.value).trigger('change');

            $('#quickAddCustomerModal').modal('hide');

            // Reset form
            $('#quickAddCustomerForm')[0].reset();
        });

        // Form validation
        $('#saleForm').submit(function(e) {
            if ($('#productsTableBody tr').not('#noProductsRow').length === 0) {
                e.preventDefault();
                alert('{% trans "يرجى إضافة منتج واحد على الأقل" %}');
                return false;
            }



            var total = parseFloat($('#totalSummary').text()) || 0;
            var amountPaid = parseFloat($('#amount_paid').val()) || 0;
            var paymentMethod = $('#payment_method').val();

            if (paymentMethod !== 'credit' && amountPaid < total) {
                if (!confirm('{% trans "المبلغ المدفوع أقل من المبلغ الإجمالي. هل تريد المتابعة؟" %}')) {
                    e.preventDefault();
                    return false;
                }
            }

            return true;
        });

        // Save and print button
        $('#saveAndPrintBtn').click(function() {
            // Add a hidden field to indicate print after save
            $('<input>').attr({
                type: 'hidden',
                name: 'print_after_save',
                value: 'true'
            }).appendTo('#saleForm');

            // Submit the form
            $('#saleForm').submit();
        });

        // Save and email button
        $('#saveAndEmailBtn').click(function() {
            // Check if customer has email
            var customerId = $('#customer').val();
            if (!customerId) {
                alert('{% trans "الرجاء اختيار عميل أولاً" %}');
                return;
            }

            // Show email modal
            $('#emailInvoiceModal').modal('show');
        });

        // Send email button
        $('#sendEmailBtn').click(function() {
            // Validate email form
            var emailTo = $('#emailTo').val();
            if (!emailTo) {
                alert('{% trans "الرجاء إدخال البريد الإلكتروني للمستلم" %}');
                return;
            }

            // Add hidden fields for email information
            $('<input>').attr({
                type: 'hidden',
                name: 'email_after_save',
                value: 'true'
            }).appendTo('#saleForm');

            $('<input>').attr({
                type: 'hidden',
                name: 'email_to',
                value: emailTo
            }).appendTo('#saleForm');

            $('<input>').attr({
                type: 'hidden',
                name: 'email_subject',
                value: $('#emailSubject').val()
            }).appendTo('#saleForm');

            $('<input>').attr({
                type: 'hidden',
                name: 'email_message',
                value: $('#emailMessage').val()
            }).appendTo('#saleForm');

            // Close modal
            $('#emailInvoiceModal').modal('hide');

            // Submit the form
            $('#saleForm').submit();
        });

        // Save and download PDF button
        $('#saveAndDownloadBtn').click(function() {
            // Add a hidden field to indicate download after save
            $('<input>').attr({
                type: 'hidden',
                name: 'download_after_save',
                value: 'true'
            }).appendTo('#saleForm');

            // Submit the form
            $('#saleForm').submit();
        });

        // Confirm sale button
        $('#confirmSaleBtn').click(function() {
            // التحقق من وجود منتجات في السلة
            if ($('#productsTableBody tr').not('#noProductsRow').length === 0) {
                alert('{% trans "يرجى إضافة منتج واحد على الأقل" %}');
                return false;
            }

            // التأكيد قبل المتابعة
            if (confirm('{% trans "هل أنت متأكد من تأكيد البيع؟ سيتم تحديث المخزون تلقائياً." %}')) {
                // إضافة حقل مخفي لتأكيد البيع
                $('<input>').attr({
                    type: 'hidden',
                    name: 'confirm_sale',
                    value: 'true'
                }).appendTo('#saleForm');

                // إرسال النموذج
                $('#saleForm').submit();
            }
        });

        // Mobile save button (same as regular save)
        $('#mobileSaveBtn').click(function() {
            // Submit the form
            $('#saleForm').submit();
        });

        // Reset cart button (تفريغ السلة)
        $('#resetCartBtn').click(function() {
            if (confirm('{% trans "هل أنت متأكد من رغبتك في تفريغ السلة بالكامل؟ سيتم حذف جميع المنتجات المضافة." %}')) {
                // Remove all products
                $('#productsTableBody').html('<tr id="noProductsRow"><td colspan="6" class="text-center">{% trans "لم يتم إضافة منتجات بعد" %}</td></tr>');

                // Reset summary
                updateSummary();

                // Show success message
                alert('{% trans "تم تفريغ السلة بنجاح" %}');
            }
        });

        // تحميل العروض الترويجية
        $('#showPromotionsBtn').click(function() {
            loadPromotions();
        });

        function loadPromotions() {
            // إظهار مؤشر التحميل
            $('#promotionsLoading').removeClass('d-none');
            $('#noPromotions').addClass('d-none');
            $('#promotionsContainer').addClass('d-none').empty();

            // تحميل العروض باستخدام AJAX
            $.ajax({
                url: '{% url "sales:get_promotions" %}',
                type: 'GET',
                success: function(response) {
                    $('#promotionsLoading').addClass('d-none');

                    if (!response.success || response.promotions_count === 0) {
                        $('#noPromotions').removeClass('d-none');
                        return;
                    }

                    // عرض المنتجات التي لديها عروض
                    var container = $('#promotionsContainer').removeClass('d-none');

                    $.each(response.products, function(index, product) {
                        var discountBadge = `<span class="badge bg-danger">${product.discount_percentage}% {% trans "خصم" %}</span>`;
                        var priceDisplay = `<del class="text-muted">${product.regular_price}</del> <span class="text-danger fw-bold">${product.promotion_price}</span> د.م`;
                        var stockClass = product.quantity <= 0 ? 'text-danger' : 'text-success';
                        var stockText = product.quantity <= 0 ? '{% trans "غير متوفر" %}' : `${product.quantity} {% trans "متوفر" %}`;
                        var disabled = product.quantity <= 0 ? 'disabled' : '';

                        var card = `
                            <div class="col-md-4">
                                <div class="card h-100 border-success">
                                    ${product.image_url ?
                                        `<img src="${product.image_url}" class="card-img-top p-2" alt="${product.name}" style="height: 150px; object-fit: contain;">` :
                                        `<div class="text-center p-4"><i class="fas fa-box fa-3x text-muted"></i></div>`
                                    }
                                    <div class="card-body">
                                        <h6 class="card-title">${product.name} ${discountBadge}</h6>
                                        <p class="card-text small">
                                            <strong>{% trans "الكود:" %}</strong> ${product.code}<br>
                                            <strong>{% trans "السعر:" %}</strong> ${priceDisplay}<br>
                                            <strong>{% trans "المخزون:" %}</strong> <span class="${stockClass}">${stockText}</span>
                                        </p>
                                    </div>
                                    <div class="card-footer bg-transparent border-success">
                                        <button type="button" class="btn btn-sm btn-success w-100 add-promotion-to-cart"
                                                data-id="${product.id}"
                                                data-code="${product.code}"
                                                data-name="${product.name}"
                                                data-price="${product.promotion_price}"
                                                data-stock="${product.quantity}"
                                                data-has-promotion="true"
                                                data-regular-price="${product.regular_price}"
                                                data-promotion-price="${product.promotion_price}"
                                                ${disabled}>
                                            <i class="fas fa-cart-plus me-1"></i> {% trans "إضافة للسلة" %}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        `;

                        container.append(card);
                    });

                    // إضافة معالج النقر لأزرار إضافة العروض
                    $('.add-promotion-to-cart').click(function() {
                        var productId = $(this).data('id');
                        var productCode = $(this).data('code');
                        var productName = $(this).data('name');
                        var productPrice = $(this).data('price');
                        var productStock = $(this).data('stock');
                        var hasPromotion = $(this).data('has-promotion');
                        var regularPrice = $(this).data('regular-price');
                        var promotionPrice = $(this).data('promotion-price');

                        // التحقق من توفر المنتج في المخزون
                        if (productStock <= 0) {
                            alert('{% trans "المنتج غير متوفر في المخزون" %}');
                            return;
                        }

                        // التحقق من وجود المنتج بالفعل في السلة
                        var existingRow = $('input[name="product_ids[]"][value="' + productId + '"]').closest('tr');

                        if (existingRow.length > 0) {
                            // المنتج موجود بالفعل، زيادة الكمية
                            var quantityInput = existingRow.find('.quantity-input');
                            var currentQuantity = parseInt(quantityInput.val());

                            if (currentQuantity + 1 > productStock) {
                                alert('{% trans "الكمية المطلوبة تتجاوز المخزون المتاح!" %}');
                                return;
                            }

                            quantityInput.val(currentQuantity + 1).trigger('input');
                            alert('{% trans "تم زيادة كمية المنتج" %}');
                        } else {
                            // إزالة صف "لا توجد منتجات" إذا كان موجوداً
                            $('#noProductsRow').remove();

                            // إضافة المنتج إلى السلة
                            var promotionClass = hasPromotion ? 'table-success' : '';
                            var promotionBadge = hasPromotion ? `<span class="badge bg-danger ms-2">{% trans "عرض" %}</span>` : '';
                            var priceDisplay = hasPromotion ?
                                `<div class="small text-muted"><del>${regularPrice}</del> <span class="text-danger fw-bold">${productPrice}</span> د.م</div>` :
                                '';

                            var newRow = `
                                <tr class="product-row ${promotionClass}">
                                    <td>
                                        <div class="text-center"><i class="fas fa-box text-muted"></i></div>
                                    </td>
                                    <td>
                                        <input type="hidden" name="product_ids[]" value="${productId}">
                                        <strong>${productCode}</strong> - ${productName} ${promotionBadge}
                                        <div class="small text-muted">{% trans "المخزون المتاح:" %} ${productStock}</div>
                                        ${priceDisplay}
                                    </td>
                                    <td>
                                        <div class="input-group input-group-sm">
                                            <button type="button" class="btn btn-outline-secondary quantity-decrease"><i class="fas fa-minus"></i></button>
                                            <input type="number" class="form-control form-control-sm quantity-input" name="quantities[]" value="1" min="1" max="${productStock}" required data-stock="${productStock}">
                                            <button type="button" class="btn btn-outline-secondary quantity-increase"><i class="fas fa-plus"></i></button>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="input-group input-group-sm">
                                            <input type="number" class="form-control form-control-sm unit-price-input" name="unit_prices[]" value="${productPrice}" step="0.01" min="0" required>
                                            <span class="input-group-text">د.م</span>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="subtotal">${productPrice}</span> د.م
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-danger remove-product">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            `;

                            $('#productsTableBody').append(newRow);
                        }

                        // تحديث الملخص
                        updateSummary();

                        // إغلاق النافذة المنبثقة
                        $('#promotionsModal').modal('hide');
                    });
                },
                error: function(xhr, status, error) {
                    $('#promotionsLoading').addClass('d-none');
                    $('#noPromotions').removeClass('d-none')
                        .html(`<i class="fas fa-exclamation-circle me-2"></i> {% trans "حدث خطأ أثناء تحميل العروض:" %} ${error}`);
                    console.error('Error loading promotions:', error);
                }
            });
        }

        // Add fixed bottom bar for mobile view
        if (window.innerWidth < 768) {
            $('<div class="fixed-bottom-bar d-md-none">' +
              '<div class="container">' +
              '<div class="d-flex justify-content-between align-items-center">' +
              '<div><strong>{% trans "المجموع:" %}</strong> <span id="mobileTotal">0.00</span> {% trans "د.م" %}</div>' +
              '<button type="submit" class="btn btn-primary"><i class="fas fa-save me-1"></i> {% trans "حفظ" %}</button>' +
              '</div></div></div>').appendTo('body');

            // Update mobile total when summary changes
            function updateMobileTotal() {
                $('#mobileTotal').text($('#totalSummary').text());
            }

            // Call initially and when summary updates
            updateMobileTotal();
            $('#totalSummary').on('DOMSubtreeModified', updateMobileTotal);
        }

        // Initialize summary
        updateSummary();
    });

    // تضمين ملف تحسينات نافذة إضافة المنتج
    $.getScript('/static/js/product-modal.js');

</script>
{% endblock %}
