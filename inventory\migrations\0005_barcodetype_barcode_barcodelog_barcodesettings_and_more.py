# Generated by Django 5.2 on 2025-05-20 10:27

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0003_product_has_promotion_product_promotion_description_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='BarcodeType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, verbose_name='الاسم')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='الرمز')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'نوع الباركود',
                'verbose_name_plural': 'أنواع الباركود',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Barcode',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('barcode_number', models.CharField(max_length=100, unique=True, verbose_name='رقم الباركود')),
                ('is_primary', models.BooleanField(default=False, help_text='تحديد ما إذا كان هذا هو الباركود الرئيسي للمنتج', verbose_name='باركود أساسي')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('include_price', models.BooleanField(default=True, help_text='تضمين سعر المنتج في الباركود المطبوع', verbose_name='تضمين السعر')),
                ('include_name', models.BooleanField(default=True, help_text='تضمين اسم المنتج في الباركود المطبوع', verbose_name='تضمين الاسم')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_barcodes', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='barcodes', to='inventory.product', verbose_name='المنتج')),
                ('barcode_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='barcodes', to='inventory.barcodetype', verbose_name='نوع الباركود')),
            ],
            options={
                'verbose_name': 'باركود',
                'verbose_name_plural': 'الباركودات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='BarcodeLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('create', 'إنشاء'), ('update', 'تحديث'), ('delete', 'حذف'), ('print', 'طباعة'), ('scan', 'مسح')], max_length=20, verbose_name='الإجراء')),
                ('barcode_number', models.CharField(max_length=100, verbose_name='رقم الباركود')),
                ('details', models.TextField(blank=True, null=True, verbose_name='التفاصيل')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('barcode', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='logs', to='inventory.barcode', verbose_name='الباركود')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='barcode_logs', to='inventory.product', verbose_name='المنتج')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='barcode_logs', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'سجل الباركود',
                'verbose_name_plural': 'سجلات الباركود',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='BarcodeSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('default_include_price', models.BooleanField(default=True, verbose_name='تضمين السعر افتراضيًا')),
                ('default_include_name', models.BooleanField(default=True, verbose_name='تضمين الاسم افتراضيًا')),
                ('label_width', models.PositiveIntegerField(default=50, verbose_name='عرض الملصق (مم)')),
                ('label_height', models.PositiveIntegerField(default=30, verbose_name='ارتفاع الملصق (مم)')),
                ('labels_per_row', models.PositiveIntegerField(default=3, verbose_name='عدد الملصقات في الصف')),
                ('labels_per_column', models.PositiveIntegerField(default=8, verbose_name='عدد الملصقات في العمود')),
                ('margin_top', models.PositiveIntegerField(default=10, verbose_name='الهامش العلوي (مم)')),
                ('margin_right', models.PositiveIntegerField(default=10, verbose_name='الهامش الأيمن (مم)')),
                ('margin_bottom', models.PositiveIntegerField(default=10, verbose_name='الهامش السفلي (مم)')),
                ('margin_left', models.PositiveIntegerField(default=10, verbose_name='الهامش الأيسر (مم)')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_barcode_settings', to=settings.AUTH_USER_MODEL, verbose_name='تم التحديث بواسطة')),
                ('default_barcode_type', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='default_settings', to='inventory.barcodetype', verbose_name='نوع الباركود الافتراضي')),
            ],
            options={
                'verbose_name': 'إعدادات الباركود',
                'verbose_name_plural': 'إعدادات الباركود',
            },
        ),
        migrations.AddConstraint(
            model_name='barcode',
            constraint=models.UniqueConstraint(condition=models.Q(('is_primary', True)), fields=('product',), name='unique_primary_barcode_per_product'),
        ),
    ]
