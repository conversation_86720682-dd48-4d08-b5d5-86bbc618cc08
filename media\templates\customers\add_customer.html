{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "إضافة عميل جديد" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .required-field::after {
        content: " *";
        color: red;
    }
    
    .form-card {
        border-radius: 10px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    }
    
    .form-card .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #e3e6f0;
    }
    
    .btn-submit {
        min-width: 120px;
    }
    
    .help-text {
        font-size: 0.8rem;
        color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0 text-gray-800">{% trans "إضافة عميل جديد" %}</h1>
    <a href="{% url 'customers:index' %}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-right me-1"></i> {% trans "العودة إلى قائمة العملاء" %}
    </a>
</div>

<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card shadow form-card mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">{% trans "معلومات العميل" %}</h6>
            </div>
            <div class="card-body">
                <form method="post" id="customerForm">
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {% for error in form.non_field_errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.name.id_for_label }}" class="form-label required-field">{% trans "الاسم" %}</label>
                            {{ form.name }}
                            {% if form.name.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if form.name.help_text %}
                                <div class="help-text">{{ form.name.help_text }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <label for="{{ form.phone.id_for_label }}" class="form-label required-field">{% trans "رقم الهاتف" %}</label>
                            {{ form.phone }}
                            {% if form.phone.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.phone.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if form.phone.help_text %}
                                <div class="help-text">{{ form.phone.help_text }}</div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.email.id_for_label }}" class="form-label">{% trans "البريد الإلكتروني" %}</label>
                            {{ form.email }}
                            {% if form.email.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.email.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if form.email.help_text %}
                                <div class="help-text">{{ form.email.help_text }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <label for="{{ form.category.id_for_label }}" class="form-label">{% trans "الفئة" %}</label>
                            {{ form.category }}
                            {% if form.category.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.category.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if form.category.help_text %}
                                <div class="help-text">{{ form.category.help_text }}</div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.city.id_for_label }}" class="form-label">{% trans "المدينة" %}</label>
                            {{ form.city }}
                            {% if form.city.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.city.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if form.city.help_text %}
                                <div class="help-text">{{ form.city.help_text }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <label for="{{ form.credit_limit.id_for_label }}" class="form-label">{% trans "حد الائتمان" %}</label>
                            {{ form.credit_limit }}
                            {% if form.credit_limit.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.credit_limit.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if form.credit_limit.help_text %}
                                <div class="help-text">{{ form.credit_limit.help_text }}</div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.address.id_for_label }}" class="form-label">{% trans "العنوان" %}</label>
                        {{ form.address }}
                        {% if form.address.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.address.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        {% if form.address.help_text %}
                            <div class="help-text">{{ form.address.help_text }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.notes.id_for_label }}" class="form-label">{% trans "ملاحظات" %}</label>
                        {{ form.notes }}
                        {% if form.notes.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.notes.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        {% if form.notes.help_text %}
                            <div class="help-text">{{ form.notes.help_text }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3 form-check">
                        {{ form.is_active }}
                        <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                            {% trans "نشط" %}
                        </label>
                    </div>
                    
                    <div class="d-flex justify-content-between mt-4">
                        <div>
                            <button type="submit" class="btn btn-primary btn-submit">
                                <i class="fas fa-save me-1"></i> {% trans "حفظ" %}
                            </button>
                            <button type="submit" name="save_and_add_another" class="btn btn-outline-primary btn-submit ms-2">
                                <i class="fas fa-plus me-1"></i> {% trans "حفظ وإضافة آخر" %}
                            </button>
                        </div>
                        <button type="submit" name="save_and_view" class="btn btn-outline-success btn-submit">
                            <i class="fas fa-eye me-1"></i> {% trans "حفظ وعرض" %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // تنسيق حقل رقم الهاتف
        $('#{{ form.phone.id_for_label }}').on('input', function() {
            var value = $(this).val();
            // إزالة الأحرف غير الرقمية
            value = value.replace(/[^0-9]/g, '');
            $(this).val(value);
        });
        
        // التحقق من صحة النموذج قبل الإرسال
        $('#customerForm').on('submit', function(e) {
            var name = $('#{{ form.name.id_for_label }}').val();
            var phone = $('#{{ form.phone.id_for_label }}').val();
            
            if (!name || !phone) {
                e.preventDefault();
                alert("{% trans 'يرجى ملء جميع الحقول المطلوبة' %}");
                return false;
            }
            
            if (phone.length < 10) {
                e.preventDefault();
                alert("{% trans 'يجب أن يكون رقم الهاتف 10 أرقام على الأقل' %}");
                return false;
            }
            
            return true;
        });
    });
</script>
{% endblock %}
