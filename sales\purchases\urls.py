from django.urls import path
from . import views

urlpatterns = [
    # Dashboard
    path('', views.index, name='index'),

    # Purchase Orders
    path('orders/', views.purchase_orders, name='purchase_orders'),
    path('orders/new/', views.new_purchase, name='new_purchase'),
    path('orders/view/<int:purchase_id>/', views.view_purchase, name='view_purchase'),
    path('orders/edit/<int:purchase_id>/', views.edit_purchase, name='edit_purchase'),
    path('orders/delete/<int:purchase_id>/', views.delete_purchase, name='delete_purchase'),
    path('orders/receive/<int:purchase_id>/', views.receive_purchase, name='receive_purchase'),
    path('orders/filter/', views.filter_purchases, name='filter_purchases'),

    # Suppliers
    path('suppliers/', views.suppliers, name='suppliers'),
    path('suppliers/add/', views.add_supplier, name='add_supplier'),
    path('suppliers/edit/<int:supplier_id>/', views.edit_supplier, name='edit_supplier'),
    path('suppliers/view/<int:supplier_id>/', views.view_supplier, name='view_supplier'),
    path('suppliers/delete/<int:supplier_id>/', views.delete_supplier, name='delete_supplier'),
    path('suppliers/search/', views.search_suppliers, name='search_suppliers'),
    path('suppliers/categories/', views.supplier_categories, name='supplier_categories'),
    path('suppliers/categories/add/', views.add_supplier_category, name='add_supplier_category'),
    path('suppliers/categories/edit/<int:category_id>/', views.edit_supplier_category, name='edit_supplier_category'),
    path('suppliers/categories/delete/<int:category_id>/', views.delete_supplier_category, name='delete_supplier_category'),

    # Invoices
    path('invoices/', views.invoices, name='invoices'),
    path('invoices/add/<int:purchase_id>/', views.add_invoice, name='add_invoice'),
    path('invoices/view/<int:invoice_id>/', views.view_invoice, name='view_invoice'),
    path('invoices/edit/<int:invoice_id>/', views.edit_invoice, name='edit_invoice'),
    path('invoices/delete/<int:invoice_id>/', views.delete_invoice, name='delete_invoice'),

    # Payments
    path('payments/', views.payments, name='payments'),
    path('payments/add/<int:purchase_id>/', views.add_payment, name='add_payment'),
    path('payments/edit/<int:payment_id>/', views.edit_payment, name='edit_payment'),
    path('payments/delete/<int:payment_id>/', views.delete_payment, name='delete_payment'),

    # Reports
    path('reports/', views.reports, name='reports'),
    path('reports/suppliers/', views.supplier_report, name='supplier_report'),
    path('reports/purchases/', views.purchases_report, name='purchases_report'),
    path('reports/payments/', views.payments_report, name='payments_report'),
    path('reports/export/<str:report_type>/', views.export_report, name='export_report'),

    # AJAX endpoints
    path('ajax/get-supplier-info/<int:supplier_id>/', views.get_supplier_info, name='get_supplier_info'),
    path('ajax/get-product-info/<int:product_id>/', views.get_product_info, name='get_product_info'),
    path('ajax/get-top-products/', views.get_top_products, name='ajax/get-top-products'),
]
