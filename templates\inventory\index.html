{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "إدارة المخزون" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
<style>
    /* Modern Card Styles */
    .dashboard-card {
        border-radius: 10px;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.1) !important;
        transition: all 0.3s ease;
        height: 100%;
        overflow: hidden;
        border: none;
    }
    
    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1.5rem 0 rgba(58, 59, 69, 0.15) !important;
    }
    
    .dashboard-card .card-icon {
        font-size: 1.8rem;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        margin-bottom: 0.5rem;
        color: white;
    }
    
    /* Status Indicators */
    .status-available {
        color: #198754;
        font-weight: bold;
    }
    .status-low {
        color: #ffc107;
        font-weight: bold;
    }
    .status-out {
        color: #dc3545;
        font-weight: bold;
    }
    
    /* Product Image */
    .product-image-small {
        width: 50px;
        height: 50px;
        object-fit: contain;
        border-radius: 4px;
    }
    
    /* Action Buttons */
    .action-buttons .btn-group {
        display: flex;
        gap: 3px;
    }

    .action-buttons .btn {
        border-radius: 4px;
        padding: 0.25rem 0.5rem;
    }

    /* Product Detail Modal */
    #productImageContainer img {
        transition: transform 0.3s ease;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }

    #productImageContainer img:hover {
        transform: scale(1.05);
    }

    #productDescription {
        min-height: 100px;
        max-height: 200px;
        overflow-y: auto;
    }
    
    /* RTL Support */
    .rtl-content {
        direction: rtl;
        text-align: right;
    }

    /* Advanced Filter Section */
    .advanced-filter-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
        color: white;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }

    .filter-card {
        background: rgba(255,255,255,0.95);
        border-radius: 12px;
        padding: 1.5rem;
        margin-top: 1rem;
        color: #333;
        backdrop-filter: blur(10px);
        border: none;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    }

    /* Enhanced Table */
    .enhanced-table {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        margin-bottom: 2rem;
    }

    .enhanced-table .table {
        margin-bottom: 0;
    }

    .enhanced-table .table thead th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 1rem;
        font-weight: 600;
    }

    .enhanced-table .table tbody tr {
        transition: all 0.2s ease;
    }

    .enhanced-table .table tbody tr:hover {
        background-color: #f8f9fc;
        transform: scale(1.01);
    }

    /* Action Buttons */
    .action-btn {
        padding: 0.5rem 1rem;
        border-radius: 25px;
        font-weight: 500;
        transition: all 0.3s ease;
        border: none;
        margin: 0.25rem;
    }

    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }

    /* Quick Filters */
    .quick-filter {
        background: white;
        border: 2px solid #e3e6f0;
        border-radius: 25px;
        padding: 0.5rem 1rem;
        margin: 0.25rem;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .quick-filter:hover, .quick-filter.active {
        background: var(--bs-primary);
        color: white;
        border-color: var(--bs-primary);
    }

    /* Statistics Cards */
    .stats-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        border-left: 4px solid var(--bs-primary);
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    }

    .stats-card.success { border-left-color: var(--bs-success); }
    .stats-card.info { border-left-color: var(--bs-info); }
    .stats-card.warning { border-left-color: var(--bs-warning); }
    .stats-card.danger { border-left-color: var(--bs-danger); }

    /* Table Styling */
    .table-card .card-header {
        background-color: transparent;
        border-bottom: 1px solid rgba(0,0,0,.05);
        padding: 1rem 1.25rem;
    }
    
    .table {
        margin-bottom: 0;
    }
    
    .table thead th {
        border-top: 0;
        font-weight: 600;
        font-size: 0.85rem;
        color: #ffffffff;
        padding: 1rem;
    }
    
    .table td {
        padding: 0.75rem 1rem;
        vertical-align: middle;
    }
    
    .table-hover tbody tr:hover {
        background-color: rgba(0,0,0,.02);
    }
    
    /* Progress Bars */
    .progress {
        height: 6px;
        border-radius: 3px;
        background-color: rgba(0,0,0,.05);
        margin-top: 0.5rem;
    }
    
    /* DataTables Adjustments */
    .dataTables_length {
        margin-bottom: 15px;
        margin-right: 15px;
    }

    .dataTables_length select {
        padding: 0.375rem 2.25rem 0.375rem 0.75rem;
        font-size: 1rem;
        font-weight: 400;
        line-height: 1.5;
        color: #212529;
        background-color: #fff;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }

    .dataTables_info {
        padding-top: 0.85em;
        white-space: nowrap;
        margin-right: 15px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    {% csrf_token %}

    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-boxes me-2"></i>{% trans "إدارة المخزون" %}
        </h1>
        <div>
        <a href="{% url 'inventory:stock_alerts' %}" class="btn btn-warning me-2">
            <i class="fas fa-exclamation-triangle me-1"></i> {% trans "تنبيهات المخزون" %}
            <span class="badge bg-danger ms-1">{{ low_stock_count|add:out_of_stock_count }}</span>
        </a>
        <button type="button" class="btn btn-success me-2" data-bs-toggle="modal" data-bs-target="#importModal">
            <i class="fas fa-file-import me-1"></i> {% trans "استيراد" %}
        </button>
        <div class="dropdown d-inline-block me-2">
            <button class="btn btn-info dropdown-toggle" type="button" id="exportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="fas fa-file-export me-1"></i> {% trans "تصدير" %}
            </button>
            <ul class="dropdown-menu" aria-labelledby="exportDropdown">
                <li><a class="dropdown-item" href="{% url 'inventory:export_products' %}?type=excel"><i class="fas fa-file-excel me-1"></i> {% trans "تصدير Excel" %}</a></li>
                <li><a class="dropdown-item" href="{% url 'inventory:export_products' %}?type=pdf"><i class="fas fa-file-pdf me-1"></i> {% trans "تصدير PDF" %}</a></li>
                <li><a class="dropdown-item" href="{% url 'inventory:export_products' %}?type=csv"><i class="fas fa-file-csv me-1"></i> {% trans "تصدير CSV" %}</a></li>
            </ul>
        </div>
        <a href="{% url 'inventory:add_product' %}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i> {% trans "إضافة منتج جديد" %}
        </a>
    </div>
</div>

<!-- Dashboard Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2 card-dashboard">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            {% trans "إجمالي المنتجات" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ products.count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-boxes fa-2x text-primary opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <a href="{% url 'inventory:stock_alerts' %}" class="text-decoration-none">
            <div class="card border-left-warning shadow h-100 py-2 card-dashboard">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                {% trans "منتجات منخفضة المخزون" %}
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ low_stock_count|default:"0" }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-warning opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </a>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <a href="{% url 'inventory:stock_alerts' %}?status=out" class="text-decoration-none">
            <div class="card border-left-danger shadow h-100 py-2 card-dashboard">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                {% trans "منتجات نفدت من المخزون" %}
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ out_of_stock_count|default:"0" }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-times-circle fa-2x text-danger opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </a>
    </div>

    <!-- Inventory Value -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card dashboard-card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            {% trans "قيمة المخزون" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ inventory_value|default:"0"|floatformat:2 }} د.م
                        </div>
                    </div>
                    <div class="rounded-circle bg-success bg-opacity-10 p-3">
                        <i class="fas fa-dollar-sign stat-icon text-success"></i>
                    </div>
                </div>
                <div class="progress mt-3" style="height: 5px;">
                    <div class="progress-bar bg-success" role="progressbar" style="width: 100%" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                </div>
                <div class="text-muted mt-2 small">{% trans "القيمة الإجمالية الحالية" %}</div>
            </div>
        </div>
    </div>
</div>

<!-- Alerts Section -->
{% if low_stock_count > 0 or out_of_stock_count > 0 %}
<div class="alert alert-warning alert-dismissible fade show" role="alert">
    <div class="d-flex align-items-center">
        <div class="me-3">
            <i class="fas fa-exclamation-triangle fa-2x"></i>
        </div>
        <div>
            <h5 class="alert-heading mb-1">{% trans "تنبيهات المخزون" %}</h5>
            <p class="mb-0">
                {% if out_of_stock_count > 0 %}
                    {% trans "لديك" %} <strong>{{ out_of_stock_count }}</strong> {% trans "منتج نفد من المخزون" %}{% if low_stock_count > 0 %} {% trans "و" %} {% endif %}
                {% endif %}
                {% if low_stock_count > 0 %}
                    <strong>{{ low_stock_count }}</strong> {% trans "منتج منخفض المخزون" %}
                {% endif %}
                {% trans "يحتاج إلى إعادة تعبئة." %}
            </p>
        </div>
        <div class="ms-auto">
            <a href="{% url 'inventory:stock_alerts' %}" class="btn btn-warning btn-sm">
                {% trans "عرض التنبيهات" %}
            </a>
        </div>
    </div>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
{% endif %}

    <!-- Search Section -->
    <div class="search-section">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" class="form-control" id="globalSearch"
                           placeholder="{% trans 'البحث في المنتجات (الاسم، الكود، الوصف، الباركود...)' %}">
                    <button class="btn btn-outline-secondary" type="button" id="clearSearch">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="col-md-4 text-end">
                <button class="btn btn-info" type="button" data-bs-toggle="collapse"
                        data-bs-target="#filterSection" aria-expanded="false">
                    <i class="fas fa-filter me-1"></i>{% trans "فلاتر متقدمة" %}
                </button>
            </div>
        </div>
    </div>

    <!-- Advanced Filters Section -->
    <div class="collapse" id="filterSection">
        <div class="filter-section">
            <form id="filterForm" method="GET">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label for="categoryFilter" class="form-label">
                            <i class="fas fa-tags me-1"></i>{% trans "الفئة" %}
                        </label>
                        <select class="form-select" id="categoryFilter" name="category">
                            <option value="">{% trans "جميع الفئات" %}</option>
                            {% for category in categories %}
                            <option value="{{ category.id }}">{{ category.name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="col-md-3 mb-3">
                        <label for="supplierFilter" class="form-label">
                            <i class="fas fa-truck me-1"></i>{% trans "المورد" %}
                        </label>
                        <select class="form-select" id="supplierFilter" name="supplier">
                            <option value="">{% trans "جميع الموردين" %}</option>
                            {% for supplier in suppliers %}
                            <option value="{{ supplier.id }}">{{ supplier.name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="col-md-3 mb-3">
                        <label for="stockStatusFilter" class="form-label">
                            <i class="fas fa-info-circle me-1"></i>{% trans "حالة المخزون" %}
                        </label>
                        <select class="form-select" id="stockStatusFilter" name="status">
                            <option value="">{% trans "جميع الحالات" %}</option>
                            <option value="available">{% trans "متوفر" %}</option>
                            <option value="low">{% trans "مخزون منخفض" %}</option>
                            <option value="out">{% trans "نفاد المخزون" %}</option>
                        </select>
                    </div>

                    <div class="col-md-3 mb-3">
                        <label for="min_price" class="form-label">
                            <i class="fas fa-dollar-sign me-1"></i>{% trans "الحد الأدنى للسعر" %}
                        </label>
                        <input type="number" class="form-control" id="min_price" name="min_price"
                               placeholder="0.00" min="0" step="0.01">
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label for="max_price" class="form-label">
                            <i class="fas fa-dollar-sign me-1"></i>{% trans "الحد الأعلى للسعر" %}
                        </label>
                        <input type="number" class="form-control" id="max_price" name="max_price"
                               placeholder="1000.00" min="0" step="0.01">
                    </div>

                    <div class="col-md-3 mb-3">
                        <label for="min_quantity" class="form-label">
                            <i class="fas fa-cubes me-1"></i>{% trans "الحد الأدنى للكمية" %}
                        </label>
                        <input type="number" class="form-control" id="min_quantity" name="min_quantity"
                               placeholder="0" min="0">
                    </div>

                    <div class="col-md-3 mb-3">
                        <label for="max_quantity" class="form-label">
                            <i class="fas fa-cubes me-1"></i>{% trans "الحد الأعلى للكمية" %}
                        </label>
                        <input type="number" class="form-control" id="max_quantity" name="max_quantity"
                               placeholder="1000" min="0">
                    </div>

                        <label for="sort_by" class="form-label">
                            <i class="fas fa-sort me-1"></i>{% trans "ترتيب حسب" %}
                        </label>
                        <select class="form-select" id="sort_by" name="sort_by">
                            <option value="name">{% trans "الاسم" %}</option>
                            <option value="code">{% trans "الكود" %}</option>
                            <option value="selling_price">{% trans "السعر" %}</option>
                            <option value="quantity">{% trans "الكمية" %}</option>
                            <option value="category">{% trans "الفئة" %}</option>
                            <option value="created_at">{% trans "تاريخ الإضافة" %}</option>
                        </select>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <div class="d-flex justify-content-between">
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-filter me-1"></i>{% trans "تطبيق الفلاتر" %}
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="clearFilters()">
                                    <i class="fas fa-undo me-1"></i>{% trans "إعادة تعيين" %}
                            </button>
                            </div>
                            <div>
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    {% trans "عدد النتائج" %}: <span id="resultsCount">{{ products.count }}</span>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

<!-- Products Table -->
<div class="card shadow mb-4 table-card">
    <div class="card-header py-3 d-flex justify-content-between align-items-center">
        <h6 class="m-0 font-weight-bold text-primary">
            <i class="fas fa-table me-2"></i>{% trans "قائمة المنتجات" %}
        </h6>
        <div class="d-flex align-items-center">
            <span class="badge bg-primary me-2">
                <i class="fas fa-list me-1"></i>{{ products.count }} {% trans "منتج" %}
            </span>
            <div class="btn-group">
                <button type="button" class="btn btn-sm btn-outline-danger" id="bulkDeleteBtn" disabled>
                    <i class="fas fa-trash me-1"></i> {% trans "حذف متعدد" %}
                </button>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover mb-0" id="productsTable" width="100%" cellspacing="0">
                <thead class="table-dark">
                    <tr>
                        <th width="30px" class="text-center">
                            <input type="checkbox" id="selectAll" class="form-check-input">
                        </th>
                        <th width="80px" class="text-center">{% trans "صورة" %}</th>
                        <th style="width: 100px;">{% trans "الكود" %}</th>
                        <th style="width: 200px;">{% trans "اسم المنتج" %}</th>
                        <th style="width: 120px;">{% trans "الفئة" %}</th>
                        <th style="width: 120px;">{% trans "المورد" %}</th>
                        <th class="text-center" style="width: 80px;">{% trans "الكمية" %}</th>
                        <th class="text-center" style="width: 80px;">{% trans "الوحدة" %}</th>
                        <th class="text-end" style="width: 100px;">{% trans "سعر البيع" %}</th>
                        <th class="text-end" style="width: 100px;">{% trans "سعر الشراء" %}</th>
                        <th class="text-center" style="width: 100px;">{% trans "الحالة" %}</th>
                        <th class="d-none">{% trans "الوصف" %}</th>
                        <th>{% trans "الإجراءات" %}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for product in products %}
                    <tr>
                        <td>
                            <input type="checkbox" class="form-check-input product-select" value="{{ product.id }}">
                        </td>
                        <td>
                            {% if product.image %}
                            <img src="{{ product.image.url }}" alt="{{ product.name }}" class="product-image-small">
                            {% else %}
                            <div class="text-center">
                                <i class="fas fa-image text-muted"></i>
                            </div>
                            {% endif %}
                        </td>
                        <td>{{ product.code }}</td>
                        <td>{{ product.name }}</td>
                        <td>{{ product.category.name }}</td>
                        <td>{{ product.supplier.name|default:"-" }}</td>
                        <td>{{ product.quantity }}</td>
                        <td>
                            <span class="badge bg-secondary">
                                {{ product.display_unit }}
                            </span>
                        </td>
                        <td>{{ product.selling_price }} د.م</td>
                        <td>{{ product.purchase_price }} د.م</td>
                        <td>
                            {% if product.quantity == 0 %}
                            <span class="status-out">{% trans "نفد" %}</span>
                            {% elif product.is_low_stock %}
                            <span class="status-low">{% trans "منخفض" %}</span>
                            {% else %}
                            <span class="status-available">{% trans "متوفر" %}</span>
                            {% endif %}
                        </td>
                        <td class="d-none">{{ product.description }}</td>
                        <td>
                            <div class="action-buttons">
                                <div class="btn-group">
                                    <a href="#" class="btn btn-sm btn-info view-product-btn" data-id="{{ product.id }}" data-bs-toggle="modal" data-bs-target="#viewProductModal" title="{% trans 'عرض التفاصيل' %}">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="#" class="btn btn-sm btn-info stock-movement-btn" data-id="{{ product.id }}" data-name="{{ product.name }}" data-bs-toggle="modal" data-bs-target="#stockMovementModal" title="{% trans 'حركة المخزون' %}">
                                        <i class="fas fa-exchange-alt"></i>
                                    </a>
                                    <a href="{% url 'inventory:edit_product' product.id %}" class="btn btn-sm btn-primary" title="{% trans 'تعديل' %}">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'inventory:delete_product' product.id %}" class="btn btn-sm btn-danger" title="{% trans 'حذف' %}">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="10" class="text-center">{% trans "لا توجد منتجات" %}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Stock Movement Modal -->
<div class="modal fade" id="stockMovementModal" tabindex="-1" aria-labelledby="stockMovementModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="stockMovementModalLabel">{% trans "حركة المخزون" %}: <span id="productName"></span></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="stockMovementForm">
                    <input type="hidden" id="productId" name="product_id">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="movementType" class="form-label">{% trans "نوع الحركة" %}</label>
                            <select class="form-select" id="movementType" name="movement_type" required>
                                <option value="in">{% trans "وارد (إضافة)" %}</option>
                                <option value="out">{% trans "صادر (سحب)" %}</option>
                                <option value="adjustment">{% trans "تعديل" %}</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="quantity" class="form-label">{% trans "الكمية" %}</label>
                            <input type="number" class="form-control" id="quantity" name="quantity" min="1" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="reference" class="form-label">{% trans "المرجع" %}</label>
                        <input type="text" class="form-control" id="reference" name="reference" placeholder="{% trans 'مثال: فاتورة شراء رقم 123' %}">
                    </div>
                    <div class="mb-3">
                        <label for="notes" class="form-label">{% trans "ملاحظات" %}</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>
                </form>

                <hr>

                <h6 class="mb-3">{% trans "سجل الحركات السابقة" %}</h6>
                <div class="table-responsive">
                    <table class="table table-sm table-bordered" id="movementsTable">
                        <thead>
                            <tr>
                                <th>{% trans "التاريخ" %}</th>
                                <th>{% trans "نوع الحركة" %}</th>
                                <th>{% trans "الكمية" %}</th>
                                <th>{% trans "المرجع" %}</th>
                            </tr>
                        </thead>
                        <tbody id="movementsTableBody">
                            <!-- سيتم ملء هذا الجزء ديناميكيًا -->
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إغلاق" %}</button>
                <button type="button" class="btn btn-primary" id="saveMovementBtn">{% trans "حفظ" %}</button>
            </div>
        </div>
    </div>
</div>

<!-- View Product Modal -->
<div class="modal fade" id="viewProductModal" tabindex="-1" aria-labelledby="viewProductModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewProductModalLabel">{% trans "تفاصيل المنتج" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-5 text-center mb-3">
                        <div id="productImageContainer" class="mb-3">
                            <img id="productImage" src="" alt="صورة المنتج" class="img-fluid rounded" style="max-height: 250px; cursor: pointer;" onclick="openImageInFullScreen(this.src)">
                        </div>
                        <div id="noImageContainer" class="d-none">
                            <div class="border rounded p-5 text-muted">
                                <i class="fas fa-image fa-4x mb-3"></i>
                                <p>{% trans "لا توجد صورة" %}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-7">
                        <h4 id="productName" class="mb-3"></h4>
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <tbody>
                                    <tr>
                                        <th width="40%">{% trans "الكود" %}</th>
                                        <td id="productCode"></td>
                                    </tr>
                                    <tr>
                                        <th>{% trans "الباركود" %}</th>
                                        <td id="productBarcode"></td>
                                    </tr>
                                    <tr>
                                        <th>{% trans "الفئة" %}</th>
                                        <td id="productCategory"></td>
                                    </tr>
                                    <tr>
                                        <th>{% trans "المورد" %}</th>
                                        <td id="productSupplier"></td>
                                    </tr>
                                    <tr>
                                        <th>{% trans "موقع التخزين" %}</th>
                                        <td id="productStorageLocation"></td>
                                    </tr>
                                    <tr>
                                        <th>{% trans "الكمية المتوفرة" %}</th>
                                        <td id="productQuantity"></td>
                                    </tr>
                                    <tr>
                                        <th>{% trans "سعر البيع" %}</th>
                                        <td id="productSellingPrice"></td>
                                    </tr>
                                    <tr>
                                        <th>{% trans "سعر الشراء" %}</th>
                                        <td id="productPurchasePrice"></td>
                                    </tr>
                                    <tr>
                                        <th>{% trans "الحد الأدنى للكمية" %}</th>
                                        <td id="productMinQuantity"></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <h5>{% trans "الوصف" %}</h5>
                        <div id="productDescription" class="border rounded p-3 bg-light"></div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <a href="#" id="editProductLink" class="btn btn-primary">
                    <i class="fas fa-edit me-1"></i> {% trans "تعديل" %}
                </a>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إغلاق" %}</button>
            </div>
        </div>
    </div>
</div>

<!-- Import Modal -->
<div class="modal fade" id="importModal" tabindex="-1" aria-labelledby="importModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="importModalLabel">{% trans "استيراد المنتجات" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="importForm" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="importFile" class="form-label">{% trans "ملف Excel/CSV" %}</label>
                        <input type="file" class="form-control" id="importFile" name="import_file" accept=".xlsx,.xls,.csv" required>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="updateExisting" name="update_existing">
                        <label class="form-check-label" for="updateExisting">
                            {% trans "تحديث المنتجات الموجودة" %}
                        </label>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-1"></i>
                        {% trans "يجب أن يحتوي الملف على الأعمدة التالية: الكود، الاسم، الفئة، نوع السيارة، سعر الشراء، سعر البيع، الكمية، الحد الأدنى للكمية" %}
                    </div>
                    <div class="mb-3">
                        <a href="#" id="downloadTemplateBtn" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-download me-1"></i> {% trans "تنزيل قالب" %}
                        </a>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إغلاق" %}</button>
                <button type="button" class="btn btn-primary" id="importBtn">{% trans "استيراد" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
<script>
    $(document).ready(function() {
        // Initialize DataTable
        var table = $('#productsTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json",
                "lengthMenu": "إظهار _MENU_ من العناصر",
                "info": "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل"
            },
            "order": [[3, "asc"]],
            "columnDefs": [
                { "orderable": false, "targets": [0, 1, 11] }
            ],
            "pageLength": 10,
            "lengthMenu": [[5, 10, 15, 20, 25, 50, -1], [5, 10, 15, 20, 25, 50, "الكل"]],
            "dom": 'lfrtip'
        });
        
        // إضافة زر ترتيب حسب سعر الشراء
        $('#sortByPurchasePrice').click(function() {
            table.order([6, 'asc']).draw(); // ترتيب حسب العمود السادس (سعر الشراء) تصاعدياً
        });

        // Select All Checkbox
        $('#selectAll').change(function() {
            $('.product-select').prop('checked', $(this).prop('checked'));
            updateBulkButtons();
        });

        // Individual Checkboxes
        $(document).on('change', '.product-select', function() {
            updateBulkButtons();
        });

        // Update Bulk Buttons State
        function updateBulkButtons() {
            var selectedCount = $('.product-select:checked').length;
            $('#bulkDeleteBtn').prop('disabled', selectedCount === 0);
        }

        // Category Filter
        $('#categoryFilter').change(function() {
            var categoryId = $(this).val();
            table.column(4).search(categoryId ? $(this).find('option:selected').text() : '').draw();
        });

        // Supplier Filter
        $('#supplierFilter').change(function() {
            var supplierId = $(this).val();
            table.column(5).search(supplierId ? $(this).find('option:selected').text() : '').draw();
        });

        // Stock Status Filter
        $('#stockStatusFilter').change(function() {
            var status = $(this).val();
            if (status) {
                var statusText = '';
                if (status === 'available') statusText = 'متوفر';
                else if (status === 'low') statusText = 'منخفض';
                else if (status === 'out') statusText = 'نفد';

                table.column(9).search(statusText).draw(); // تحديث رقم العمود بعد إضافة عمود سعر الشراء
            } else {
                table.column(9).search('').draw(); // تحديث رقم العمود بعد إضافة عمود سعر الشراء
            }
        });

        // Quick Search
        $('#searchBtn').click(function() {
            var searchTerm = $('#quickSearch').val();
            table.search(searchTerm).draw();
        });

        $('#quickSearch').keypress(function(e) {
            if (e.which === 13) {
                var searchTerm = $(this).val();
                table.search(searchTerm).draw();
            }
        });

        // Stock Movement Modal
        $('.stock-movement-btn').click(function() {
            var productId = $(this).data('id');
            var productName = $(this).data('name');

            $('#productId').val(productId);
            $('#productName').text(productName);

            // Load product movements
            $.ajax({
                url: '/inventory/product/' + productId + '/movements/',
                type: 'GET',
                success: function(data) {
                    var tbody = $('#movementsTableBody');
                    tbody.empty();

                    if (data.movements.length > 0) {
                        $.each(data.movements, function(i, movement) {
                            var row = '<tr>' +
                                '<td>' + movement.created_at + '</td>' +
                                '<td>' + movement.movement_type_display + '</td>' +
                                '<td>' + movement.quantity + '</td>' +
                                '<td>' + (movement.reference || '-') + '</td>' +
                                '</tr>';
                            tbody.append(row);
                        });
                    } else {
                        tbody.append('<tr><td colspan="4" class="text-center">لا توجد حركات سابقة</td></tr>');
                    }
                },
                error: function() {
                    $('#movementsTableBody').html('<tr><td colspan="4" class="text-center text-danger">حدث خطأ أثناء تحميل البيانات</td></tr>');
                }
            });
        });

        // Save Stock Movement
        $('#saveMovementBtn').click(function() {
            var form = $('#stockMovementForm');

            if (!form[0].checkValidity()) {
                form[0].reportValidity();
                return;
            }

            var formData = {
                product_id: $('#productId').val(),
                movement_type: $('#movementType').val(),
                quantity: $('#quantity').val(),
                reference: $('#reference').val(),
                notes: $('#notes').val()
            };

            $.ajax({
                url: '/inventory/stock-movement/add/',
                type: 'POST',
                data: formData,
                headers: {
                    'X-CSRFToken': getCookie('csrftoken')
                },
                success: function(response) {
                    if (response.success) {
                        // Show success message
                        alert('تم حفظ حركة المخزون بنجاح');

                        // Close modal and reload page
                        $('#stockMovementModal').modal('hide');
                        location.reload();
                    } else {
                        alert('حدث خطأ: ' + response.error);
                    }
                },
                error: function(xhr, status, error) {
                    console.error("Error details:", xhr.responseText);
                    alert('حدث خطأ أثناء معالجة الطلب: ' + error);
                }
            });
        });

        // Export Button
        $('#exportBtn').click(function(e) {
            e.preventDefault();
            window.location.href = '/inventory/export/';
        });

        // Import Button
        $('#importBtn').click(function() {
            var form = $('#importForm')[0];

            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            var formData = new FormData(form);

            $.ajax({
                url: '/inventory/import/',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                headers: {
                    'X-CSRFToken': getCookie('csrftoken')
                },
                success: function(response) {
                    if (response.success) {
                        alert('تم استيراد المنتجات بنجاح: ' + response.imported_count + ' منتج');
                        $('#importModal').modal('hide');
                        location.reload();
                    } else {
                        alert('حدث خطأ: ' + response.error);
                    }
                },
                error: function() {
                    alert('حدث خطأ أثناء معالجة الطلب');
                }
            });
        });

        // Download Template Button
        $('#downloadTemplateBtn').click(function(e) {
            e.preventDefault();
            window.location.href = '/inventory/export-template/';
        });

        // Bulk Delete Button
        $('#bulkDeleteBtn').click(function() {
            if (confirm('هل أنت متأكد من رغبتك في حذف المنتجات المحددة؟')) {
                var selectedIds = [];
                $('.product-select:checked').each(function() {
                    selectedIds.push($(this).val());
                });

                $.ajax({
                    url: '/inventory/bulk-delete/',
                    type: 'POST',
                    data: {
                        product_ids: selectedIds.join(',')
                    },
                    headers: {
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    success: function(response) {
                        if (response.success) {
                            alert('تم حذف المنتجات بنجاح');
                            location.reload();
                        } else {
                            alert('حدث خطأ: ' + response.error);
                        }
                    },
                    error: function() {
                        alert('حدث خطأ أثناء معالجة الطلب');
                    }
                });
            }
        });

        // View Product Details
        $('.view-product-btn').click(function() {
            var productId = $(this).data('id');

            // Load product details
            $.ajax({
                url: '/inventory/product/' + productId + '/details/',
                type: 'GET',
                success: function(data) {
                    // Fill product details in modal
                    $('#productName').text(data.name);
                    $('#productCode').text(data.code);
                    $('#productBarcode').text(data.barcode);
                    $('#productCategory').text(data.category_name);
                    $('#productSupplier').text(data.supplier_name);
                    $('#productStorageLocation').text(data.storage_location || '-');
                    $('#productQuantity').text(data.quantity);
                    $('#productSellingPrice').text(data.selling_price + ' د.م');
                    $('#productPurchasePrice').text(data.purchase_price + ' د.م');
                    $('#productMinQuantity').text(data.min_quantity);
                    $('#productDescription').html(data.description || '<span class="text-muted">لا يوجد وصف</span>');

                    // Set edit link
                    $('#editProductLink').attr('href', '/inventory/edit/' + productId + '/');

                    // Handle product image
                    if (data.image_url) {
                        $('#productImage').attr('src', data.image_url);
                        $('#productImageContainer').removeClass('d-none');
                        $('#noImageContainer').addClass('d-none');
                    } else {
                        $('#productImageContainer').addClass('d-none');
                        $('#noImageContainer').removeClass('d-none');
                    }
                },
                error: function() {
                    alert('حدث خطأ أثناء تحميل بيانات المنتج');
                }
            });
        });

        // Function to open image in full screen
        function openImageInFullScreen(src) {
            window.open(src, '_blank');
        }

        // Helper function to get CSRF token
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
    });

    // Define openImageInFullScreen function in global scope
    function openImageInFullScreen(src) {
        window.open(src, '_blank');
    }

    // Clear filters function
    function clearFilters() {
        $('#filterForm')[0].reset();
        $('#globalSearch').val('');
        window.location.href = window.location.pathname;
    }

    // Clear search function
    $('#clearSearch').on('click', function() {
        $('#globalSearch').val('');
        table.search('').draw();
    });

    // Global search functionality
    $('#globalSearch').on('keyup', function() {
        table.search(this.value).draw();
    });
</script>
{% endblock %}
