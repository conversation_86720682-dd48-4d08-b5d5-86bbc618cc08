/**
 * تحسينات نافذة إضافة المنتج
 * هذا الملف يحتوي على الوظائف المخصصة لتحسين تجربة المستخدم في نافذة إضافة المنتج
 */

$(document).ready(function() {
    // تحسين البحث في نافذة إضافة المنتج
    $('#productSearch').on('keyup', function() {
        var value = $(this).val().toLowerCase();
        $("#productsSearchTable tbody tr").filter(function() {
            var rowText = $(this).text().toLowerCase();
            var isVisible = rowText.indexOf(value) > -1;
            $(this).toggle(isVisible);
        });
        
        // إظهار رسالة عدم وجود نتائج إذا لم يتم العثور على منتجات
        if ($("#productsSearchTable tbody tr:visible").length === 0) {
            $("#noProductsFound").removeClass("d-none");
        } else {
            $("#noProductsFound").addClass("d-none");
        }
    });

    // تفعيل أزرار التصفية
    $('.filter-btn').click(function() {
        $('.filter-btn').removeClass('active');
        $(this).addClass('active');
        
        var filter = $(this).data('filter');
        $("#productsSearchTable tbody tr").show();
        
        if (filter === 'available') {
            $("#productsSearchTable tbody tr").not('.in-stock').hide();
        } else if (filter === 'promotion') {
            $("#productsSearchTable tbody tr").not('.has-promotion').hide();
        } else if (filter === 'popular') {
            // يمكن تنفيذ منطق خاص للمنتجات الأكثر مبيعاً هنا
            // هذا مجرد مثال بسيط
            $("#productsSearchTable tbody tr").slice(3).hide();
        }
        
        // إظهار رسالة عدم وجود نتائج إذا لم يتم العثور على منتجات
        if ($("#productsSearchTable tbody tr:visible").length === 0) {
            $("#noProductsFound").removeClass("d-none");
        } else {
            $("#noProductsFound").addClass("d-none");
        }
    });

    // تأثيرات بصرية عند تحريك الماوس فوق الصور المصغرة
    $(document).on('mouseenter', '.product-image-tiny', function() {
        $(this).css('transform', 'scale(1.2)');
    }).on('mouseleave', '.product-image-tiny', function() {
        $(this).css('transform', 'scale(1)');
    });

    // تحسين تجربة اختيار المنتج
    $(document).on('click', '.select-product', function() {
        var button = $(this);
        
        // إضافة تأثير بصري عند اختيار المنتج
        button.html('<i class="fas fa-check me-1"></i> تم الإضافة');
        button.removeClass('btn-primary').addClass('btn-success');
        
        setTimeout(() => {
            button.html('<i class="fas fa-plus me-1"></i> إضافة');
            button.removeClass('btn-success').addClass('btn-primary');
        }, 1000);
    });

    // تفعيل زر البحث المتقدم
    $('#advancedSearchBtn').click(function() {
        // إنشاء نافذة البحث المتقدم
        let advancedSearchHTML = `
            <div class="advanced-search-container p-3 border rounded shadow-sm mb-3 bg-light">
                <h6 class="mb-3 text-primary"><i class="fas fa-filter me-2"></i>البحث المتقدم</h6>
                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label">اسم المنتج</label>
                        <input type="text" class="form-control" id="advancedSearchName">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">الكود</label>
                        <input type="text" class="form-control" id="advancedSearchCode">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">الفئة</label>
                        <select class="form-select" id="advancedSearchCategory">
                            <option value="">كل الفئات</option>
                            <option value="ملابس">ملابس</option>
                            <option value="إلكترونيات">إلكترونيات</option>
                            <option value="أحذية">أحذية</option>
                            <option value="أخرى">أخرى</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">الحالة</label>
                        <select class="form-select" id="advancedSearchStatus">
                            <option value="">الكل</option>
                            <option value="متوفر">متوفر</option>
                            <option value="غير متوفر">غير متوفر</option>
                        </select>
                    </div>
                    <div class="col-12 d-flex justify-content-between">
                        <button type="button" class="btn btn-secondary" id="closeAdvancedSearch">
                            <i class="fas fa-times me-1"></i>إغلاق
                        </button>
                        <button type="button" class="btn btn-primary" id="applyAdvancedSearch">
                            <i class="fas fa-search me-1"></i>بحث
                        </button>
                    </div>
                </div>
            </div>
        `;

        // التحقق مما إذا كانت نافذة البحث المتقدم موجودة بالفعل
        if ($('.advanced-search-container').length === 0) {
            // إضافة نافذة البحث المتقدم قبل فلاتر البحث السريع
            $('.quick-filters').before(advancedSearchHTML);
            
            // إضافة تأثير حركي لظهور النافذة
            $('.advanced-search-container').hide().slideDown(300);
            
            // تفعيل زر الإغلاق
            $('#closeAdvancedSearch').click(function() {
                $('.advanced-search-container').slideUp(300, function() {
                    $(this).remove();
                });
            });
            
            // تفعيل زر البحث
            $('#applyAdvancedSearch').click(function() {
                // جمع معايير البحث
                const name = $('#advancedSearchName').val().toLowerCase();
                const code = $('#advancedSearchCode').val().toLowerCase();
                const category = $('#advancedSearchCategory').val();
                const status = $('#advancedSearchStatus').val();
                
                // تطبيق البحث على جميع المنتجات
                $('.product-item').each(function() {
                    const $item = $(this);
                    const productName = $item.find('.product-name').text().toLowerCase();
                    const productCode = $item.find('.product-code').text().toLowerCase();
                    const productCategory = $item.data('category') || '';
                    const productStatus = $item.data('status') || '';
                    
                    // التحقق من تطابق معايير البحث
                    const nameMatch = name === '' || productName.includes(name);
                    const codeMatch = code === '' || productCode.includes(code);
                    const categoryMatch = category === '' || productCategory === category;
                    const statusMatch = status === '' || productStatus === status;
                    
                    // إظهار أو إخفاء المنتج بناءً على نتيجة البحث
                    if (nameMatch && codeMatch && categoryMatch && statusMatch) {
                        $item.show();
                    } else {
                        $item.hide();
                    }
                });
                
                // إظهار رسالة إذا لم يتم العثور على نتائج
                const visibleProducts = $('.product-item:visible').length;
                if (visibleProducts === 0) {
                    if ($('#noProductsFound').length === 0) {
                        $('.product-table-container').append('<div id="noProductsFound" class="alert alert-info mt-3">لم يتم العثور على منتجات تطابق معايير البحث</div>');
                    }
                } else {
                    $('#noProductsFound').remove();
                }
            });
        } else {
            // إذا كانت نافذة البحث المتقدم مفتوحة بالفعل، قم بإغلاقها
            $('.advanced-search-container').slideUp(300, function() {
                $(this).remove();
            });
        }
    });


    // تحسين تجربة المستخدم عند فتح النافذة
    $('#addProductModal').on('shown.bs.modal', function () {
        $('#productSearch').focus();
        
        // تفعيل زر "الكل" افتراضياً
        $('.filter-btn[data-filter="all"]').addClass('active');
    });
});