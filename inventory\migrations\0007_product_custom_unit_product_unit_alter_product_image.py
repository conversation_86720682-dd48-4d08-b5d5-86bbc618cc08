# Generated by Django 5.2 on 2025-07-15 11:42

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0006_product_barcode'),
    ]

    operations = [
        migrations.AddField(
            model_name='product',
            name='custom_unit',
            field=models.CharField(blank=True, help_text='استخدم هذا الحقل إذا اخترت "أخرى" في الوحدة', max_length=50, null=True, verbose_name='وحدة مخصصة'),
        ),
        migrations.AddField(
            model_name='product',
            name='unit',
            field=models.CharField(choices=[('piece', 'قطعة'), ('kg', 'كيلوغرام'), ('gram', 'غرام'), ('liter', 'لتر'), ('ml', 'مليلتر'), ('meter', 'متر'), ('cm', 'سنتيمتر'), ('mm', 'مليمتر'), ('m2', 'متر مربع'), ('cm2', 'سنتيمتر مربع'), ('m3', 'متر مكعب'), ('cm3', 'سنتيمتر مكعب'), ('box', 'صندوق'), ('pack', 'علبة'), ('bottle', 'زجاجة'), ('bag', 'كيس'), ('roll', 'لفة'), ('sheet', 'ورقة'), ('pair', 'زوج'), ('set', 'طقم'), ('dozen', 'دزينة'), ('carton', 'كرتونة'), ('pallet', 'منصة نقالة'), ('ton', 'طن'), ('inch', 'بوصة'), ('foot', 'قدم'), ('yard', 'ياردة'), ('gallon', 'غالون'), ('other', 'أخرى')], default='piece', max_length=20, verbose_name='الوحدة'),
        ),
        migrations.AlterField(
            model_name='product',
            name='image',
            field=models.ImageField(blank=True, null=True, upload_to='products/', verbose_name='الصورة الرئيسية'),
        ),
    ]
