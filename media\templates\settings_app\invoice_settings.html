{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/settings.css' %}">
<style>
    .color-picker {
        width: 100%;
        height: 38px;
        padding: 0.375rem 0.75rem;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
    }
    .tab-pane {
        padding-top: 20px;
    }
    .change-log-item {
        border-left: 3px solid #4e73df;
        padding-left: 10px;
        margin-bottom: 10px;
    }
    .preview-container {
        position: sticky;
        top: 20px;
    }
    .font-preview {
        margin-top: 10px;
        padding: 10px;
        border: 1px solid #e3e6f0;
        border-radius: 0.25rem;
    }
</style>
{% endblock %}

{% block title %}{% trans "إعدادات الفاتورة" %} | {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header with Statistics -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{% trans "إعدادات الفاتورة" %}</h1>
        <div>
            <span class="badge bg-primary me-2">
                <i class="fas fa-file-invoice me-1"></i> {% trans "عدد الفواتير:" %} {{ invoice_count }}
            </span>
            <span class="badge bg-info">
                <i class="fas fa-clock me-1"></i> {% trans "آخر تحديث:" %} {{ invoice_setting.updated_at|date:"Y-m-d H:i" }}
            </span>
            {% if invoice_setting.updated_by %}
            <span class="badge bg-secondary">
                <i class="fas fa-user me-1"></i> {% trans "بواسطة:" %} {{ invoice_setting.updated_by.username }}
            </span>
            {% endif %}
        </div>
    </div>

    <!-- Alert Messages -->
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    <!-- Tabs -->
    <ul class="nav nav-tabs mb-4" id="invoiceSettingsTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab" aria-controls="general" aria-selected="true">
                <i class="fas fa-cog me-1"></i> {% trans "إعدادات عامة" %}
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="company-tab" data-bs-toggle="tab" data-bs-target="#company" type="button" role="tab" aria-controls="company" aria-selected="false">
                <i class="fas fa-building me-1"></i> {% trans "معلومات المتجر" %}
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="design-tab" data-bs-toggle="tab" data-bs-target="#design" type="button" role="tab" aria-controls="design" aria-selected="false">
                <i class="fas fa-palette me-1"></i> {% trans "التصميم" %}
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="content-tab" data-bs-toggle="tab" data-bs-target="#content" type="button" role="tab" aria-controls="content" aria-selected="false">
                <i class="fas fa-file-alt me-1"></i> {% trans "المحتوى" %}
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="tax-tab" data-bs-toggle="tab" data-bs-target="#tax" type="button" role="tab" aria-controls="tax" aria-selected="false">
                <i class="fas fa-percentage me-1"></i> {% trans "الضرائب" %}
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="print-tab" data-bs-toggle="tab" data-bs-target="#print" type="button" role="tab" aria-controls="print" aria-selected="false">
                <i class="fas fa-print me-1"></i> {% trans "الطباعة" %}
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="email-tab" data-bs-toggle="tab" data-bs-target="#email" type="button" role="tab" aria-controls="email" aria-selected="false">
                <i class="fas fa-envelope me-1"></i> {% trans "البريد الإلكتروني" %}
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="logs-tab" data-bs-toggle="tab" data-bs-target="#logs" type="button" role="tab" aria-controls="logs" aria-selected="false">
                <i class="fas fa-history me-1"></i> {% trans "سجل التغييرات" %}
            </button>
        </li>
    </ul>

    <!-- Tab Content -->
    <div class="tab-content" id="invoiceSettingsTabsContent">
        <!-- General Settings Tab -->
        <div class="tab-pane fade show active" id="general" role="tabpanel" aria-labelledby="general-tab">
            <div class="row">
                <div class="col-lg-8">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3 d-flex justify-content-between align-items-center">
                            <h6 class="m-0 font-weight-bold text-primary">{% trans "إعدادات عامة" %}</h6>
                            <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#resetSequenceModal">
                                <i class="fas fa-redo-alt me-1"></i> {% trans "إعادة تعيين التسلسل" %}
                            </button>
                        </div>
                        <div class="card-body">
                            <form method="post" action="{% url 'settings_app:invoice_settings' %}" enctype="multipart/form-data">
                                {% csrf_token %}
                                <input type="hidden" name="action" value="update_settings">

                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <h5 class="text-gray-800 mb-3">{% trans "ترقيم الفاتورة" %}</h5>
                                        <div class="mb-3">
                                            <label for="prefix" class="form-label">{% trans "بادئة الفاتورة" %}</label>
                                            <input type="text" class="form-control" id="prefix" name="prefix" value="{{ invoice_setting.prefix }}" required>
                                            <div class="form-text">{% trans "مثال: INV-، INVOICE-، إلخ." %}</div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="next_number" class="form-label">{% trans "الرقم التالي" %}</label>
                                            <input type="number" class="form-control" id="next_number" name="next_number" value="{{ invoice_setting.next_number }}" min="1" required>
                                            <div class="form-text">{% trans "سيتم استخدام هذا الرقم للفاتورة التالية." %}</div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="number_format" class="form-label">{% trans "تنسيق الرقم" %}</label>
                                            <select class="form-select" id="number_format" name="number_format">
                                                {% for value, label in invoice_setting.NUMBER_FORMAT_CHOICES %}
                                                    <option value="{{ value }}" {% if invoice_setting.number_format == value %}selected{% endif %}>{{ label }}</option>
                                                {% endfor %}
                                            </select>
                                            <div class="form-text">{% trans "يمكنك تخصيص تنسيق رقم الفاتورة ليشمل التاريخ أو السنة أو الشهر." %}</div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="reset_sequence" class="form-label">{% trans "إعادة تعيين التسلسل" %}</label>
                                            <select class="form-select" id="reset_sequence" name="reset_sequence">
                                                {% for value, label in invoice_setting.RESET_SEQUENCE_CHOICES %}
                                                    <option value="{{ value }}" {% if invoice_setting.reset_sequence == value %}selected{% endif %}>{{ label }}</option>
                                                {% endfor %}
                                            </select>
                                            <div class="form-text">{% trans "متى يجب إعادة تعيين تسلسل أرقام الفواتير." %}</div>
                                        </div>
                                        <div class="alert alert-info">
                                            <strong>{% trans "معاينة:" %}</strong> <span id="invoice_preview">{{ invoice_setting.prefix }}{{ invoice_setting.next_number }}</span>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <h5 class="text-gray-800 mb-3">{% trans "خيارات العرض" %}</h5>
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="show_logo" name="show_logo" {% if invoice_setting.show_logo %}checked{% endif %}>
                                            <label class="form-check-label" for="show_logo">
                                                {% trans "إظهار شعار الشركة" %}
                                            </label>
                                        </div>
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="show_tax" name="show_tax" {% if invoice_setting.show_tax %}checked{% endif %}>
                                            <label class="form-check-label" for="show_tax">
                                                {% trans "إظهار معلومات الضريبة" %}
                                            </label>
                                        </div>
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="show_customer_info" name="show_customer_info" {% if invoice_setting.show_customer_info %}checked{% endif %}>
                                            <label class="form-check-label" for="show_customer_info">
                                                {% trans "إظهار معلومات العميل" %}
                                            </label>
                                        </div>
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="show_signature" name="show_signature" {% if invoice_setting.show_signature %}checked{% endif %}>
                                            <label class="form-check-label" for="show_signature">
                                                {% trans "إظهار التوقيع" %}
                                            </label>
                                        </div>
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="show_qr_code" name="show_qr_code" {% if invoice_setting.show_qr_code %}checked{% endif %}>
                                            <label class="form-check-label" for="show_qr_code">
                                                {% trans "إظهار رمز QR" %}
                                            </label>
                                            <div class="form-text">{% trans "سيتم إنشاء رمز QR يحتوي على معلومات الفاتورة." %}</div>
                                        </div>
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="show_payment_info" name="show_payment_info" {% if invoice_setting.show_payment_info %}checked{% endif %}>
                                            <label class="form-check-label" for="show_payment_info">
                                                {% trans "إظهار معلومات الدفع" %}
                                            </label>
                                        </div>
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="show_company_info" name="show_company_info" {% if invoice_setting.show_company_info %}checked{% endif %}>
                                            <label class="form-check-label" for="show_company_info">
                                                {% trans "إظهار معلومات الشركة" %}
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <h5 class="text-gray-800 mb-3">{% trans "نص التذييل" %}</h5>
                                    <div class="mb-3">
                                        <textarea class="form-control" id="footer_text" name="footer_text" rows="3">{{ invoice_setting.footer_text }}</textarea>
                                        <div class="form-text">{% trans "سيظهر هذا النص في أسفل الفاتورة." %}</div>
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <h5 class="text-gray-800 mb-3">{% trans "الشروط والأحكام" %}</h5>
                                    <div class="mb-3">
                                        <textarea class="form-control" id="terms_and_conditions" name="terms_and_conditions" rows="5">{{ invoice_setting.terms_and_conditions }}</textarea>
                                        <div class="form-text">{% trans "سيتم عرض هذه الشروط والأحكام في الفاتورة." %}</div>
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <h5 class="text-gray-800 mb-3">{% trans "ملاحظات افتراضية" %}</h5>
                                    <div class="mb-3">
                                        <textarea class="form-control" id="default_notes" name="default_notes" rows="3">{{ invoice_setting.default_notes }}</textarea>
                                        <div class="form-text">{% trans "سيتم إضافة هذه الملاحظات تلقائياً لكل فاتورة جديدة." %}</div>
                                    </div>
                                </div>

                                <div class="text-end">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i> {% trans "حفظ الإعدادات" %}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="card shadow mb-4 preview-container">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">{% trans "معاينة الفاتورة" %}</h6>
                        </div>
                        <div class="card-body">
                            <div class="invoice-preview p-3 border rounded">
                        <div class="text-center mb-4">
                            {% if invoice_setting.show_logo %}
                            <div class="mb-3">
                                <img src="{% static 'img/logo-placeholder.png' %}" alt="Company Logo" style="max-height: 80px;">
                            </div>
                            {% endif %}
                            <h4>{% trans "فاتورة" %} #<span class="invoice-number-preview">{{ invoice_setting.prefix }}{{ invoice_setting.next_number }}</span></h4>
                        </div>

                        <div class="row mb-3">
                            <div class="col-6">
                                <strong>{% trans "التاريخ:" %}</strong> {% now "Y-m-d" %}
                            </div>
                            <div class="col-6 text-end">
                                <strong>{% trans "رقم الفاتورة:" %}</strong> <span class="invoice-number-preview">{{ invoice_setting.prefix }}{{ invoice_setting.next_number }}</span>
                            </div>
                        </div>

                        {% if invoice_setting.show_customer_info %}
                        <div class="row mb-4">
                            <div class="col-6">
                                <strong>{% trans "من:" %}</strong>
                                <div>{% trans "اسم الشركة" %}</div>
                                <div>{% trans "عنوان الشركة" %}</div>
                                <div>{% trans "رقم الهاتف" %}</div>
                            </div>
                            <div class="col-6 text-end">
                                <strong>{% trans "إلى:" %}</strong>
                                <div>{% trans "اسم العميل" %}</div>
                                <div>{% trans "عنوان العميل" %}</div>
                                <div>{% trans "رقم الهاتف" %}</div>
                            </div>
                        </div>
                        {% endif %}

                        <table class="table table-bordered table-sm">
                            <thead>
                                <tr>
                                    <th>{% trans "المنتج" %}</th>
                                    <th>{% trans "الكمية" %}</th>
                                    <th>{% trans "السعر" %}</th>
                                    <th>{% trans "المجموع" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>{% trans "منتج 1" %}</td>
                                    <td>2</td>
                                    <td>100.00</td>
                                    <td>200.00</td>
                                </tr>
                                <tr>
                                    <td>{% trans "منتج 2" %}</td>
                                    <td>1</td>
                                    <td>150.00</td>
                                    <td>150.00</td>
                                </tr>
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td colspan="3" class="text-end"><strong>{% trans "المجموع الفرعي:" %}</strong></td>
                                    <td>350.00</td>
                                </tr>
                                {% if invoice_setting.show_tax %}
                                <tr>
                                    <td colspan="3" class="text-end"><strong>{% trans "الضريبة (15%):" %}</strong></td>
                                    <td>52.50</td>
                                </tr>
                                {% endif %}
                                <tr>
                                    <td colspan="3" class="text-end"><strong>{% trans "المجموع:" %}</strong></td>
                                    <td>402.50</td>
                                </tr>
                            </tfoot>
                        </table>

                        {% if invoice_setting.footer_text %}
                        <div class="mt-4 text-center footer-preview">
                            {{ invoice_setting.footer_text }}
                        </div>
                        {% endif %}

                        {% if invoice_setting.terms_and_conditions %}
                        <div class="mt-4 terms-preview">
                            <strong>{% trans "الشروط والأحكام:" %}</strong>
                            <p class="small">{{ invoice_setting.terms_and_conditions }}</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Design Tab -->
<div class="tab-pane fade" id="design" role="tabpanel" aria-labelledby="design-tab">
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "تصميم الفاتورة" %}</h6>
                </div>
                <div class="card-body">
                    <form method="post" action="{% url 'settings_app:invoice_settings' %}" enctype="multipart/form-data">
                        {% csrf_token %}
                        <input type="hidden" name="action" value="update_settings">

                        <div class="mb-4">
                            <h5 class="text-gray-800 mb-3">{% trans "شعار الشركة" %}</h5>
                            <div class="mb-3">
                                <label for="logo" class="form-label">{% trans "تحميل شعار جديد" %}</label>
                                <input type="file" class="form-control" id="logo" name="logo" accept="image/png, image/jpeg, image/jpg">
                                <div class="form-text">{% trans "يدعم صيغ PNG و JPG. الحجم الموصى به: 300x100 بكسل." %}</div>
                            </div>

                            {% if invoice_setting.logo %}
                            <div class="mb-3">
                                <div class="d-flex align-items-center">
                                    <img src="{{ invoice_setting.logo.url }}" alt="Company Logo" class="me-3" style="max-height: 80px;">
                                    <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteLogoModal">
                                        <i class="fas fa-trash-alt me-1"></i> {% trans "حذف الشعار" %}
                                    </button>
                                </div>
                            </div>
                            {% endif %}
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h5 class="text-gray-800 mb-3">{% trans "الألوان" %}</h5>
                                <div class="mb-3">
                                    <label for="primary_color" class="form-label">{% trans "اللون الرئيسي" %}</label>
                                    <input type="color" class="color-picker" id="primary_color" name="primary_color" value="{{ invoice_setting.primary_color }}">
                                    <div class="form-text">{% trans "يستخدم للعناوين والترويسات." %}</div>
                                </div>
                                <div class="mb-3">
                                    <label for="secondary_color" class="form-label">{% trans "اللون الثانوي" %}</label>
                                    <input type="color" class="color-picker" id="secondary_color" name="secondary_color" value="{{ invoice_setting.secondary_color }}">
                                    <div class="form-text">{% trans "يستخدم للخلفيات والتفاصيل الثانوية." %}</div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <h5 class="text-gray-800 mb-3">{% trans "الخطوط" %}</h5>
                                <div class="mb-3">
                                    <label for="font_family" class="form-label">{% trans "نوع الخط" %}</label>
                                    <select class="form-select" id="font_family" name="font_family">
                                        {% for value, label in invoice_setting.FONT_CHOICES %}
                                            <option value="{{ value }}" {% if invoice_setting.font_family == value %}selected{% endif %}>{{ label }}</option>
                                        {% endfor %}
                                    </select>
                                    <div class="form-text">{% trans "اختر نوع الخط المستخدم في الفاتورة." %}</div>
                                </div>

                                <div class="font-preview p-3 border rounded" id="font_preview">
                                    <p class="mb-1" style="font-size: 18px;">{% trans "مثال على العنوان" %}</p>
                                    <p class="mb-1" style="font-size: 14px;">{% trans "مثال على النص العادي في الفاتورة. هذا النص يوضح شكل الخط المختار." %}</p>
                                    <p class="mb-0" style="font-size: 12px;">{% trans "مثال على نص صغير مثل الملاحظات والتفاصيل." %}</p>
                                </div>
                            </div>
                        </div>

                        <div class="text-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> {% trans "حفظ التصميم" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow mb-4 preview-container">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "معاينة التصميم" %}</h6>
                </div>
                <div class="card-body">
                    <div class="design-preview p-3 border rounded">
                        <div class="text-center mb-3">
                            <img src="{% if invoice_setting.logo %}{{ invoice_setting.logo.url }}{% else %}{% static 'img/logo-placeholder.png' %}{% endif %}" alt="Company Logo" style="max-height: 80px;">
                        </div>

                        <div class="mb-3" style="border-bottom: 2px solid var(--primary-color, #4e73df);">
                            <h4 style="color: var(--primary-color, #4e73df);">{% trans "فاتورة مبيعات" %}</h4>
                        </div>

                        <div class="mb-3">
                            <div class="row">
                                <div class="col-6">
                                    <strong>{% trans "رقم الفاتورة:" %}</strong> INV-1001
                                </div>
                                <div class="col-6 text-end">
                                    <strong>{% trans "التاريخ:" %}</strong> {% now "Y-m-d" %}
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="p-2" style="background-color: var(--secondary-color, #f8f9fa);">
                                <strong>{% trans "ملخص الفاتورة" %}</strong>
                            </div>
                            <div class="p-2 border-bottom">
                                <div class="row">
                                    <div class="col-6">{% trans "المجموع الفرعي:" %}</div>
                                    <div class="col-6 text-end">350.00 ر.س</div>
                                </div>
                            </div>
                            <div class="p-2 border-bottom">
                                <div class="row">
                                    <div class="col-6">{% trans "الضريبة (15%):" %}</div>
                                    <div class="col-6 text-end">52.50 ر.س</div>
                                </div>
                            </div>
                            <div class="p-2" style="font-weight: bold;">
                                <div class="row">
                                    <div class="col-6">{% trans "المجموع:" %}</div>
                                    <div class="col-6 text-end">402.50 ر.س</div>
                                </div>
                            </div>
                        </div>

                        <div class="text-center mt-3">
                            <small>{% trans "شكراً لتعاملكم معنا" %}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Content Tab -->
<div class="tab-pane fade" id="content" role="tabpanel" aria-labelledby="content-tab">
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "محتوى الفاتورة" %}</h6>
                </div>
                <div class="card-body">
                    <form method="post" action="{% url 'settings_app:invoice_settings' %}" enctype="multipart/form-data">
                        {% csrf_token %}
                        <input type="hidden" name="action" value="update_content_settings">

                        <div class="mb-4">
                            <h5 class="text-gray-800 mb-3">{% trans "معلومات الفاتورة" %}</h5>
                            <p class="text-muted mb-3">{% trans "حدد المعلومات التي تريد إظهارها في الفاتورة المطبوعة" %}</p>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="show_invoice_number" name="show_invoice_number" checked>
                                        <label class="form-check-label" for="show_invoice_number">
                                            {% trans "رقم الفاتورة" %}
                                        </label>
                                    </div>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="show_customer" name="show_customer" checked>
                                        <label class="form-check-label" for="show_customer">
                                            {% trans "العميل" %}
                                        </label>
                                    </div>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="show_sale_date" name="show_sale_date" checked>
                                        <label class="form-check-label" for="show_sale_date">
                                            {% trans "تاريخ البيع" %}
                                        </label>
                                    </div>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="show_employee" name="show_employee" checked>
                                        <label class="form-check-label" for="show_employee">
                                            {% trans "الموظف" %}
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="show_status" name="show_status" checked>
                                        <label class="form-check-label" for="show_status">
                                            {% trans "الحالة" %}
                                        </label>
                                    </div>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="show_payment_method" name="show_payment_method" checked>
                                        <label class="form-check-label" for="show_payment_method">
                                            {% trans "طريقة الدفع" %}
                                        </label>
                                    </div>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="show_created_at" name="show_created_at" checked>
                                        <label class="form-check-label" for="show_created_at">
                                            {% trans "تاريخ الإنشاء" %}
                                        </label>
                                    </div>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="show_updated_at" name="show_updated_at" checked>
                                        <label class="form-check-label" for="show_updated_at">
                                            {% trans "آخر تحديث" %}
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <h5 class="text-gray-800 mb-3">{% trans "معلومات المنتجات" %}</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="show_product_code" name="show_product_code" checked>
                                        <label class="form-check-label" for="show_product_code">
                                            {% trans "كود المنتج" %}
                                        </label>
                                    </div>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="show_product_description" name="show_product_description" checked>
                                        <label class="form-check-label" for="show_product_description">
                                            {% trans "وصف المنتج" %}
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="show_unit_price" name="show_unit_price" checked>
                                        <label class="form-check-label" for="show_unit_price">
                                            {% trans "سعر الوحدة" %}
                                        </label>
                                    </div>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="show_quantity" name="show_quantity" checked>
                                        <label class="form-check-label" for="show_quantity">
                                            {% trans "الكمية" %}
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <h5 class="text-gray-800 mb-3">{% trans "معلومات إضافية" %}</h5>
                            <div class="mb-3">
                                <label for="invoice_title" class="form-label">{% trans "عنوان الفاتورة" %}</label>
                                <input type="text" class="form-control" id="invoice_title" name="invoice_title" value="فاتورة مبيعات">
                                <div class="form-text">{% trans "العنوان الرئيسي الذي سيظهر في أعلى الفاتورة." %}</div>
                            </div>
                            <div class="mb-3">
                                <label for="invoice_subtitle" class="form-label">{% trans "العنوان الفرعي" %}</label>
                                <input type="text" class="form-control" id="invoice_subtitle" name="invoice_subtitle" value="شكراً لتعاملكم معنا">
                                <div class="form-text">{% trans "نص إضافي يظهر تحت العنوان الرئيسي." %}</div>
                            </div>
                        </div>

                        <div class="text-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> {% trans "حفظ المحتوى" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow mb-4 preview-container">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "معاينة المحتوى" %}</h6>
                </div>
                <div class="card-body">
                    <div class="content-preview p-3 border rounded">
                        <div class="text-center mb-3">
                            <h4>{% trans "فاتورة مبيعات" %}</h4>
                            <p class="small">{% trans "شكراً لتعاملكم معنا" %}</p>
                        </div>

                        <div class="mb-3">
                            <div class="row">
                                <div class="col-6">
                                    <strong>{% trans "رقم الفاتورة:" %}</strong> INV-1001
                                </div>
                                <div class="col-6 text-end">
                                    <strong>{% trans "التاريخ:" %}</strong> {% now "Y-m-d" %}
                                </div>
                            </div>
                        </div>

                        <div class="mb-3 p-2 bg-light">
                            <div class="row mb-1">
                                <div class="col-6">{% trans "العميل:" %}</div>
                                <div class="col-6 text-end">محمد أحمد</div>
                            </div>
                            <div class="row mb-1">
                                <div class="col-6">{% trans "الموظف:" %}</div>
                                <div class="col-6 text-end">admin</div>
                            </div>
                            <div class="row mb-1">
                                <div class="col-6">{% trans "الحالة:" %}</div>
                                <div class="col-6 text-end">مكتمل</div>
                            </div>
                            <div class="row mb-1">
                                <div class="col-6">{% trans "طريقة الدفع:" %}</div>
                                <div class="col-6 text-end">نقداً</div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="table-responsive">
                                <table class="table table-sm table-bordered">
                                    <thead class="table-light">
                                        <tr>
                                            <th>{% trans "المنتج" %}</th>
                                            <th>{% trans "الكمية" %}</th>
                                            <th>{% trans "السعر" %}</th>
                                            <th>{% trans "المجموع" %}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>منتج 1</td>
                                            <td>2</td>
                                            <td>100.00</td>
                                            <td>200.00</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <div class="text-center mt-3">
                            <small>{% trans "تاريخ الإنشاء:" %} {% now "Y-m-d H:i" %}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
    <!-- Tax Tab -->
    <div class="tab-pane fade" id="tax" role="tabpanel" aria-labelledby="tax-tab">
        <div class="row">
            <div class="col-lg-8">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">{% trans "إعدادات الضرائب" %}</h6>
                    </div>
                    <div class="card-body">
                        <form method="post" action="{% url 'settings_app:invoice_settings' %}" enctype="multipart/form-data">
                            {% csrf_token %}
                            <input type="hidden" name="action" value="update_tax_settings">

                            <div class="mb-4">
                                <h5 class="text-gray-800 mb-3">{% trans "الضريبة الرئيسية" %}</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="tax_name" class="form-label">{% trans "اسم الضريبة" %}</label>
                                            <input type="text" class="form-control" id="tax_name" name="tax_name" value="{{ tax_settings.name }}" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="tax_rate" class="form-label">{% trans "نسبة الضريبة (%)" %}</label>
                                            <input type="number" class="form-control" id="tax_rate" name="tax_rate" value="{{ tax_settings.rate }}" min="0" max="100" step="0.01" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="tax_type" class="form-label">{% trans "نوع الضريبة" %}</label>
                                    <select class="form-select" id="tax_type" name="tax_type">
                                        <option value="inclusive" {% if tax_settings.tax_type == 'inclusive' %}selected{% endif %}>{% trans "شامل (مضمنة في السعر)" %}</option>
                                        <option value="exclusive" {% if tax_settings.tax_type == 'exclusive' %}selected{% endif %}>{% trans "إضافي (تضاف للسعر)" %}</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="tax_number" class="form-label">{% trans "الرقم الضريبي" %}</label>
                                    <input type="text" class="form-control" id="tax_number" name="tax_number" value="{{ tax_settings.tax_number }}">
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="is_enabled" name="is_enabled" {% if tax_settings.is_enabled %}checked{% endif %}>
                                    <label class="form-check-label" for="is_enabled">
                                        {% trans "تفعيل الضريبة" %}
                                    </label>
                                </div>
                            </div>

                            <div class="mb-4">
                                <h5 class="text-gray-800 mb-3">{% trans "خيارات عرض الضريبة" %}</h5>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="show_tax_column" name="show_tax_column" checked>
                                    <label class="form-check-label" for="show_tax_column">
                                        {% trans "إظهار عمود الضريبة في الفاتورة" %}
                                    </label>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="show_tax_summary" name="show_tax_summary" checked>
                                    <label class="form-check-label" for="show_tax_summary">
                                        {% trans "إظهار ملخص الضريبة في نهاية الفاتورة" %}
                                    </label>
                                </div>
                            </div>

                            <div class="text-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i> {% trans "حفظ إعدادات الضريبة" %}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">{% trans "معاينة الضريبة" %}</h6>
                    </div>
                    <div class="card-body">
                        <div class="tax-preview p-3 border rounded">
                            <div class="mb-3">
                                <h5 class="text-center">{% trans "مثال على حساب الضريبة" %}</h5>
                            </div>

                            <div class="mb-3">
                                <div class="table-responsive">
                                    <table class="table table-sm table-bordered">
                                        <thead class="table-light">
                                            <tr>
                                                <th>{% trans "المنتج" %}</th>
                                                <th>{% trans "السعر" %}</th>
                                                <th>{% trans "الضريبة" %}</th>
                                                <th>{% trans "المجموع" %}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>منتج 1</td>
                                                <td>100.00</td>
                                                <td id="tax-preview-value">15.00</td>
                                                <td id="tax-preview-total">115.00</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <div class="alert alert-info small">
                                <div class="mb-2"><strong>{% trans "معلومات الضريبة:" %}</strong></div>
                                <div class="mb-1">{% trans "الاسم:" %} <span id="tax-name-preview">{{ tax_settings.name }}</span></div>
                                <div class="mb-1">{% trans "النسبة:" %} <span id="tax-rate-preview">{{ tax_settings.rate }}</span>%</div>
                                <div class="mb-1">{% trans "النوع:" %} <span id="tax-type-preview">{% if tax_settings.tax_type == 'inclusive' %}{% trans "شامل" %}{% else %}{% trans "إضافي" %}{% endif %}</span></div>
                                <div>{% trans "الرقم الضريبي:" %} <span id="tax-number-preview">{{ tax_settings.tax_number }}</span></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Print Tab -->
    <div class="tab-pane fade" id="print" role="tabpanel" aria-labelledby="print-tab">
        <div class="row">
            <div class="col-lg-8">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">{% trans "إعدادات الطباعة" %}</h6>
                    </div>
                    <div class="card-body">
                        <form method="post" action="{% url 'settings_app:invoice_settings' %}" enctype="multipart/form-data">
                            {% csrf_token %}
                            <input type="hidden" name="action" value="update_print_settings">

                            <div class="mb-4">
                                <h5 class="text-gray-800 mb-3">{% trans "خيارات الطباعة" %}</h5>
                                <div class="mb-3">
                                    <label for="paper_size" class="form-label">{% trans "حجم الورق" %}</label>
                                    <select class="form-select" id="paper_size" name="paper_size">
                                        {% for value, label in invoice_setting.PAPER_SIZE_CHOICES %}
                                            <option value="{{ value }}" {% if invoice_setting.paper_size == value %}selected{% endif %}>{{ label }}</option>
                                        {% endfor %}
                                    </select>
                                    <div class="form-text">{% trans "اختر حجم الورق المستخدم للطباعة." %}</div>
                                </div>
                                <div class="mb-3">
                                    <label for="print_copies" class="form-label">{% trans "عدد النسخ" %}</label>
                                    <input type="number" class="form-control" id="print_copies" name="print_copies" value="1" min="1" max="10">
                                    <div class="form-text">{% trans "عدد النسخ التي سيتم طباعتها تلقائياً." %}</div>
                                </div>
                            </div>

                            <div class="mb-4">
                                <h5 class="text-gray-800 mb-3">{% trans "هوامش الصفحة" %}</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="margin_top" class="form-label">{% trans "الهامش العلوي (مم)" %}</label>
                                            <input type="number" class="form-control" id="margin_top" name="margin_top" value="10" min="0" max="50">
                                        </div>
                                        <div class="mb-3">
                                            <label for="margin_right" class="form-label">{% trans "الهامش الأيمن (مم)" %}</label>
                                            <input type="number" class="form-control" id="margin_right" name="margin_right" value="10" min="0" max="50">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="margin_bottom" class="form-label">{% trans "الهامش السفلي (مم)" %}</label>
                                            <input type="number" class="form-control" id="margin_bottom" name="margin_bottom" value="10" min="0" max="50">
                                        </div>
                                        <div class="mb-3">
                                            <label for="margin_left" class="form-label">{% trans "الهامش الأيسر (مم)" %}</label>
                                            <input type="number" class="form-control" id="margin_left" name="margin_left" value="10" min="0" max="50">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-4">
                                <h5 class="text-gray-800 mb-3">{% trans "خيارات متقدمة" %}</h5>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="auto_print" name="auto_print" checked>
                                    <label class="form-check-label" for="auto_print">
                                        {% trans "طباعة تلقائية عند إنشاء فاتورة جديدة" %}
                                    </label>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="print_background" name="print_background" checked>
                                    <label class="form-check-label" for="print_background">
                                        {% trans "طباعة الخلفيات والألوان" %}
                                    </label>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="print_header_footer" name="print_header_footer" checked>
                                    <label class="form-check-label" for="print_header_footer">
                                        {% trans "طباعة رأس وتذييل الصفحة" %}
                                    </label>
                                </div>
                            </div>

                            <div class="text-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i> {% trans "حفظ إعدادات الطباعة" %}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="card shadow mb-4 preview-container">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">{% trans "معاينة الطباعة" %}</h6>
                    </div>
                    <div class="card-body">
                        <div class="print-preview p-3 border rounded">
                            <div class="d-flex justify-content-center mb-3">
                                <div class="paper-preview">
                                    <div class="paper-content text-center">
                                        <div class="small mb-2">{% trans "معاينة حجم الورق" %}</div>
                                        <div class="paper-size-label">A4</div>
                                        <div class="mt-3">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="printTest()">
                                                <i class="fas fa-print me-1"></i> {% trans "اختبار الطباعة" %}
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="alert alert-info small">
                                <i class="fas fa-info-circle me-1"></i> {% trans "يمكنك اختبار إعدادات الطباعة بالضغط على زر اختبار الطباعة." %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Email Tab -->
    <div class="tab-pane fade" id="email" role="tabpanel" aria-labelledby="email-tab">
        <div class="row">
            <div class="col-lg-8">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">{% trans "إعدادات البريد الإلكتروني" %}</h6>
                    </div>
                    <div class="card-body">
                        <form method="post" action="{% url 'settings_app:invoice_settings' %}" enctype="multipart/form-data">
                            {% csrf_token %}
                            <input type="hidden" name="action" value="update_email_settings">

                            <div class="mb-4">
                                <h5 class="text-gray-800 mb-3">{% trans "قوالب البريد الإلكتروني" %}</h5>
                                <div class="mb-3">
                                    <label for="email_subject_template" class="form-label">{% trans "قالب عنوان البريد" %}</label>
                                    <input type="text" class="form-control" id="email_subject_template" name="email_subject_template" value="{{ invoice_setting.email_subject_template }}">
                                    <div class="form-text">{% trans "يمكنك استخدام {invoice_number}، {customer_name}، {company_name} كمتغيرات." %}</div>
                                </div>
                                <div class="mb-3">
                                    <label for="email_body_template" class="form-label">{% trans "قالب نص البريد" %}</label>
                                    <textarea class="form-control" id="email_body_template" name="email_body_template" rows="6">{{ invoice_setting.email_body_template }}</textarea>
                                    <div class="form-text">{% trans "يمكنك استخدام {invoice_number}، {customer_name}، {company_name}، {invoice_date}، {total_amount} كمتغيرات." %}</div>
                                </div>
                            </div>

                            <div class="mb-4">
                                <h5 class="text-gray-800 mb-3">{% trans "إعدادات SMTP" %}</h5>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-1"></i> {% trans "يتم استخدام إعدادات SMTP من صفحة إعدادات البريد الإلكتروني العامة." %}
                                    <a href="{% url 'settings_app:email_settings' %}" class="alert-link">{% trans "تعديل إعدادات SMTP" %}</a>
                                </div>
                            </div>

                            <div class="mb-4">
                                <h5 class="text-gray-800 mb-3">{% trans "خيارات إضافية" %}</h5>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="auto_send_email" name="auto_send_email" {% if invoice_setting.auto_send_email %}checked{% endif %}>
                                    <label class="form-check-label" for="auto_send_email">
                                        {% trans "إرسال الفاتورة تلقائياً للعميل عند إتمام البيع" %}
                                    </label>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="attach_pdf" name="attach_pdf" checked>
                                    <label class="form-check-label" for="attach_pdf">
                                        {% trans "إرفاق الفاتورة كملف PDF" %}
                                    </label>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="bcc_admin" name="bcc_admin" checked>
                                    <label class="form-check-label" for="bcc_admin">
                                        {% trans "إرسال نسخة مخفية للمدير" %}
                                    </label>
                                </div>
                            </div>

                            <div class="text-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i> {% trans "حفظ إعدادات البريد" %}
                                </button>
                                <button type="button" class="btn btn-outline-primary ms-2" data-bs-toggle="modal" data-bs-target="#testEmailModal">
                                    <i class="fas fa-paper-plane me-1"></i> {% trans "اختبار الإرسال" %}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">{% trans "معاينة البريد الإلكتروني" %}</h6>
                    </div>
                    <div class="card-body">
                        <div class="email-preview p-3 border rounded">
                            <div class="mb-3">
                                <div class="email-header p-2 bg-light rounded">
                                    <div class="mb-2"><strong>{% trans "من:" %}</strong> {{ company_info.name }} &lt;{{ company_info.email }}&gt;</div>
                                    <div class="mb-2"><strong>{% trans "إلى:" %}</strong> <EMAIL></div>
                                    <div><strong>{% trans "الموضوع:" %}</strong> <span id="email-subject-preview">{{ invoice_setting.email_subject_template|default:"فاتورة رقم {invoice_number}" }}</span></div>
                                </div>
                            </div>

                            <div class="email-body p-3 border rounded mb-3" style="min-height: 150px;">
                                <div id="email-body-preview" style="white-space: pre-line;">{{ invoice_setting.email_body_template|default:"مرحباً {customer_name},\n\nمرفق فاتورة المبيعات الخاصة بكم رقم {invoice_number}.\n\nشكراً لتعاملكم معنا.\n\n{company_name}" }}</div>
                            </div>

                            <div class="email-attachment p-2 bg-light rounded d-flex align-items-center">
                                <i class="fas fa-file-pdf text-danger me-2"></i>
                                <span>{% trans "فاتورة" %}-INV-1001.pdf</span>
                                <span class="ms-2 text-muted">(215 KB)</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Logs Tab -->
    <div class="tab-pane fade" id="logs" role="tabpanel" aria-labelledby="logs-tab">
        <div class="row">
            <div class="col-lg-12">
                <div class="card shadow mb-4">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">{% trans "سجل تغييرات إعدادات الفاتورة" %}</h6>
                        <div>
                            <button type="button" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-download me-1"></i> {% trans "تصدير السجل" %}
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <label for="logsPerPage" class="me-2">{% trans "عرض" %}</label>
                                <select id="logsPerPage" class="form-select form-select-sm d-inline-block" style="width: auto;">
                                    <option value="5">5</option>
                                    <option value="10" selected>10</option>
                                    <option value="15">15</option>
                                    <option value="20">20</option>
                                    <option value="25">25</option>
                                    <option value="50">50</option>
                                    <option value="-1">{% trans "الكل" %}</option>
                                </select>
                                <span class="ms-2">{% trans "عناصر" %}</span>
                            </div>
                            <div>
                                <input type="text" id="logsSearch" class="form-control form-control-sm" placeholder="{% trans 'بحث...' %}" style="width: 200px;">
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-bordered table-hover" id="logsTable">
                                <thead class="table-light">
                                    <tr>
                                        <th>{% trans "التاريخ والوقت" %}</th>
                                        <th>{% trans "المستخدم" %}</th>
                                        <th>{% trans "الحقل" %}</th>
                                        <th>{% trans "القيمة القديمة" %}</th>
                                        <th>{% trans "القيمة الجديدة" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for log in change_logs %}
                                        {% for change in log.get_changes_dict %}
                                            <tr>
                                                <td>{{ log.created_at|date:"Y-m-d H:i:s" }}</td>
                                                <td>{{ log.user.username|default:"غير معروف" }}</td>
                                                <td>{{ change.field }}</td>
                                                <td>{{ change.old_value|default:"-" }}</td>
                                                <td>{{ change.new_value|default:"-" }}</td>
                                            </tr>
                                        {% empty %}
                                            <tr>
                                                <td>{{ log.created_at|date:"Y-m-d H:i:s" }}</td>
                                                <td>{{ log.user.username|default:"غير معروف" }}</td>
                                                <td colspan="3">{% trans "تم تحديث الإعدادات" %}</td>
                                            </tr>
                                        {% endfor %}
                                    {% empty %}
                                        <tr>
                                            <td colspan="5" class="text-center">{% trans "لا توجد سجلات تغيير" %}</td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div id="logsInfo" class="text-muted small">
                                {% trans "عرض" %} <span id="logsShowing">1-10</span> {% trans "من" %} <span id="logsTotal">{{ change_logs|length }}</span> {% trans "سجل" %}
                            </div>
                            <div class="pagination-container">
                                <ul class="pagination pagination-sm" id="logsPagination">
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" id="logsPrevious">{% trans "السابق" %}</a>
                                    </li>
                                    <li class="page-item active">
                                        <a class="page-link" href="#">1</a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="#" id="logsNext">{% trans "التالي" %}</a>
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <div class="mt-3">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-1"></i> {% trans "يتم تسجيل جميع التغييرات التي تتم على إعدادات الفاتورة تلقائياً." %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>
</div>
<!-- Modal for Test Email -->
<div class="modal fade" id="testEmailModal" tabindex="-1" aria-labelledby="testEmailModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="testEmailModalLabel">{% trans "اختبار إرسال البريد الإلكتروني" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'settings_app:invoice_settings' %}">
                {% csrf_token %}
                <input type="hidden" name="action" value="test_email">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="test_email" class="form-label">{% trans "عنوان البريد الإلكتروني للاختبار" %}</label>
                        <input type="email" class="form-control" id="test_email" name="test_email" required>
                        <div class="form-text">{% trans "سيتم إرسال بريد إلكتروني تجريبي إلى هذا العنوان." %}</div>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-1"></i> {% trans "سيتم استخدام قالب البريد الإلكتروني المحدد في الإعدادات." %}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane me-1"></i> {% trans "إرسال" %}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal for Reset Sequence -->
<div class="modal fade" id="resetSequenceModal" tabindex="-1" aria-labelledby="resetSequenceModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="resetSequenceModalLabel">{% trans "إعادة تعيين تسلسل أرقام الفواتير" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'settings_app:invoice_settings' %}">
                {% csrf_token %}
                <input type="hidden" name="action" value="reset_sequence">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="reset_number" class="form-label">{% trans "الرقم الجديد" %}</label>
                        <input type="number" class="form-control" id="reset_number" name="reset_number" value="1001" min="1" required>
                        <div class="form-text">{% trans "سيتم استخدام هذا الرقم كبداية جديدة لتسلسل أرقام الفواتير." %}</div>
                    </div>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-1"></i> {% trans "تحذير: إعادة تعيين تسلسل أرقام الفواتير قد يؤدي إلى تكرار الأرقام. تأكد من اختيار رقم بداية مناسب." %}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-redo-alt me-1"></i> {% trans "إعادة تعيين" %}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const prefixInput = document.getElementById('prefix');
        const nextNumberInput = document.getElementById('next_number');
        const invoicePreview = document.getElementById('invoice_preview');
        const invoiceNumberPreviews = document.querySelectorAll('.invoice-number-preview');
        const footerText = document.getElementById('footer_text');
        const termsAndConditions = document.getElementById('terms_and_conditions');
        const footerPreview = document.querySelector('.footer-preview');
        const termsPreview = document.querySelector('.terms-preview');
        const paperSizeSelect = document.getElementById('paper_size');
        const paperSizeLabel = document.querySelector('.paper-size-label');

        // Logs table pagination and filtering
        const logsTable = document.getElementById('logsTable');
        const logsPerPage = document.getElementById('logsPerPage');
        const logsSearch = document.getElementById('logsSearch');
        const logsInfo = document.getElementById('logsInfo');
        const logsShowing = document.getElementById('logsShowing');
        const logsTotal = document.getElementById('logsTotal');
        const logsPagination = document.getElementById('logsPagination');
        const logsPrevious = document.getElementById('logsPrevious');
        const logsNext = document.getElementById('logsNext');

        // Update invoice number preview
        function updateInvoiceNumberPreview() {
            const prefix = prefixInput.value;
            const nextNumber = nextNumberInput.value;
            const invoiceNumber = prefix + nextNumber;

            invoicePreview.textContent = invoiceNumber;
            invoiceNumberPreviews.forEach(function(preview) {
                preview.textContent = invoiceNumber;
            });
        }

        // Update footer preview
        function updateFooterPreview() {
            if (footerPreview) {
                footerPreview.textContent = footerText.value;
            }
        }

        // Update terms preview
        function updateTermsPreview() {
            if (termsPreview && termsPreview.querySelector('p')) {
                termsPreview.querySelector('p').textContent = termsAndConditions.value;
            }
        }

        // Update paper size preview
        function updatePaperSizePreview() {
            if (paperSizeSelect && paperSizeLabel) {
                const selectedOption = paperSizeSelect.options[paperSizeSelect.selectedIndex];
                paperSizeLabel.textContent = selectedOption.text;

                // Update paper preview dimensions based on selection
                const paperPreview = document.querySelector('.paper-preview');
                if (paperPreview) {
                    // Reset classes
                    paperPreview.classList.remove('a4', 'a5', 'letter', 'thermal');
                    // Add selected class
                    paperPreview.classList.add(paperSizeSelect.value);
                }
            }
        }

        // Add event listeners
        if (prefixInput && nextNumberInput) {
            prefixInput.addEventListener('input', updateInvoiceNumberPreview);
            nextNumberInput.addEventListener('input', updateInvoiceNumberPreview);
        }

        if (footerText && footerPreview) {
            footerText.addEventListener('input', updateFooterPreview);
        }

        if (termsAndConditions && termsPreview) {
            termsAndConditions.addEventListener('input', updateTermsPreview);
        }

        if (paperSizeSelect) {
            paperSizeSelect.addEventListener('change', updatePaperSizePreview);
            // Initialize paper size preview
            updatePaperSizePreview();
        }

        // Toggle display options
        const showLogoCheckbox = document.getElementById('show_logo');
        const showTaxCheckbox = document.getElementById('show_tax');
        const showCustomerInfoCheckbox = document.getElementById('show_customer_info');

        if (showLogoCheckbox) {
            showLogoCheckbox.addEventListener('change', function() {
                const logoPreview = document.querySelector('.invoice-preview img').parentElement;
                if (logoPreview) {
                    logoPreview.style.display = this.checked ? 'block' : 'none';
                }
            });
        }

        if (showTaxCheckbox) {
            showTaxCheckbox.addEventListener('change', function() {
                const taxRow = document.querySelector('.invoice-preview tfoot tr:nth-child(2)');
                if (taxRow) {
                    taxRow.style.display = this.checked ? 'table-row' : 'none';
                }
            });
        }

        if (showCustomerInfoCheckbox) {
            showCustomerInfoCheckbox.addEventListener('change', function() {
                const customerInfoSection = document.querySelector('.invoice-preview .row.mb-4');
                if (customerInfoSection) {
                    customerInfoSection.style.display = this.checked ? 'flex' : 'none';
                }
            });
        }

        // Logs table pagination and filtering functionality
        if (logsTable && logsPerPage && logsSearch) {
            const rows = Array.from(logsTable.querySelectorAll('tbody tr'));
            let currentPage = 1;
            let itemsPerPage = parseInt(logsPerPage.value);
            let filteredRows = [...rows];

            // Initialize pagination
            function initPagination() {
                updatePagination();
                updateTable();
            }

            // Update pagination controls
            function updatePagination() {
                // Clear existing page links except first, previous, and next
                const pageLinks = logsPagination.querySelectorAll('li:not(:first-child):not(:last-child)');
                pageLinks.forEach(link => {
                    if (!link.classList.contains('prev-page') && !link.classList.contains('next-page')) {
                        link.remove();
                    }
                });

                // Calculate total pages
                const totalPages = itemsPerPage === -1 ? 1 : Math.ceil(filteredRows.length / itemsPerPage);

                // Update info text
                if (filteredRows.length === 0) {
                    logsShowing.textContent = '0-0';
                } else if (itemsPerPage === -1) {
                    logsShowing.textContent = `1-${filteredRows.length}`;
                } else {
                    const start = (currentPage - 1) * itemsPerPage + 1;
                    const end = Math.min(start + itemsPerPage - 1, filteredRows.length);
                    logsShowing.textContent = `${start}-${end}`;
                }
                logsTotal.textContent = filteredRows.length;

                // Enable/disable previous button
                if (currentPage === 1) {
                    logsPrevious.parentElement.classList.add('disabled');
                } else {
                    logsPrevious.parentElement.classList.remove('disabled');
                }

                // Create page links
                const nextPageItem = logsNext.parentElement;
                for (let i = 1; i <= totalPages; i++) {
                    const pageItem = document.createElement('li');
                    pageItem.classList.add('page-item');
                    if (i === currentPage) {
                        pageItem.classList.add('active');
                    }

                    const pageLink = document.createElement('a');
                    pageLink.classList.add('page-link');
                    pageLink.href = '#';
                    pageLink.textContent = i;
                    pageLink.addEventListener('click', function(e) {
                        e.preventDefault();
                        currentPage = i;
                        updatePagination();
                        updateTable();
                    });

                    pageItem.appendChild(pageLink);
                    logsPagination.insertBefore(pageItem, nextPageItem);
                }

                // Enable/disable next button
                if (currentPage === totalPages || totalPages === 0) {
                    logsNext.parentElement.classList.add('disabled');
                } else {
                    logsNext.parentElement.classList.remove('disabled');
                }
            }

            // Update table with current page and filter
            function updateTable() {
                // Hide all rows
                rows.forEach(row => {
                    row.style.display = 'none';
                });

                // Show filtered rows for current page
                if (itemsPerPage === -1) {
                    // Show all filtered rows
                    filteredRows.forEach(row => {
                        row.style.display = '';
                    });
                } else {
                    // Show only rows for current page
                    const start = (currentPage - 1) * itemsPerPage;
                    const end = Math.min(start + itemsPerPage, filteredRows.length);

                    for (let i = start; i < end; i++) {
                        if (filteredRows[i]) {
                            filteredRows[i].style.display = '';
                        }
                    }
                }
            }

            // Filter rows based on search text
            function filterRows() {
                const searchText = logsSearch.value.toLowerCase();

                if (searchText === '') {
                    filteredRows = [...rows];
                } else {
                    filteredRows = rows.filter(row => {
                        return Array.from(row.cells).some(cell =>
                            cell.textContent.toLowerCase().includes(searchText)
                        );
                    });
                }

                currentPage = 1;
                updatePagination();
                updateTable();
            }

            // Event listeners
            logsPerPage.addEventListener('change', function() {
                itemsPerPage = parseInt(this.value);
                currentPage = 1;
                updatePagination();
                updateTable();
            });

            logsSearch.addEventListener('input', filterRows);

            logsPrevious.addEventListener('click', function(e) {
                e.preventDefault();
                if (currentPage > 1) {
                    currentPage--;
                    updatePagination();
                    updateTable();
                }
            });

            logsNext.addEventListener('click', function(e) {
                e.preventDefault();
                const totalPages = itemsPerPage === -1 ? 1 : Math.ceil(filteredRows.length / itemsPerPage);
                if (currentPage < totalPages) {
                    currentPage++;
                    updatePagination();
                    updateTable();
                }
            });

            // Initialize
            initPagination();
        }
    });

    // Test print function
    function printTest() {
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <html dir="rtl">
            <head>
                <title>اختبار الطباعة</title>
                <style>
                    body {
                        font-family: 'Tajawal', Arial, sans-serif;
                        padding: 20px;
                    }
                    .test-page {
                        border: 1px dashed #ccc;
                        padding: 20px;
                        max-width: 800px;
                        margin: 0 auto;
                    }
                    .header {
                        text-align: center;
                        margin-bottom: 20px;
                    }
                    .content {
                        margin-bottom: 20px;
                    }
                    .footer {
                        text-align: center;
                        font-size: 12px;
                        color: #666;
                        margin-top: 30px;
                    }
                </style>
            </head>
            <body>
                <div class="test-page">
                    <div class="header">
                        <h1>صفحة اختبار الطباعة</h1>
                        <p>هذه صفحة لاختبار إعدادات الطباعة</p>
                    </div>
                    <div class="content">
                        <p>يمكنك استخدام هذه الصفحة للتحقق من:</p>
                        <ul>
                            <li>حجم الورق المحدد</li>
                            <li>الهوامش</li>
                            <li>الخطوط</li>
                            <li>الألوان</li>
                        </ul>
                        <p>تأكد من أن جميع العناصر تظهر بشكل صحيح في النسخة المطبوعة.</p>
                    </div>
                    <div class="footer">
                        تم إنشاء هذه الصفحة للاختبار فقط - ${new Date().toLocaleString('ar-SA')}
                    </div>
                </div>
            </body>
            </html>
        `);
        printWindow.document.close();
        setTimeout(() => {
            printWindow.print();
        }, 500);
    }
</script>

<style>
    .paper-preview {
        border: 1px solid #ddd;
        background-color: white;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        margin: 0 auto;
        position: relative;
    }

    .paper-preview.a4 {
        width: 210px;
        height: 297px;
        max-height: 250px;
        aspect-ratio: 210/297;
    }

    .paper-preview.a5 {
        width: 148px;
        height: 210px;
        max-height: 200px;
        aspect-ratio: 148/210;
    }

    .paper-preview.letter {
        width: 216px;
        height: 279px;
        max-height: 250px;
        aspect-ratio: 216/279;
    }

    .paper-preview.thermal {
        width: 80px;
        height: 200px;
        max-height: 200px;
    }

    .paper-content {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 90%;
    }

    .paper-size-label {
        font-size: 24px;
        font-weight: bold;
    }
</style>
{% endblock %}
