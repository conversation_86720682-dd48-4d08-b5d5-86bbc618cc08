{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "تنبيهات المبيعات" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
<style>
    .alert-card {
        border-right: 5px solid;
        transition: all 0.3s;
    }

    .alert-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    .alert-card.overdue {
        border-right-color: #e74a3b;
    }

    .alert-card.pending {
        border-right-color: #f6c23e;
    }

    .alert-card.low-stock {
        border-right-color: #36b9cc;
    }

    .alert-badge {
        position: absolute;
        top: 10px;
        left: 10px;
    }

    .alert-days {
        font-size: 0.8rem;
        padding: 0.2rem 0.5rem;
        border-radius: 10px;
        background-color: #f8f9fa;
        display: inline-block;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0 text-gray-800">{% trans "تنبيهات المبيعات" %}</h1>
    <a href="{% url 'sales:index' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-1"></i> {% trans "العودة إلى المبيعات" %}
    </a>
</div>

{% if messages %}
    {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    {% endfor %}
{% endif %}

<!-- Alert Categories -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-danger shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                            {% trans "فواتير متأخرة" %}</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ overdue_count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar-times fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            {% trans "فواتير معلقة" %}</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ pending_count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            {% trans "منتجات منخفضة المخزون" %}</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ low_stock_count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-box-open fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            {% trans "إجمالي المبالغ المعلقة" %}</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ pending_amount|floatformat:2 }} ر.س</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-money-bill-wave fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Overdue Invoices -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">{% trans "الفواتير المتأخرة" %}</h6>
    </div>
    <div class="card-body">
        {% if overdue_sales %}
        <div class="row">
            {% for sale in overdue_sales %}
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card alert-card overdue h-100">
                    <span class="badge bg-danger alert-badge">{% trans "متأخرة" %}</span>
                    <div class="card-body">
                        <h5 class="card-title">
                            <a href="{% url 'sales:view_sale' sale.id %}" class="text-primary">{{ sale.invoice_number }}</a>
                        </h5>
                        <h6 class="card-subtitle mb-2 text-muted">{{ sale.customer.name }}</h6>
                        <p class="card-text">
                            <strong>{% trans "تاريخ البيع:" %}</strong> {{ sale.date|date:"Y-m-d" }}<br>
                            <strong>{% trans "المبلغ الإجمالي:" %}</strong> {{ sale.total_amount|floatformat:2 }} ر.س<br>
                            <strong>{% trans "المبلغ المتبقي:" %}</strong> {{ sale.remaining_amount|floatformat:2 }} ر.س
                        </p>
                        <div class="alert-days text-danger">
                            <i class="fas fa-exclamation-circle me-1"></i>
                            {% trans "متأخرة بـ" %} {{ sale.days_overdue }} {% trans "يوم" %}
                        </div>
                    </div>
                    <div class="card-footer bg-transparent">
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'sales:view_sale' sale.id %}" class="btn btn-sm btn-primary">
                                <i class="fas fa-eye me-1"></i> {% trans "عرض" %}
                            </a>
                            <button type="button" class="btn btn-sm btn-success" data-bs-toggle="modal" data-bs-target="#addPaymentModal" data-sale-id="{{ sale.id }}" data-amount="{{ sale.remaining_amount }}">
                                <i class="fas fa-plus me-1"></i> {% trans "إضافة دفعة" %}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="text-center py-4">
            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
            <p>{% trans "لا توجد فواتير متأخرة" %}</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- Pending Invoices -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">{% trans "الفواتير المعلقة" %}</h6>
    </div>
    <div class="card-body">
        {% if pending_sales %}
        <div class="table-responsive">
            <table class="table table-bordered" id="pendingTable" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th>{% trans "رقم الفاتورة" %}</th>
                        <th>{% trans "العميل" %}</th>
                        <th>{% trans "التاريخ" %}</th>
                        <th>{% trans "المبلغ الإجمالي" %}</th>
                        <th>{% trans "المبلغ المدفوع" %}</th>
                        <th>{% trans "المبلغ المتبقي" %}</th>
                        <th>{% trans "الإجراءات" %}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for sale in pending_sales %}
                    <tr>
                        <td>
                            <a href="{% url 'sales:view_sale' sale.id %}" class="text-primary">{{ sale.invoice_number }}</a>
                        </td>
                        <td>{{ sale.customer.name }}</td>
                        <td>{{ sale.date|date:"Y-m-d" }}</td>
                        <td>{{ sale.total_amount|floatformat:2 }} ر.س</td>
                        <td>{{ sale.paid_amount|floatformat:2 }} ر.س</td>
                        <td>{{ sale.remaining_amount|floatformat:2 }} ر.س</td>
                        <td>
                            <div class="btn-group">
                                <a href="{% url 'sales:view_sale' sale.id %}" class="btn btn-sm btn-primary">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <button type="button" class="btn btn-sm btn-success" data-bs-toggle="modal" data-bs-target="#addPaymentModal" data-sale-id="{{ sale.id }}" data-amount="{{ sale.remaining_amount }}">
                                    <i class="fas fa-plus"></i>
                                </button>
                                <a href="{% url 'sales:invoice' sale.id %}" class="btn btn-sm btn-info">
                                    <i class="fas fa-file-invoice"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-4">
            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
            <p>{% trans "لا توجد فواتير معلقة" %}</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- Low Stock Products -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">{% trans "المنتجات منخفضة المخزون" %}</h6>
    </div>
    <div class="card-body">
        {% if low_stock_products %}
        <div class="table-responsive">
            <table class="table table-bordered" id="lowStockTable" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th>{% trans "كود المنتج" %}</th>
                        <th>{% trans "اسم المنتج" %}</th>
                        <th>{% trans "الفئة" %}</th>
                        <th>{% trans "الكمية المتوفرة" %}</th>
                        <th>{% trans "الحد الأدنى" %}</th>
                        <th>{% trans "الإجراءات" %}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for product in low_stock_products %}
                    <tr>
                        <td>{{ product.code }}</td>
                        <td>{{ product.name }}</td>
                        <td>{{ product.category.name }}</td>
                        <td>
                            <span class="badge bg-danger">{{ product.quantity }}</span>
                        </td>
                        <td>{{ product.min_stock }}</td>
                        <td>
                            <a href="{% url 'inventory:view_product' product.id %}" class="btn btn-sm btn-primary">
                                <i class="fas fa-eye me-1"></i> {% trans "عرض" %}
                            </a>
                            <a href="{% url 'purchases:new_purchase' %}?product={{ product.id }}" class="btn btn-sm btn-success">
                                <i class="fas fa-shopping-cart me-1"></i> {% trans "شراء" %}
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-4">
            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
            <p>{% trans "جميع المنتجات متوفرة بكميات كافية" %}</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- Add Payment Modal -->
<div class="modal fade" id="addPaymentModal" tabindex="-1" aria-labelledby="addPaymentModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addPaymentModalLabel">{% trans "إضافة دفعة" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="" id="paymentForm">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="payment_amount" class="form-label required-field">{% trans "المبلغ" %}</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="payment_amount" name="amount" step="0.01" min="0.01" required>
                            <span class="input-group-text">ر.س</span>
                        </div>
                        <div class="form-text" id="remainingText"></div>
                    </div>
                    <div class="mb-3">
                        <label for="payment_date" class="form-label required-field">{% trans "تاريخ الدفع" %}</label>
                        <input type="date" class="form-control" id="payment_date" name="date" value="{{ today|date:'Y-m-d' }}" required>
                    </div>
                    <div class="mb-3">
                        <label for="payment_method" class="form-label required-field">{% trans "طريقة الدفع" %}</label>
                        <select class="form-select" id="payment_method" name="payment_method" required>
                            <option value="cash">{% trans "نقدي" %}</option>
                            <option value="card">{% trans "بطاقة ائتمان" %}</option>
                            <option value="transfer">{% trans "تحويل بنكي" %}</option>
                            <option value="check">{% trans "شيك" %}</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="reference_number" class="form-label">{% trans "رقم المرجع" %}</label>
                        <input type="text" class="form-control" id="reference_number" name="reference_number">
                        <div class="form-text">{% trans "مطلوب لطرق الدفع: بطاقة ائتمان، تحويل بنكي، شيك" %}</div>
                    </div>
                    <div class="mb-3">
                        <label for="payment_notes" class="form-label">{% trans "ملاحظات" %}</label>
                        <textarea class="form-control" id="payment_notes" name="notes" rows="2"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                    <button type="submit" class="btn btn-primary">{% trans "حفظ الدفعة" %}</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
<script>
    $(document).ready(function() {
        // Initialize DataTables
        $('#pendingTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json",
                "lengthMenu": "إظهار _MENU_ من العناصر",
                "info": "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل"
            },
            "pageLength": 10,
            "lengthMenu": [[5, 10, 15, 20, 25, 50, -1], [5, 10, 15, 20, 25, 50, "الكل"]],
            "dom": 'lfrtip'
        });

        $('#lowStockTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json",
                "lengthMenu": "إظهار _MENU_ من العناصر",
                "info": "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل"
            },
            "pageLength": 10,
            "lengthMenu": [[5, 10, 15, 20, 25, 50, -1], [5, 10, 15, 20, 25, 50, "الكل"]],
            "dom": 'lfrtip'
        });

        // Handle payment modal
        $('#addPaymentModal').on('show.bs.modal', function (event) {
            var button = $(event.relatedTarget);
            var saleId = button.data('sale-id');
            var amount = button.data('amount');
            var modal = $(this);

            // Update form action
            modal.find('#paymentForm').attr('action', '/sales/add-payment/' + saleId + '/');

            // Set amount
            modal.find('#payment_amount').val(amount).attr('max', amount);
            modal.find('#remainingText').text('{% trans "المبلغ المتبقي:" %} ' + amount + ' ر.س');
        });

        // Payment Method Change
        $('#payment_method').change(function() {
            var method = $(this).val();
            if (method === 'card' || method === 'transfer' || method === 'check') {
                $('#reference_number').attr('required', true);
            } else {
                $('#reference_number').attr('required', false);
            }
        });
    });
</script>
{% endblock %}
