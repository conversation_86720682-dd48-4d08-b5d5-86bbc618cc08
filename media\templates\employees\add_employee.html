{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "إضافة موظف جديد" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{% trans "إضافة موظف جديد" %}</h1>
        <a href="{% url 'employees:index' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> {% trans "العودة إلى قائمة الموظفين" %}
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">{% trans "معلومات الموظف" %}</h6>
        </div>
        <div class="card-body">
            <form method="post" enctype="multipart/form-data">
                {% csrf_token %}

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="first_name">{% trans "الاسم الأول" %} *</label>
                            <input type="text" class="form-control" id="first_name" name="first_name" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="last_name">{% trans "الاسم الأخير" %} *</label>
                            <input type="text" class="form-control" id="last_name" name="last_name" required>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="username">{% trans "اسم المستخدم" %} *</label>
                            <input type="text" class="form-control" id="username" name="username" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="email">{% trans "البريد الإلكتروني" %} *</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="password">{% trans "كلمة المرور" %} *</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="password_confirm">{% trans "تأكيد كلمة المرور" %} *</label>
                            <input type="password" class="form-control" id="password_confirm" name="password_confirm" required>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="employee_id">{% trans "رقم الموظف" %} *</label>
                            <input type="text" class="form-control" id="employee_id" name="employee_id" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="phone">{% trans "رقم الهاتف" %} *</label>
                            <input type="text" class="form-control" id="phone" name="phone" required>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="department">{% trans "القسم" %} *</label>
                            <select class="form-control" id="department" name="department" required>
                                <option value="">{% trans "اختر القسم" %}</option>
                                {% for department in departments %}
                                <option value="{{ department.id }}">{{ department.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="position">{% trans "المنصب" %} *</label>
                            <select class="form-control" id="position" name="position" required>
                                <option value="">{% trans "اختر المنصب" %}</option>
                                {% for position in positions %}
                                <option value="{{ position.id }}" data-department="{{ position.department.id }}">{{ position.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="role">{% trans "الدور الوظيفي" %}</label>
                            <select class="form-control" id="role" name="role">
                                <option value="">{% trans "اختر الدور الوظيفي" %}</option>
                                {% for role in roles %}
                                <option value="{{ role.id }}">{{ role.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="gender">{% trans "الجنس" %} *</label>
                            <select class="form-control" id="gender" name="gender" required>
                                <option value="">{% trans "اختر الجنس" %}</option>
                                <option value="male">{% trans "ذكر" %}</option>
                                <option value="female">{% trans "أنثى" %}</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="date_of_birth">{% trans "تاريخ الميلاد" %}</label>
                            <input type="date" class="form-control" id="date_of_birth" name="date_of_birth">
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="hire_date">{% trans "تاريخ التوظيف" %} *</label>
                            <input type="date" class="form-control" id="hire_date" name="hire_date" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="salary">{% trans "الراتب" %} *</label>
                            <input type="number" step="0.01" class="form-control" id="salary" name="salary" required>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="national_id">{% trans "رقم الهوية" %}</label>
                            <input type="text" class="form-control" id="national_id" name="national_id">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="image">{% trans "الصورة الشخصية" %}</label>
                            <input type="file" class="form-control-file" id="image" name="image">
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="address">{% trans "العنوان" %}</label>
                    <textarea class="form-control" id="address" name="address" rows="3"></textarea>
                </div>

                <div class="form-group">
                    <label for="notes">{% trans "ملاحظات" %}</label>
                    <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> {% trans "حفظ" %}
                    </button>
                    <a href="{% url 'employees:index' %}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> {% trans "إلغاء" %}
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // تصفية المناصب بناءً على القسم المحدد
        $('#department').on('change', function() {
            var departmentId = $(this).val();
            console.log('تم تغيير القسم إلى: ' + departmentId);

            // إخفاء جميع المناصب
            $('#position option').each(function() {
                if ($(this).val() === '') {
                    $(this).show(); // إظهار خيار "اختر المنصب"
                } else {
                    var positionDept = $(this).data('department');
                    console.log('منصب: ' + $(this).text() + ', قسم: ' + positionDept);

                    if (positionDept == departmentId) {
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                }
            });

            // إعادة تعيين المنصب المحدد
            $('#position').val('');
        });

        // تعيين تاريخ اليوم كتاريخ توظيف افتراضي
        var today = new Date().toISOString().split('T')[0];
        $('#hire_date').val(today);

        // تشغيل تغيير القسم عند تحميل الصفحة
        $('#department').trigger('change');
    });
</script>
{% endblock %}
