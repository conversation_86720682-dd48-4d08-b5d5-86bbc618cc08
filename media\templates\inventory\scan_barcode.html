{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "مسح الباركود" %}{% endblock %}

{% block styles %}
<style>
    .scanner-container {
        max-width: 600px;
        margin: 20px auto;
        padding: 20px;
        border: 1px solid #ddd;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        background-color: #fff;
    }
    #barcode-scanner-video {
        width: 100%;
        border: 1px solid #ccc;
        border-radius: 4px;
    }
    .scan-results {
        margin-top: 20px;
    }
    .scan-results h5 {
        margin-bottom: 10px;
    }
    .product-info {
        padding: 15px;
        border: 1px solid #eee;
        border-radius: 4px;
        background-color: #f9f9f9;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{% url 'dashboard:index' %}">{% trans "لوحة التحكم" %}</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'inventory:index' %}">{% trans "المخزون" %}</a></li>
                        <li class="breadcrumb-item active">{% trans "مسح الباركود" %}</li>
                    </ol>
                </div>
                <h4 class="page-title">{% trans "مسح الباركود" %}</h4>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="scanner-container">
                <h5 class="text-center mb-3">{% trans "وجه الكاميرا نحو الباركود" %}</h5>
                <div id="barcode-scanner">
                    <video id="barcode-scanner-video" autoplay playsinline></video>
                </div>
                <div class="text-center mt-3">
                    <button id="start-scan-btn" class="btn btn-primary me-2">{% trans "بدء المسح" %}</button>
                    <button id="stop-scan-btn" class="btn btn-danger" style="display: none;">{% trans "إيقاف المسح" %}</button>
                </div>

                <div class="scan-results mt-4">
                    <h5>{% trans "نتائج المسح:" %}</h5>
                    <div id="scanned-barcode-result">
                        <p class="text-muted">{% trans "لم يتم مسح أي باركود بعد." %}</p>
                    </div>
                    <div id="product-details-result" class="mt-3" style="display: none;">
                        <h5>{% trans "تفاصيل المنتج:" %}</h5>
                        <div class="product-info">
                            <p><strong>{% trans "الاسم:" %}</strong> <span id="product-name"></span></p>
                            <p><strong>{% trans "السعر:" %}</strong> <span id="product-price"></span></p>
                            <p><strong>{% trans "الكمية المتوفرة:" %}</strong> <span id="product-quantity"></span></p>
                            <a href="#" id="product-link" class="btn btn-sm btn-info">{% trans "عرض المنتج" %}</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/@zxing/library@latest/umd/index.min.js"></script>
<script>
    $(document).ready(function() {
        const codeReader = new ZXing.BrowserMultiFormatReader();
        let selectedDeviceId;
        const videoElement = document.getElementById('barcode-scanner-video');
        const startScanButton = $('#start-scan-btn');
        const stopScanButton = $('#stop-scan-btn');
        const scannedBarcodeResult = $('#scanned-barcode-result');
        const productDetailsResult = $('#product-details-result');

        codeReader.listVideoInputDevices()
            .then((videoInputDevices) => {
                if (videoInputDevices.length > 0) {
                    selectedDeviceId = videoInputDevices[0].deviceId;
                } else {
                    toastr.error('{% trans "لم يتم العثور على كاميرا." %}');
                    startScanButton.prop('disabled', true);
                }
            })
            .catch(err => {
                console.error(err);
                toastr.error('{% trans "خطأ في الوصول إلى الكاميرا." %}');
            });

        startScanButton.click(function () {
            if (!selectedDeviceId) {
                toastr.warning('{% trans "يرجى تحديد جهاز كاميرا أولاً." %}');
                return;
            }
            scannedBarcodeResult.html('<p class="text-muted">{% trans "جاري البحث عن باركود..." %}</p>');
            productDetailsResult.hide();
            codeReader.decodeFromVideoDevice(selectedDeviceId, 'barcode-scanner-video', (result, err) => {
                if (result) {
                    console.log(result);
                    scannedBarcodeResult.html(`<p class="text-success"><strong>{% trans "الباركود الممسوح:" %}</strong> ${result.text}</p>`);
                    fetchProductDetails(result.text);
                    codeReader.reset(); // لإيقاف المسح بعد العثور على نتيجة
                    startScanButton.show();
                    stopScanButton.hide();
                }
                if (err && !(err instanceof ZXing.NotFoundException)) {
                    console.error(err);
                    scannedBarcodeResult.html(`<p class="text-danger">{% trans "خطأ أثناء المسح:" %} ${err}</p>`);
                }
            });
            startScanButton.hide();
            stopScanButton.show();
        });

        stopScanButton.click(function () {
            codeReader.reset();
            scannedBarcodeResult.html('<p class="text-muted">{% trans "تم إيقاف المسح." %}</p>');
            productDetailsResult.hide();
            startScanButton.show();
            stopScanButton.hide();
        });

        function fetchProductDetails(barcodeNumber) {
            $.ajax({
                url: "{% url 'inventory:barcode:scan_barcode' %}", // سيتم استخدام نفس الـ URL ولكن مع POST لبيانات الباركود
                type: "POST",
                data: {
                    'barcode_number': barcodeNumber,
                    'csrfmiddlewaretoken': '{{ csrf_token }}'
                },
                success: function(response) {
                    if (response.product) {
                        $('#product-name').text(response.product.name);
                        $('#product-price').text(response.product.price);
                        $('#product-quantity').text(response.product.quantity);
                        // افترض أن لديك مسار URL لعرض تفاصيل المنتج
                        // $('#product-link').attr('href', `/inventory/product/${response.product.id}/`);
                        productDetailsResult.show();

                        // إرسال بيانات المنتج إلى نافذة نقطة البيع (إذا كانت مفتوحة)
                        if (window.opener) {
                            window.opener.postMessage({
                                type: 'barcode_scanned',
                                product: response.product
                            }, '*');
                        }

                    } else if (response.error) {
                        toastr.error(response.error);
                        scannedBarcodeResult.append(`<p class="text-warning">${response.error}</p>`);
                    } else {
                        toastr.info('{% trans "لم يتم العثور على منتج مرتبط بهذا الباركود." %}');
                         scannedBarcodeResult.append(`<p class="text-info">{% trans "لم يتم العثور على منتج مرتبط بهذا الباركود." %}</p>`);
                    }
                },
                error: function(xhr) {
                    toastr.error('{% trans "حدث خطأ أثناء جلب تفاصيل المنتج." %}');
                }
            });
        }
    });
</script>
{% endblock %}