{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "إدارة الأدوار والصلاحيات" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{% trans "إدارة الأدوار والصلاحيات" %}</h1>
        <div>
            <a href="{% url 'employees:roles' %}" class="btn btn-info">
                <i class="fas fa-sitemap"></i> {% trans "الأقسام والمناصب" %}
            </a>
            <a href="{% url 'employees:index' %}" class="btn btn-secondary">
                <i class="fas fa-users"></i> {% trans "الموظفين" %}
            </a>
        </div>
    </div>

    <div class="row">
        <!-- الأدوار الوظيفية -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "الأدوار الوظيفية" %}</h6>
                    <button class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addRoleModal">
                        <i class="fas fa-plus"></i> {% trans "إضافة دور" %}
                    </button>
                </div>
                <div class="card-body">
                    <div class="list-group" id="rolesList">
                        {% for role in roles %}
                        <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center role-item" data-role-id="{{ role.id }}">
                            <div>
                                <h6 class="mb-1">{{ role.name }}</h6>
                                <small class="text-muted">{{ role.description|default:"-"|truncatechars:50 }}</small>
                            </div>
                            <div>
                                {% if role.is_active %}
                                <span class="badge bg-success">{% trans "نشط" %}</span>
                                {% else %}
                                <span class="badge bg-danger">{% trans "غير نشط" %}</span>
                                {% endif %}
                            </div>
                        </a>
                        {% empty %}
                        <div class="text-center py-3">
                            <p>{% trans "لا توجد أدوار وظيفية" %}</p>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>

        <!-- تفاصيل الدور والصلاحيات -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary" id="roleDetailsTitle">{% trans "تفاصيل الدور الوظيفي" %}</h6>
                    <div id="roleActions" style="display: none;">
                        <button class="btn btn-sm btn-primary edit-role-btn">
                            <i class="fas fa-edit"></i> {% trans "تعديل" %}
                        </button>
                        <button class="btn btn-sm btn-danger delete-role-btn">
                            <i class="fas fa-trash"></i> {% trans "حذف" %}
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div id="roleDetailsPlaceholder" class="text-center py-5">
                        <p>{% trans "يرجى اختيار دور وظيفي من القائمة" %}</p>
                    </div>
                    
                    <div id="roleDetails" style="display: none;">
                        <form id="rolePermissionsForm">
                            {% csrf_token %}
                            <input type="hidden" id="roleId" name="role_id">
                            
                            <div class="mb-4">
                                <h5>{% trans "معلومات الدور" %}</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">{% trans "الاسم" %}</label>
                                            <input type="text" class="form-control" id="roleName" name="name" readonly>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">{% trans "الحالة" %}</label>
                                            <input type="text" class="form-control" id="roleStatus" readonly>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">{% trans "الوصف" %}</label>
                                    <textarea class="form-control" id="roleDescription" name="description" rows="2" readonly></textarea>
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <h5>{% trans "الصلاحيات" %}</h5>
                                <div class="alert alert-info">
                                    {% trans "حدد الصلاحيات التي يمتلكها هذا الدور الوظيفي" %}
                                </div>
                                
                                <div id="permissionsContainer">
                                    {% for category, perms in permissions_by_category.items %}
                                    <div class="card mb-3">
                                        <div class="card-header bg-light">
                                            <h6 class="mb-0">{{ category }}</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                {% for perm in perms %}
                                                <div class="col-md-6">
                                                    <div class="form-check mb-2">
                                                        <input class="form-check-input permission-checkbox" type="checkbox" id="perm_{{ perm.id }}" name="permissions" value="{{ perm.id }}" disabled>
                                                        <label class="form-check-label" for="perm_{{ perm.id }}">
                                                            {{ perm.name }}
                                                            <small class="d-block text-muted">{{ perm.description|default:"-" }}</small>
                                                        </label>
                                                    </div>
                                                </div>
                                                {% endfor %}
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                            
                            <div class="mb-3 text-end" id="savePermissionsContainer" style="display: none;">
                                <button type="button" class="btn btn-secondary cancel-edit-btn">
                                    <i class="fas fa-times"></i> {% trans "إلغاء" %}
                                </button>
                                <button type="button" class="btn btn-primary save-permissions-btn">
                                    <i class="fas fa-save"></i> {% trans "حفظ التغييرات" %}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- إضافة دور جديد Modal -->
<div class="modal fade" id="addRoleModal" tabindex="-1" aria-labelledby="addRoleModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addRoleModalLabel">{% trans "إضافة دور وظيفي جديد" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addRoleForm">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="newRoleName" class="form-label">{% trans "اسم الدور" %} *</label>
                        <input type="text" class="form-control" id="newRoleName" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="newRoleDescription" class="form-label">{% trans "الوصف" %}</label>
                        <textarea class="form-control" id="newRoleDescription" name="description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="newRoleActive" name="is_active" checked>
                            <label class="form-check-label" for="newRoleActive">
                                {% trans "نشط" %}
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                <button type="button" class="btn btn-primary" id="saveNewRole">{% trans "حفظ" %}</button>
            </div>
        </div>
    </div>
</div>

<!-- تعديل دور Modal -->
<div class="modal fade" id="editRoleModal" tabindex="-1" aria-labelledby="editRoleModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editRoleModalLabel">{% trans "تعديل دور وظيفي" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editRoleForm">
                    {% csrf_token %}
                    <input type="hidden" id="editRoleId" name="id">
                    <div class="mb-3">
                        <label for="editRoleName" class="form-label">{% trans "اسم الدور" %} *</label>
                        <input type="text" class="form-control" id="editRoleName" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="editRoleDescription" class="form-label">{% trans "الوصف" %}</label>
                        <textarea class="form-control" id="editRoleDescription" name="description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="editRoleActive" name="is_active">
                            <label class="form-check-label" for="editRoleActive">
                                {% trans "نشط" %}
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                <button type="button" class="btn btn-primary" id="updateRole">{% trans "حفظ التغييرات" %}</button>
            </div>
        </div>
    </div>
</div>

<!-- حذف دور Modal -->
<div class="modal fade" id="deleteRoleModal" tabindex="-1" aria-labelledby="deleteRoleModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteRoleModalLabel">{% trans "تأكيد الحذف" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>{% trans "هل أنت متأكد من حذف الدور الوظيفي" %}: <strong id="deleteRoleName"></strong>؟</p>
                <div class="alert alert-warning">
                    {% trans "سيتم إزالة هذا الدور من جميع الموظفين المرتبطين به." %}
                </div>
                <form id="deleteRoleForm">
                    {% csrf_token %}
                    <input type="hidden" id="deleteRoleId" name="id">
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteRole">{% trans "حذف" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // عرض تفاصيل الدور عند النقر عليه
        $('.role-item').click(function(e) {
            e.preventDefault();
            
            // إزالة التحديد من جميع العناصر
            $('.role-item').removeClass('active');
            
            // تحديد العنصر الحالي
            $(this).addClass('active');
            
            // الحصول على معرف الدور
            var roleId = $(this).data('role-id');
            
            // إظهار تفاصيل الدور
            loadRoleDetails(roleId);
        });
        
        // تحميل تفاصيل الدور
        function loadRoleDetails(roleId) {
            // هنا يجب إرسال طلب AJAX للحصول على تفاصيل الدور
            // للتبسيط، سنستخدم بيانات وهمية
            
            // إخفاء رسالة الاختيار وإظهار التفاصيل
            $('#roleDetailsPlaceholder').hide();
            $('#roleDetails').show();
            $('#roleActions').show();
            
            // تعيين معرف الدور
            $('#roleId').val(roleId);
            
            // تعيين عنوان التفاصيل
            var roleName = $('.role-item.active h6').text();
            $('#roleDetailsTitle').text(roleName);
            
            // تعيين بيانات الدور
            $('#roleName').val(roleName);
            $('#roleDescription').val($('.role-item.active small').text());
            
            // تعيين حالة الدور
            var isActive = $('.role-item.active .badge').hasClass('bg-success');
            $('#roleStatus').val(isActive ? '{% trans "نشط" %}' : '{% trans "غير نشط" %}');
            
            // تحديد الصلاحيات (هنا يجب تحديد الصلاحيات الفعلية للدور)
            $('.permission-checkbox').prop('checked', false);
            
            // للتجربة، سنحدد بعض الصلاحيات عشوائياً
            $('.permission-checkbox').each(function(index) {
                if (index % 3 === 0) {
                    $(this).prop('checked', true);
                }
            });
        }
        
        // تفعيل وضع التعديل للصلاحيات
        $('.edit-role-btn').click(function() {
            // تفعيل حقول الإدخال
            $('.permission-checkbox').prop('disabled', false);
            
            // إظهار أزرار الحفظ والإلغاء
            $('#savePermissionsContainer').show();
        });
        
        // إلغاء وضع التعديل
        $('.cancel-edit-btn').click(function() {
            // تعطيل حقول الإدخال
            $('.permission-checkbox').prop('disabled', true);
            
            // إخفاء أزرار الحفظ والإلغاء
            $('#savePermissionsContainer').hide();
            
            // إعادة تحميل تفاصيل الدور
            var roleId = $('#roleId').val();
            loadRoleDetails(roleId);
        });
        
        // حفظ التغييرات
        $('.save-permissions-btn').click(function() {
            // الحصول على الصلاحيات المحددة
            var selectedPermissions = [];
            $('.permission-checkbox:checked').each(function() {
                selectedPermissions.push($(this).val());
            });
            
            // هنا يجب إرسال طلب AJAX لحفظ التغييرات
            alert('سيتم حفظ الصلاحيات: ' + selectedPermissions.join(', '));
            
            // تعطيل حقول الإدخال
            $('.permission-checkbox').prop('disabled', true);
            
            // إخفاء أزرار الحفظ والإلغاء
            $('#savePermissionsContainer').hide();
        });
        
        // فتح نافذة تعديل الدور
        $('.edit-role-btn').click(function() {
            var roleId = $('#roleId').val();
            var roleName = $('#roleName').val();
            var roleDescription = $('#roleDescription').val();
            var isActive = $('#roleStatus').val() === '{% trans "نشط" %}';
            
            $('#editRoleId').val(roleId);
            $('#editRoleName').val(roleName);
            $('#editRoleDescription').val(roleDescription);
            $('#editRoleActive').prop('checked', isActive);
            
            $('#editRoleModal').modal('show');
        });
        
        // حفظ تعديلات الدور
        $('#updateRole').click(function() {
            // هنا يجب إرسال طلب AJAX لتحديث الدور
            alert('سيتم تحديث الدور');
            $('#editRoleModal').modal('hide');
        });
        
        // فتح نافذة حذف الدور
        $('.delete-role-btn').click(function() {
            var roleId = $('#roleId').val();
            var roleName = $('#roleName').val();
            
            $('#deleteRoleId').val(roleId);
            $('#deleteRoleName').text(roleName);
            
            $('#deleteRoleModal').modal('show');
        });
        
        // تأكيد حذف الدور
        $('#confirmDeleteRole').click(function() {
            // هنا يجب إرسال طلب AJAX لحذف الدور
            alert('سيتم حذف الدور');
            $('#deleteRoleModal').modal('hide');
            
            // إعادة تحميل الصفحة
            // window.location.reload();
        });
        
        // حفظ دور جديد
        $('#saveNewRole').click(function() {
            // هنا يجب إرسال طلب AJAX لإضافة دور جديد
            alert('سيتم إضافة دور جديد');
            $('#addRoleModal').modal('hide');
            
            // إعادة تحميل الصفحة
            // window.location.reload();
        });
    });
</script>
{% endblock %}
