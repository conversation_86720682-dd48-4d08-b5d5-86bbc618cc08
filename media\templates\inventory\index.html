{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "إدارة المخزون" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
<style>
    /* Modern Card Styles */
    .dashboard-card {
        border-radius: 10px;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.1) !important;
        transition: all 0.3s ease;
        height: 100%;
        overflow: hidden;
        border: none;
    }
    
    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1.5rem 0 rgba(58, 59, 69, 0.15) !important;
    }
    
    .dashboard-card .card-icon {
        font-size: 1.8rem;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        margin-bottom: 0.5rem;
        color: white;
    }
    
    /* Status Indicators */
    .status-available {
        color: #198754;
        font-weight: bold;
    }
    .status-low {
        color: #ffc107;
        font-weight: bold;
    }
    .status-out {
        color: #dc3545;
        font-weight: bold;
    }
    
    /* Product Image */
    .product-image-small {
        width: 50px;
        height: 50px;
        object-fit: contain;
        border-radius: 4px;
    }
    
    /* Action Buttons */
    .action-buttons .btn-group {
        display: flex;
        gap: 3px;
    }

    .action-buttons .btn {
        border-radius: 4px;
        padding: 0.25rem 0.5rem;
    }

    /* Product Detail Modal */
    #productImageContainer img {
        transition: transform 0.3s ease;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }

    #productImageContainer img:hover {
        transform: scale(1.05);
    }

    #productDescription {
        min-height: 100px;
        max-height: 200px;
        overflow-y: auto;
    }
    
    /* RTL Support */
    .rtl-content {
        direction: rtl;
        text-align: right;
    }

    /* Advanced Filter Section */
    .advanced-filter-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
        color: white;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }

    .filter-card {
        background: rgba(255,255,255,0.95);
        border-radius: 12px;
        padding: 1.5rem;
        margin-top: 1rem;
        color: #333;
        backdrop-filter: blur(10px);
        border: none;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    }

    /* Enhanced Table */
    .enhanced-table {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        margin-bottom: 2rem;
    }

    .enhanced-table .table {
        margin-bottom: 0;
    }

    .enhanced-table .table thead th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 1rem;
        font-weight: 600;
    }

    .enhanced-table .table tbody tr {
        transition: all 0.2s ease;
    }

    .enhanced-table .table tbody tr:hover {
        background-color: #f8f9fc;
        transform: scale(1.01);
    }

    /* Action Buttons */
    .action-btn {
        padding: 0.5rem 1rem;
        border-radius: 25px;
        font-weight: 500;
        transition: all 0.3s ease;
        border: none;
        margin: 0.25rem;
    }

    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }

    /* Quick Filters */
    .quick-filter {
        background: white;
        border: 2px solid #e3e6f0;
        border-radius: 25px;
        padding: 0.5rem 1rem;
        margin: 0.25rem;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .quick-filter:hover, .quick-filter.active {
        background: var(--bs-primary);
        color: white;
        border-color: var(--bs-primary);
    }

    /* Statistics Cards */
    .stats-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        border-left: 4px solid var(--bs-primary);
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    }

    .stats-card.success { border-left-color: var(--bs-success); }
    .stats-card.info { border-left-color: var(--bs-info); }
    .stats-card.warning { border-left-color: var(--bs-warning); }
    .stats-card.danger { border-left-color: var(--bs-danger); }

    /* Table Controls */
    .table-controls {
        background: #f8f9fc;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
        border: 1px solid #e3e6f0;
    }

    .table-controls .form-select-sm {
        min-width: 80px;
    }

    .table-controls .input-group {
        max-width: 400px;
    }

    /* Enhanced search styling */
    #tableSearch {
        border-radius: 25px 0 0 25px;
    }

    #clearTableSearch {
        border-radius: 0 25px 25px 0;
        border-left: none;
    }

    /* Entries selector styling */
    #entriesSelect {
        border-radius: 20px;
        border: 2px solid #e3e6f0;
        transition: all 0.3s ease;
    }

    #entriesSelect:focus {
        border-color: var(--bs-primary);
        box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
    }

    /* Enhanced Dashboard Cards */
    .stats-card {
        background: linear-gradient(135deg, #fff 0%, #f8f9fc 100%);
        border-radius: 20px;
        border: none;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        overflow: hidden;
        position: relative;
        height: 100%;
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    }

    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--card-color) 0%, var(--card-color-light) 100%);
    }

    .stats-card-primary {
        --card-color: #4e73df;
        --card-color-light: #6f8ef7;
    }

    .stats-card-warning {
        --card-color: #f6c23e;
        --card-color-light: #f8d866;
    }

    .stats-card-danger {
        --card-color: #e74a3b;
        --card-color-light: #ea6659;
    }

    .stats-card-success {
        --card-color: #1cc88a;
        --card-color-light: #36d9a3;
    }

    .stats-card-body {
        padding: 1.5rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
        background: linear-gradient(135deg, var(--card-color) 0%, var(--card-color-light) 100%);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }

    .stats-content {
        flex: 1;
        margin-right: 1rem;
    }

    .stats-title {
        font-size: 0.875rem;
        font-weight: 600;
        color: #6c757d;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 0.5rem;
    }

    .stats-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: #2c3e50;
        line-height: 1;
        margin-bottom: 0.25rem;
    }

    .stats-subtitle {
        font-size: 0.75rem;
        color: #95a5a6;
        font-weight: 500;
    }

    .stats-trend {
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 0.75rem;
        font-weight: 600;
        color: var(--card-color);
    }

    .stats-trend i {
        font-size: 1rem;
        margin-bottom: 0.25rem;
    }

    .stats-trend.warning {
        color: #f39c12;
    }

    .stats-trend.danger {
        color: #e74c3c;
    }

    .stats-trend.success {
        color: #27ae60;
    }

    .stats-footer {
        padding: 0.75rem 1.5rem;
        background: rgba(0,0,0,0.02);
        border-top: 1px solid rgba(0,0,0,0.05);
    }

    .stats-footer small {
        font-size: 0.75rem;
        color: #6c757d;
    }

    /* Table Styling */
    .table-card .card-header {
        background-color: transparent;
        border-bottom: 1px solid rgba(0,0,0,.05);
        padding: 1rem 1.25rem;
    }
    
    .table {
        margin-bottom: 0;
    }
    
    .table thead th {
        border-top: 0;
        font-weight: 600;
        font-size: 0.85rem;
        color: #ffffffff;
        padding: 1rem;
    }
    
    .table td {
        padding: 0.75rem 1rem;
        vertical-align: middle;
    }
    
    .table-hover tbody tr:hover {
        background-color: rgba(0,0,0,.02);
    }
    
    /* Progress Bars */
    .progress {
        height: 6px;
        border-radius: 3px;
        background-color: rgba(0,0,0,.05);
        margin-top: 0.5rem;
    }
    
    /* DataTables Adjustments */
    .dataTables_length {
        margin-bottom: 15px;
        margin-right: 15px;
    }

    .dataTables_length select {
        padding: 0.375rem 2.25rem 0.375rem 0.75rem;
        font-size: 1rem;
        font-weight: 400;
        line-height: 1.5;
        color: #212529;
        background-color: #fff;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }

    .dataTables_info {
        padding-top: 0.85em;
        white-space: nowrap;
        margin-right: 15px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    {% csrf_token %}

    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-boxes me-2"></i>{% trans "إدارة المخزون" %}
        </h1>
        <div>
        <a href="{% url 'inventory:add_product' %}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i> {% trans "إضافة منتج جديد" %}
        </a>
    </div>
</div>

<!-- Enhanced Dashboard Cards -->
<div class="row mb-4">
    <!-- إجمالي المنتجات -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card stats-card-primary">
            <div class="stats-card-body">
                <div class="stats-content">
                    <div class="stats-title">{% trans "إجمالي المنتجات" %}</div>
                    <div class="stats-number">{{ products.count }}</div>
                    <div class="stats-subtitle">{% trans "منتج مسجل" %}</div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-boxes"></i>
                </div>
            </div>
            <div class="stats-footer">
                <small>
                    <i class="fas fa-chart-line me-1"></i>{% trans "نمو مستمر في المخزون" %}
                </small>
            </div>
        </div>
    </div>

    <!-- منتجات منخفضة المخزون -->
    <div class="col-xl-3 col-md-6 mb-4">
        <a href="{% url 'inventory:stock_alerts' %}" class="text-decoration-none">
            <div class="stats-card stats-card-warning">
                <div class="stats-card-body">
                    <div class="stats-content">
                        <div class="stats-title">{% trans "مخزون منخفض" %}</div>
                        <div class="stats-number">{{ low_stock_count|default:"0" }}</div>
                        <div class="stats-subtitle">{% trans "يحتاج تجديد" %}</div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                </div>
                <div class="stats-footer">
                    <small>
                        <i class="fas fa-bell me-1"></i>{% trans "تنبيه مخزون - اضغط للتفاصيل" %}
                    </small>
                </div>
            </div>
        </a>
    </div>

    <!-- منتجات نفدت من المخزون -->
    <div class="col-xl-3 col-md-6 mb-4">
        <a href="{% url 'inventory:stock_alerts' %}?status=out" class="text-decoration-none">
            <div class="stats-card stats-card-danger">
                <div class="stats-card-body">
                    <div class="stats-content">
                        <div class="stats-title">{% trans "نفد المخزون" %}</div>
                        <div class="stats-number">{{ out_of_stock_count|default:"0" }}</div>
                        <div class="stats-subtitle">{% trans "غير متوفر" %}</div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-times-circle"></i>
                    </div>
                </div>
                <div class="stats-footer">
                    <small>
                        <i class="fas fa-exclamation-circle me-1"></i>{% trans "يحتاج طلب فوري - اضغط للتفاصيل" %}
                    </small>
                </div>
            </div>
        </a>
    </div>

    <!-- قيمة المخزون -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card stats-card-success">
            <div class="stats-card-body">
                <div class="stats-content">
                    <div class="stats-title">{% trans "قيمة المخزون" %}</div>
                    <div class="stats-number">{{ inventory_value|default:"0"|floatformat:0 }}</div>
                    <div class="stats-subtitle">{% trans "درهم مغربي" %}</div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-coins"></i>
                </div>
            </div>
            <div class="stats-footer">
                <small>
                    <i class="fas fa-calculator me-1"></i>{% trans "القيمة الإجمالية الحالية للمخزون" %}
                </small>
            </div>
        </div>
    </div>
</div>



    <!-- Search Section -->
    <div class="search-section">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" class="form-control" id="globalSearch"
                           placeholder="{% trans 'البحث في المنتجات (الاسم، الكود، الوصف، الباركود...)' %}">
                    <button class="btn btn-outline-secondary" type="button" id="clearSearch">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="col-md-4 text-end">
                <button class="btn btn-info" type="button" data-bs-toggle="collapse"
                        data-bs-target="#filterSection" aria-expanded="false">
                    <i class="fas fa-filter me-1"></i>{% trans "فلاتر متقدمة" %}
                </button>
            </div>
        </div>
    </div>

    <!-- Advanced Filters Section -->
    <div class="collapse" id="filterSection">
        <div class="filter-section">
            <form id="filterForm" method="GET">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label for="categoryFilter" class="form-label">
                            <i class="fas fa-tags me-1"></i>{% trans "الفئة" %}
                        </label>
                        <select class="form-select" id="categoryFilter" name="category">
                            <option value="">{% trans "جميع الفئات" %}</option>
                            {% for category in categories %}
                            <option value="{{ category.id }}">{{ category.name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="col-md-3 mb-3">
                        <label for="supplierFilter" class="form-label">
                            <i class="fas fa-truck me-1"></i>{% trans "المورد" %}
                        </label>
                        <select class="form-select" id="supplierFilter" name="supplier">
                            <option value="">{% trans "جميع الموردين" %}</option>
                            {% for supplier in suppliers %}
                            <option value="{{ supplier.id }}">{{ supplier.name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="col-md-3 mb-3">
                        <label for="stockStatusFilter" class="form-label">
                            <i class="fas fa-info-circle me-1"></i>{% trans "حالة المخزون" %}
                        </label>
                        <select class="form-select" id="stockStatusFilter" name="status">
                            <option value="">{% trans "جميع الحالات" %}</option>
                            <option value="available">{% trans "متوفر" %}</option>
                            <option value="low">{% trans "مخزون منخفض" %}</option>
                            <option value="out">{% trans "نفاد المخزون" %}</option>
                        </select>
                    </div>

                    <div class="col-md-3 mb-3">
                        <label for="min_price" class="form-label">
                            <i class="fas fa-dollar-sign me-1"></i>{% trans "الحد الأدنى للسعر" %}
                        </label>
                        <input type="number" class="form-control" id="min_price" name="min_price"
                               placeholder="0.00" min="0" step="0.01">
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label for="max_price" class="form-label">
                            <i class="fas fa-dollar-sign me-1"></i>{% trans "الحد الأعلى للسعر" %}
                        </label>
                        <input type="number" class="form-control" id="max_price" name="max_price"
                               placeholder="1000.00" min="0" step="0.01">
                    </div>

                    <div class="col-md-3 mb-3">
                        <label for="min_quantity" class="form-label">
                            <i class="fas fa-cubes me-1"></i>{% trans "الحد الأدنى للكمية" %}
                        </label>
                        <input type="number" class="form-control" id="min_quantity" name="min_quantity"
                               placeholder="0" min="0">
                    </div>

                    <div class="col-md-3 mb-3">
                        <label for="max_quantity" class="form-label">
                            <i class="fas fa-cubes me-1"></i>{% trans "الحد الأعلى للكمية" %}
                        </label>
                        <input type="number" class="form-control" id="max_quantity" name="max_quantity"
                               placeholder="1000" min="0">
                    </div>

                        <label for="sort_by" class="form-label">
                            <i class="fas fa-sort me-1"></i>{% trans "ترتيب حسب" %}
                        </label>
                        <select class="form-select" id="sort_by" name="sort_by">
                            <option value="name">{% trans "الاسم" %}</option>
                            <option value="code">{% trans "الكود" %}</option>
                            <option value="selling_price">{% trans "السعر" %}</option>
                            <option value="quantity">{% trans "الكمية" %}</option>
                            <option value="category">{% trans "الفئة" %}</option>
                            <option value="created_at">{% trans "تاريخ الإضافة" %}</option>
                        </select>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <div class="d-flex justify-content-between">
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-filter me-1"></i>{% trans "تطبيق الفلاتر" %}
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="clearFilters()">
                                    <i class="fas fa-undo me-1"></i>{% trans "إعادة تعيين" %}
                            </button>
                            </div>
                            <div>
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    {% trans "عدد النتائج" %}: <span id="resultsCount">{{ products.count }}</span>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

<!-- Products Table -->
<div class="card shadow mb-4 table-card">
    <div class="card-header py-3 d-flex justify-content-between align-items-center">
        <h6 class="m-0 font-weight-bold text-primary">
            <i class="fas fa-table me-2"></i>{% trans "قائمة المنتجات" %}
        </h6>
        <div class="d-flex align-items-center">
            <span class="badge bg-primary me-2">
                <i class="fas fa-list me-1"></i>{{ products.count }} {% trans "منتج" %}
            </span>
            <div class="btn-group">
                <button type="button" class="btn btn-sm btn-outline-danger" id="bulkDeleteBtn" disabled>
                    <i class="fas fa-trash me-1"></i> {% trans "حذف متعدد" %}
                </button>
            </div>
        </div>
    </div>
    <div class="card-body">
        <!-- Export Actions -->
        <div class="d-flex justify-content-start mb-3">
            <div class="btn-group">
                <a href="{% url 'inventory:export_products' %}?type=excel" class="btn btn-success btn-sm">
                    <i class="fas fa-file-excel me-1"></i>Excel
                </a>
                <a href="{% url 'inventory:export_products' %}?type=pdf" class="btn btn-danger btn-sm">
                    <i class="fas fa-file-pdf me-1"></i>PDF
                </a>
                <button type="button" class="btn btn-primary btn-sm" onclick="window.print()">
                    <i class="fas fa-print me-1"></i>Print
                </button>
            </div>
        </div>

        <!-- Search and Display Controls -->
        <div class="table-controls">
            <div class="row align-items-center">
                <div class="col-md-4">
                    <div class="d-flex align-items-center">
                        <label for="entriesSelect" class="form-label me-2 mb-0 fw-bold">
                            <i class="fas fa-list me-1 text-primary"></i>{% trans "عرض" %}
                        </label>
                        <select class="form-select form-select-sm" id="entriesSelect">
                            <option value="5">5</option>
                            <option value="10" selected>10</option>
                            <option value="15">15</option>
                            <option value="20">20</option>
                            <option value="25">25</option>
                            <option value="-1">{% trans "الكل" %}</option>
                        </select>
                        <span class="ms-2 text-muted">{% trans "عنصر" %}</span>
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="input-group">
                        <span class="input-group-text bg-primary text-white">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control" id="tableSearch"
                               placeholder="{% trans 'بحث سريع في الجدول (اسم المنتج، الكود، الفئة، المورد...)' %}">
                        <button class="btn btn-outline-danger" type="button" id="clearTableSearch" title="{% trans 'مسح البحث' %}">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table table-hover mb-0" id="productsTable" width="100%" cellspacing="0">
                <thead class="table-dark">
                    <tr>
                        <th width="30px" class="text-center">
                            <input type="checkbox" id="selectAll" class="form-check-input">
                        </th>
                        <th width="80px" class="text-center">{% trans "صورة" %}</th>
                        <th style="width: 100px;">{% trans "الكود" %}</th>
                        <th style="width: 200px;">{% trans "اسم المنتج" %}</th>
                        <th style="width: 150px;">{% trans "الفئة والمورد" %}</th>
                        <th class="text-center" style="width: 120px;">{% trans "المخزون" %}</th>
                        <th class="text-end" style="width: 140px;">{% trans "الأسعار" %}</th>
                        <th class="text-center" style="width: 120px;">{% trans "الحالة" %}</th>
                        <th class="d-none">{% trans "الوصف" %}</th>
                        <th>{% trans "الإجراءات" %}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for product in products %}
                    <tr>
                        <td>
                            <input type="checkbox" class="form-check-input product-select" value="{{ product.id }}">
                        </td>
                        <td>
                            {% if product.image %}
                            <img src="{{ product.image.url }}" alt="{{ product.name }}" class="product-image-small">
                            {% else %}
                            <div class="text-center">
                                <i class="fas fa-image text-muted"></i>
                            </div>
                            {% endif %}
                        </td>
                        <td>
                            <div>
                                <strong>{{ product.code }}</strong>
                                {% if product.barcode %}
                                    <br><small class="text-muted">
                                        <i class="fas fa-barcode me-1"></i>{{ product.barcode }}
                                    </small>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            <div>
                                <strong>{{ product.name }}</strong>
                                {% if product.description %}
                                    <br><small class="text-muted">{{ product.description|truncatewords:8 }}</small>
                                {% endif %}
                                {% if product.part_number %}
                                    <br><span class="badge bg-info">{{ product.part_number }}</span>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            <div>
                                <span class="badge bg-primary">{{ product.category.name }}</span>
                                {% if product.supplier %}
                                    <br><small class="text-muted">
                                        <i class="fas fa-truck me-1"></i>{{ product.supplier.name }}
                                    </small>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            <div class="text-center">
                                <span class="badge {% if product.quantity == 0 %}bg-danger{% elif product.is_low_stock %}bg-warning{% else %}bg-success{% endif %} fs-6">
                                    {{ product.quantity }}
                                </span>
                                {% if product.display_unit %}
                                    <br><small class="text-muted">{{ product.display_unit }}</small>
                                {% endif %}
                                {% if product.min_quantity %}
                                    <br><small class="text-muted">
                                        حد أدنى: {{ product.min_quantity }}
                                    </small>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            <div class="text-end">
                                <div class="fw-bold text-success">{{ product.selling_price|floatformat:2 }} د.م</div>
                                <small class="text-muted">
                                    شراء: {{ product.purchase_price|floatformat:2 }} د.م
                                </small>
                                {% if product.selling_price > product.purchase_price %}
                                    <br><small class="text-success">
                                        <i class="fas fa-arrow-up me-1"></i>
                                        ربح: {{ product.selling_price|add:"-"|add:product.purchase_price|floatformat:2 }} د.م
                                    </small>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            <div class="text-center">
                                {% if product.quantity == 0 %}
                                    <span class="badge bg-danger">
                                        <i class="fas fa-times-circle me-1"></i>نفد
                                    </span>
                                    <br><small class="text-danger">غير متوفر</small>
                                {% elif product.is_low_stock %}
                                    <span class="badge bg-warning">
                                        <i class="fas fa-exclamation-triangle me-1"></i>منخفض
                                    </span>
                                    <br><small class="text-warning">يحتاج تجديد</small>
                                {% else %}
                                    <span class="badge bg-success">
                                        <i class="fas fa-check-circle me-1"></i>متوفر
                                    </span>
                                    <br><small class="text-success">مخزون جيد</small>
                                {% endif %}
                            </div>
                        </td>
                        <td class="d-none">{{ product.description }}</td>
                        <td>
                            <div class="action-buttons">
                                <div class="btn-group">
                                    <a href="#" class="btn btn-sm btn-info view-product-btn" data-id="{{ product.id }}" data-bs-toggle="modal" data-bs-target="#viewProductModal" title="{% trans 'عرض التفاصيل' %}">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="#" class="btn btn-sm btn-info stock-movement-btn" data-id="{{ product.id }}" data-name="{{ product.name }}" data-bs-toggle="modal" data-bs-target="#stockMovementModal" title="{% trans 'حركة المخزون' %}">
                                        <i class="fas fa-exchange-alt"></i>
                                    </a>
                                    <a href="{% url 'inventory:edit_product' product.id %}" class="btn btn-sm btn-primary" title="{% trans 'تعديل' %}">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'inventory:delete_product' product.id %}" class="btn btn-sm btn-danger" title="{% trans 'حذف' %}">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="10" class="text-center">{% trans "لا توجد منتجات" %}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Stock Movement Modal -->
<div class="modal fade" id="stockMovementModal" tabindex="-1" aria-labelledby="stockMovementModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="stockMovementModalLabel">{% trans "حركة المخزون" %}: <span id="productName"></span></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="stockMovementForm">
                    <input type="hidden" id="productId" name="product_id">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="movementType" class="form-label">{% trans "نوع الحركة" %}</label>
                            <select class="form-select" id="movementType" name="movement_type" required>
                                <option value="in">{% trans "وارد (إضافة)" %}</option>
                                <option value="out">{% trans "صادر (سحب)" %}</option>
                                <option value="adjustment">{% trans "تعديل" %}</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="quantity" class="form-label">{% trans "الكمية" %}</label>
                            <input type="number" class="form-control" id="quantity" name="quantity" min="1" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="reference" class="form-label">{% trans "المرجع" %}</label>
                        <input type="text" class="form-control" id="reference" name="reference" placeholder="{% trans 'مثال: فاتورة شراء رقم 123' %}">
                    </div>
                    <div class="mb-3">
                        <label for="notes" class="form-label">{% trans "ملاحظات" %}</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>
                </form>

                <hr>

                <h6 class="mb-3">{% trans "سجل الحركات السابقة" %}</h6>
                <div class="table-responsive">
                    <table class="table table-sm table-bordered" id="movementsTable">
                        <thead>
                            <tr>
                                <th>{% trans "التاريخ" %}</th>
                                <th>{% trans "نوع الحركة" %}</th>
                                <th>{% trans "الكمية" %}</th>
                                <th>{% trans "المرجع" %}</th>
                            </tr>
                        </thead>
                        <tbody id="movementsTableBody">
                            <!-- سيتم ملء هذا الجزء ديناميكيًا -->
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إغلاق" %}</button>
                <button type="button" class="btn btn-primary" id="saveMovementBtn">{% trans "حفظ" %}</button>
            </div>
        </div>
    </div>
</div>

<!-- View Product Modal -->
<div class="modal fade" id="viewProductModal" tabindex="-1" aria-labelledby="viewProductModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewProductModalLabel">{% trans "تفاصيل المنتج" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-5 text-center mb-3">
                        <div id="productImageContainer" class="mb-3">
                            <img id="productImage" src="" alt="صورة المنتج" class="img-fluid rounded" style="max-height: 250px; cursor: pointer;" onclick="openImageInFullScreen(this.src)">
                        </div>
                        <div id="noImageContainer" class="d-none">
                            <div class="border rounded p-5 text-muted">
                                <i class="fas fa-image fa-4x mb-3"></i>
                                <p>{% trans "لا توجد صورة" %}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-7">
                        <h4 id="productName" class="mb-3"></h4>
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <tbody>
                                    <tr>
                                        <th width="40%">{% trans "الكود" %}</th>
                                        <td id="productCode"></td>
                                    </tr>
                                    <tr>
                                        <th>{% trans "الباركود" %}</th>
                                        <td id="productBarcode"></td>
                                    </tr>
                                    <tr>
                                        <th>{% trans "الفئة" %}</th>
                                        <td id="productCategory"></td>
                                    </tr>
                                    <tr>
                                        <th>{% trans "المورد" %}</th>
                                        <td id="productSupplier"></td>
                                    </tr>
                                    <tr>
                                        <th>{% trans "موقع التخزين" %}</th>
                                        <td id="productStorageLocation"></td>
                                    </tr>
                                    <tr>
                                        <th>{% trans "الكمية المتوفرة" %}</th>
                                        <td id="productQuantity"></td>
                                    </tr>
                                    <tr>
                                        <th>{% trans "سعر البيع" %}</th>
                                        <td id="productSellingPrice"></td>
                                    </tr>
                                    <tr>
                                        <th>{% trans "سعر الشراء" %}</th>
                                        <td id="productPurchasePrice"></td>
                                    </tr>
                                    <tr>
                                        <th>{% trans "الحد الأدنى للكمية" %}</th>
                                        <td id="productMinQuantity"></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <h5>{% trans "الوصف" %}</h5>
                        <div id="productDescription" class="border rounded p-3 bg-light"></div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <a href="#" id="editProductLink" class="btn btn-primary">
                    <i class="fas fa-edit me-1"></i> {% trans "تعديل" %}
                </a>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إغلاق" %}</button>
            </div>
        </div>
    </div>
</div>


{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
<script>
    $(document).ready(function() {
        // Initialize DataTable
        var table = $('#productsTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json",
                "lengthMenu": "إظهار _MENU_ من العناصر",
                "info": "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل",
                "search": "بحث:",
                "paginate": {
                    "first": "الأول",
                    "last": "الأخير",
                    "next": "التالي",
                    "previous": "السابق"
                }
            },
            "order": [[3, "asc"]],
            "columnDefs": [
                { "orderable": false, "targets": [0, 1, 6] }
            ],
            "pageLength": 10,
            "lengthMenu": [[5, 10, 15, 20, 25, -1], [5, 10, 15, 20, 25, "الكل"]],
            "dom": 'rt<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
            "searching": true,
            "info": true,
            "paging": true
        });

        // Table search functionality (separate from global search)
        $('#tableSearch').on('keyup', function() {
            table.search(this.value).draw();
        });

        // Clear table search
        $('#clearTableSearch').on('click', function() {
            $('#tableSearch').val('');
            table.search('').draw();
        });

        // Entries per page selector
        $('#entriesSelect').on('change', function() {
            var length = $(this).val();
            table.page.len(length).draw();
        });

        // Select All Checkbox
        $('#selectAll').change(function() {
            $('.product-select').prop('checked', $(this).prop('checked'));
            updateBulkButtons();
        });

        // Individual Checkboxes
        $(document).on('change', '.product-select', function() {
            updateBulkButtons();
        });

        // Update Bulk Buttons State
        function updateBulkButtons() {
            var selectedCount = $('.product-select:checked').length;
            $('#bulkDeleteBtn').prop('disabled', selectedCount === 0);
        }

        // Custom filter function for multiple criteria
        $.fn.dataTable.ext.search.push(
            function(settings, data, dataIndex) {
                // Get filter values
                var categoryFilter = $('#categoryFilter').val();
                var supplierFilter = $('#supplierFilter').val();
                var statusFilter = $('#stockStatusFilter').val();
                var minPrice = parseFloat($('#min_price').val()) || 0;
                var maxPrice = parseFloat($('#max_price').val()) || 999999;
                var minQuantity = parseInt($('#min_quantity').val()) || 0;
                var maxQuantity = parseInt($('#max_quantity').val()) || 999999;

                // Get data from columns
                var categorySupplierText = data[4] || ''; // الفئة والمورد
                var statusText = data[7] || ''; // الحالة
                var priceText = data[6] || ''; // الأسعار
                var quantityText = data[5] || ''; // المخزون

                // Check category filter
                if (categoryFilter) {
                    var categoryText = $('#categoryFilter option:selected').text();
                    if (categorySupplierText.indexOf(categoryText) === -1) {
                        return false;
                    }
                }

                // Check supplier filter
                if (supplierFilter) {
                    var supplierText = $('#supplierFilter option:selected').text();
                    if (categorySupplierText.indexOf(supplierText) === -1) {
                        return false;
                    }
                }

                // Check status filter with more precise matching
                if (statusFilter) {
                    var statusMatch = false;

                    if (statusFilter === 'available') {
                        // يجب أن يحتوي على "متوفر" وليس "غير متوفر"
                        statusMatch = statusText.includes('متوفر') && !statusText.includes('غير متوفر');
                    } else if (statusFilter === 'low') {
                        // يجب أن يحتوي على "منخفض" أو "يحتاج تجديد"
                        statusMatch = statusText.includes('منخفض') || statusText.includes('يحتاج تجديد');
                    } else if (statusFilter === 'out') {
                        // يجب أن يحتوي على "نفد" أو "غير متوفر"
                        statusMatch = statusText.includes('نفد') || statusText.includes('غير متوفر');
                    }

                    if (!statusMatch) {
                        return false;
                    }
                }

                // Check price filter
                var priceMatch = priceText.match(/(\d+\.?\d*)\s*د\.م/);
                var price = priceMatch ? parseFloat(priceMatch[1]) : 0;
                if (price < minPrice || price > maxPrice) {
                    return false;
                }

                // Check quantity filter
                var quantityMatch = quantityText.match(/(\d+)/);
                var quantity = quantityMatch ? parseInt(quantityMatch[1]) : 0;
                if (quantity < minQuantity || quantity > maxQuantity) {
                    return false;
                }

                return true;
            }
        );

        // Filter change events
        $('#categoryFilter, #supplierFilter, #stockStatusFilter').change(function() {
            table.draw();
        });

        // Price and quantity filter events
        $('#min_price, #max_price, #min_quantity, #max_quantity').on('input', function() {
            table.draw();
        });

        // Form submission for filters
        $('#filterForm').on('submit', function(e) {
            e.preventDefault();

            // Get all filter values
            var filters = {
                category: $('#categoryFilter').val(),
                supplier: $('#supplierFilter').val(),
                status: $('#stockStatusFilter').val(),
                min_price: $('#min_price').val(),
                max_price: $('#max_price').val(),
                min_quantity: $('#min_quantity').val(),
                max_quantity: $('#max_quantity').val(),
                sort_by: $('#sort_by').val(),
                search: $('#globalSearch').val()
            };

            // Build URL with filters
            var url = new URL(window.location.href);
            Object.keys(filters).forEach(key => {
                if (filters[key]) {
                    url.searchParams.set(key, filters[key]);
                } else {
                    url.searchParams.delete(key);
                }
            });

            // Redirect with filters
            window.location.href = url.toString();
        });

        // Global search functionality
        $('#globalSearch').on('keyup', function() {
            table.search(this.value).draw();
        });

        // Clear global search
        $('#clearSearch').on('click', function() {
            $('#globalSearch').val('');
            table.search('').draw();
        });

        // Quick Search
        $('#searchBtn').click(function() {
            var searchTerm = $('#quickSearch').val();
            table.search(searchTerm).draw();
        });

        $('#quickSearch').keypress(function(e) {
            if (e.which === 13) {
                var searchTerm = $(this).val();
                table.search(searchTerm).draw();
            }
        });

        // Stock Movement Modal
        $('.stock-movement-btn').click(function() {
            var productId = $(this).data('id');
            var productName = $(this).data('name');

            $('#productId').val(productId);
            $('#productName').text(productName);

            // Load product movements
            $.ajax({
                url: '/inventory/product/' + productId + '/movements/',
                type: 'GET',
                success: function(data) {
                    var tbody = $('#movementsTableBody');
                    tbody.empty();

                    if (data.movements.length > 0) {
                        $.each(data.movements, function(i, movement) {
                            var row = '<tr>' +
                                '<td>' + movement.created_at + '</td>' +
                                '<td>' + movement.movement_type_display + '</td>' +
                                '<td>' + movement.quantity + '</td>' +
                                '<td>' + (movement.reference || '-') + '</td>' +
                                '</tr>';
                            tbody.append(row);
                        });
                    } else {
                        tbody.append('<tr><td colspan="4" class="text-center">لا توجد حركات سابقة</td></tr>');
                    }
                },
                error: function() {
                    $('#movementsTableBody').html('<tr><td colspan="4" class="text-center text-danger">حدث خطأ أثناء تحميل البيانات</td></tr>');
                }
            });
        });

        // Save Stock Movement
        $('#saveMovementBtn').click(function() {
            var form = $('#stockMovementForm');

            if (!form[0].checkValidity()) {
                form[0].reportValidity();
                return;
            }

            var formData = {
                product_id: $('#productId').val(),
                movement_type: $('#movementType').val(),
                quantity: $('#quantity').val(),
                reference: $('#reference').val(),
                notes: $('#notes').val()
            };

            $.ajax({
                url: '/inventory/stock-movement/add/',
                type: 'POST',
                data: formData,
                headers: {
                    'X-CSRFToken': getCookie('csrftoken')
                },
                success: function(response) {
                    if (response.success) {
                        // Show success message
                        alert('تم حفظ حركة المخزون بنجاح');

                        // Close modal and reload page
                        $('#stockMovementModal').modal('hide');
                        location.reload();
                    } else {
                        alert('حدث خطأ: ' + response.error);
                    }
                },
                error: function(xhr, status, error) {
                    console.error("Error details:", xhr.responseText);
                    alert('حدث خطأ أثناء معالجة الطلب: ' + error);
                }
            });
        });

        // Export Button
        $('#exportBtn').click(function(e) {
            e.preventDefault();
            window.location.href = '/inventory/export/';
        });





        // Bulk Delete Button
        $('#bulkDeleteBtn').click(function() {
            if (confirm('هل أنت متأكد من رغبتك في حذف المنتجات المحددة؟')) {
                var selectedIds = [];
                $('.product-select:checked').each(function() {
                    selectedIds.push($(this).val());
                });

                $.ajax({
                    url: '/inventory/bulk-delete/',
                    type: 'POST',
                    data: {
                        product_ids: selectedIds.join(',')
                    },
                    headers: {
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    success: function(response) {
                        if (response.success) {
                            alert('تم حذف المنتجات بنجاح');
                            location.reload();
                        } else {
                            alert('حدث خطأ: ' + response.error);
                        }
                    },
                    error: function() {
                        alert('حدث خطأ أثناء معالجة الطلب');
                    }
                });
            }
        });

        // View Product Details
        $('.view-product-btn').click(function() {
            var productId = $(this).data('id');

            // Load product details
            $.ajax({
                url: '/inventory/product/' + productId + '/details/',
                type: 'GET',
                success: function(data) {
                    // Fill product details in modal
                    $('#productName').text(data.name);
                    $('#productCode').text(data.code);
                    $('#productBarcode').text(data.barcode);
                    $('#productCategory').text(data.category_name);
                    $('#productSupplier').text(data.supplier_name);
                    $('#productStorageLocation').text(data.storage_location || '-');
                    $('#productQuantity').text(data.quantity);
                    $('#productSellingPrice').text(data.selling_price + ' د.م');
                    $('#productPurchasePrice').text(data.purchase_price + ' د.م');
                    $('#productMinQuantity').text(data.min_quantity);
                    $('#productDescription').html(data.description || '<span class="text-muted">لا يوجد وصف</span>');

                    // Set edit link
                    $('#editProductLink').attr('href', '/inventory/edit/' + productId + '/');

                    // Handle product image
                    if (data.image_url) {
                        $('#productImage').attr('src', data.image_url);
                        $('#productImageContainer').removeClass('d-none');
                        $('#noImageContainer').addClass('d-none');
                    } else {
                        $('#productImageContainer').addClass('d-none');
                        $('#noImageContainer').removeClass('d-none');
                    }
                },
                error: function() {
                    alert('حدث خطأ أثناء تحميل بيانات المنتج');
                }
            });
        });

        // Function to open image in full screen
        function openImageInFullScreen(src) {
            window.open(src, '_blank');
        }

        // Helper function to get CSRF token
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
    });

    // Define openImageInFullScreen function in global scope
    function openImageInFullScreen(src) {
        window.open(src, '_blank');
    }

    // Clear filters function
    function clearFilters() {
        $('#filterForm')[0].reset();
        $('#globalSearch').val('');
        $('#tableSearch').val('');

        // Clear all DataTable searches
        if (typeof table !== 'undefined') {
            table.search('').columns().search('').draw();
        }

        // Redirect to clean page
        window.location.href = window.location.pathname;
    }
</script>
{% endblock %}
