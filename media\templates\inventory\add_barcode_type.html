{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/inventory.css' %}">
<style>
    .barcode-type-form {
        max-width: 800px;
        margin: 0 auto;
    }
    .form-section {
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
    .form-section-title {
        margin-bottom: 1.25rem;
        color: #4e73df;
        font-weight: 600;
    }
</style>
{% endblock %}

{% block title %}{% trans "إضافة نوع باركود" %} | {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{% trans "إضافة نوع باركود جديد" %}</h1>
        <a href="{% url 'inventory:barcode_types' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> {% trans "العودة إلى قائمة أنواع الباركود" %}
        </a>
    </div>

    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    <div class="barcode-type-form">
        <form method="post" action="{% url 'inventory:add_barcode_type' %}">
            {% csrf_token %}
            <div class="form-section">
                <h5 class="form-section-title">{% trans "معلومات نوع الباركود" %}</h5>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="name" class="form-label">{% trans "الاسم" %} <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" required>
                        <div class="form-text">{% trans "اسم نوع الباركود (مثال: EAN-13، Code 128)" %}</div>
                    </div>
                    <div class="col-md-6">
                        <label for="code" class="form-label">{% trans "الرمز" %} <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="code" name="code" required>
                        <div class="form-text">{% trans "رمز نوع الباركود (مثال: ean13، code128)" %}</div>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="description" class="form-label">{% trans "الوصف" %}</label>
                    <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    <div class="form-text">{% trans "وصف اختياري لنوع الباركود" %}</div>
                </div>
            </div>

            <div class="d-flex justify-content-end">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-1"></i> {% trans "حفظ" %}
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // يمكن إضافة أي سلوك جافاسكريبت هنا إذا لزم الأمر
    });
</script>
{% endblock %}