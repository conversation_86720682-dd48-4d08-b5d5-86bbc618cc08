<!-- Invoice Header -->
<div class="invoice-header">
    <div class="row">
        <div class="col-md-6">
            <div class="d-flex align-items-center">
                <div class="logo-container" id="logo-container">
                    {% if invoice_settings.show_logo %}
                        {% if company_info.logo %}
                        <img src="{{ company_info.logo.url }}" alt="{{ company_info.name }}" class="invoice-logo me-3">
                        {% else %}
                        <img src="/static/img/logo.png" alt="Logo" class="invoice-logo me-3">
                        {% endif %}
                    {% endif %}
                </div>
                <div>
                    <h1 class="invoice-title">{{ company_info.name }}</h1>
                    <span class="badge bg-primary">{% trans "فاتورة مبيعات" %}</span>
                    <p class="mb-0">{% trans "فاتورة مبيعات" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-6 text-md-end">
            <h2>{% trans "فاتورة" %} #{{ sale.invoice_number }}</h2>
            <p class="mb-0">{% trans "تاريخ الفاتورة:" %} {{ sale.date|date:"Y-m-d" }}</p>
            <p class="mb-0">{% trans "وقت الإصدار:" %} {{ sale.date|date:"H:i" }}</p>
        </div>
    </div>
</div>

<!-- Invoice Details -->
<div class="row invoice-details">
    <div class="col-md-6 customer-info-container" id="customer-info-container">
        {% if invoice_settings.show_customer_info %}
        <h5>{% trans "معلومات العميل" %}</h5>
        <dl class="row mb-0">
            <dt class="col-sm-4">{% trans "اسم العميل:" %}</dt>
            <dd class="col-sm-8">{{ sale.customer.name }}</dd>

            {% if sale.customer.phone %}
            <dt class="col-sm-4">{% trans "رقم الهاتف:" %}</dt>
            <dd class="col-sm-8">{{ sale.customer.phone }}</dd>
            {% endif %}

            {% if sale.customer.email %}
            <dt class="col-sm-4">{% trans "البريد الإلكتروني:" %}</dt>
            <dd class="col-sm-8">{{ sale.customer.email }}</dd>
            {% endif %}

            {% if sale.customer.address %}
            <dt class="col-sm-4">{% trans "العنوان:" %}</dt>
            <dd class="col-sm-8">{{ sale.customer.address }}</dd>
            {% endif %}
        </dl>
        {% endif %}
    </div>
    <div class="col-md-6 company-info-container" id="company-info-container" {% if not invoice_settings.show_company_info %}style="display:none;"{% endif %}>
        <h5>{% trans "معلومات الشركة" %}</h5>
        <dl class="row mb-0">
            <dt class="col-sm-4">{% trans "اسم الشركة:" %}</dt>
            <dd class="col-sm-8">{{ company_info.name }}</dd>

            <dt class="col-sm-4">{% trans "رقم الهاتف:" %}</dt>
            <dd class="col-sm-8">{{ company_info.phone }}</dd>

            <dt class="col-sm-4">{% trans "البريد الإلكتروني:" %}</dt>
            <dd class="col-sm-8">{{ company_info.email }}</dd>

            <dt class="col-sm-4">{% trans "العنوان:" %}</dt>
            <dd class="col-sm-8">{{ company_info.address }}</dd>

            {% if company_info.tax_number %}
            <dt class="col-sm-4">{% trans "الرقم الضريبي:" %}</dt>
            <dd class="col-sm-8">{{ company_info.tax_number }}</dd>
            {% endif %}
        </dl>
    </div>
</div>

<!-- Invoice Items -->
<div class="invoice-items">
    <h4 class="mb-3"><i class="fas fa-shopping-cart me-2"></i>{% trans "المنتجات" %}</h4>
    <div class="table-responsive">
        <table class="table table-invoice">
            <thead>
                <tr>
                    <th width="5%">#</th>
                    <th width="15%">{% trans "كود المنتج" %}</th>
                    <th width="35%">{% trans "اسم المنتج" %}</th>
                    <th width="10%">{% trans "الكمية" %}</th>
                    <th width="15%">{% trans "سعر الوحدة" %}</th>
                    <th width="20%">{% trans "المجموع" %}</th>
                </tr>
            </thead>
            <tbody>
                {% for item in items %}
                <tr>
                    <td class="text-center">{{ forloop.counter }}</td>
                    <td>{{ item.product.code }}</td>
                    <td><strong>{{ item.product.name }}</strong></td>
                    <td class="text-center">{{ item.quantity }}</td>
                    <td class="text-start">{{ item.unit_price|floatformat:2 }} {% trans "د.م" %}</td>
                    <td class="text-start"><strong>{{ item.subtotal|floatformat:2 }} {% trans "د.م" %}</strong></td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

<!-- Invoice Summary -->
<div class="row">
    <div class="col-md-6">
        <div class="payment-info">
            <h5><i class="fas fa-money-check-alt me-2"></i>{% trans "معلومات الدفع" %}</h5>
            <dl class="row mb-0">
                <dt class="col-sm-6 payment-method-info" {% if not invoice_settings.show_payment_info %}style="display:none;"{% endif %}>{% trans "طريقة الدفع:" %}</dt>
                <dd class="col-sm-6 payment-method-info" {% if not invoice_settings.show_payment_info %}style="display:none;"{% endif %}>
                    {% if sale.payment_method == 'cash' %}
                    {% trans "نقدي" %}
                    {% elif sale.payment_method == 'card' %}
                    {% trans "بطاقة ائتمان" %}
                    {% elif sale.payment_method == 'transfer' %}
                    {% trans "تحويل بنكي" %}
                    {% elif sale.payment_method == 'check' %}
                    {% trans "شيك" %}
                    {% elif sale.payment_method == 'credit' %}
                    {% trans "آجل" %}
                    {% endif %}
                </dd>

                <dt class="col-sm-6">{% trans "حالة الدفع:" %}</dt>
                <dd class="col-sm-6">
                    {% if sale.status == 'completed' %}
                    <span class="payment-status paid"><i class="fas fa-check-circle me-1"></i>{% trans "مدفوع بالكامل" %}</span>
                    {% elif sale.status == 'pending' %}
                    <span class="payment-status partial"><i class="fas fa-clock me-1"></i>{% trans "معلق" %}</span>
                    {% elif sale.status == 'cancelled' %}
                    <span class="payment-status unpaid"><i class="fas fa-exclamation-circle me-1"></i>{% trans "ملغي" %}</span>
                    {% endif %}
                </dd>
                
                <!-- Display payment details -->
                {% if payments %}
                <dt class="col-sm-12 mt-3">{% trans "الدفعات:" %}</dt>
                <dd class="col-sm-12">
                    <table class="table table-sm mt-2">
                        <thead>
                            <tr>
                                <th>{% trans "التاريخ" %}</th>
                                <th>{% trans "المبلغ" %}</th>
                                <th>{% trans "المرجع" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for payment in payments %}
                            <tr>
                                <td>{{ payment.payment_date|date:"Y-m-d H:i" }}</td>
                                <td>{{ payment.amount|floatformat:2 }} {% trans "د.م" %}</td>
                                <td>{{ payment.reference|default:"" }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot>
                            <tr>
                                <th>{% trans "المجموع المدفوع:" %}</th>
                                <th colspan="2">{{ total_paid|floatformat:2 }} {% trans "د.م" %}</th>
                            </tr>
                            <tr>
                                <th>{% trans "المبلغ المتبقي:" %}</th>
                                <th colspan="2">{{ remaining_amount|floatformat:2 }} {% trans "د.م" %}</th>
                            </tr>
                        </tfoot>
                    </table>
                </dd>
                {% endif %}
            </dl>
        </div>

        <div class="qr-code mt-4 text-center" id="qr-code">
            <img src="https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=INV-{{ sale.invoice_number }}" alt="QR Code">
            <p class="small mt-2">{% trans "امسح الرمز للتحقق من الفاتورة" %}</p>
        </div>
    </div>
    <div class="col-md-6">
        <div class="invoice-summary">
            <div class="d-flex justify-content-between mb-2">
                <span>{% trans "المجموع الفرعي:" %}</span>
                <span>{{ sale.subtotal|floatformat:2 }} د.م</span>
            </div>
            {% if invoice_settings.show_tax %}
            <div class="d-flex justify-content-between mb-2">
                <span>{% trans "ضريبة القيمة المضافة" %} ({{ tax.rate }}%):</span>
                <span>{{ sale.tax_amount|floatformat:2 }} د.م</span>
            </div>
            {% endif %}
            {% if sale.discount > 0 %}
            <div class="d-flex justify-content-between mb-2">
                <span>{% trans "الخصم:" %}</span>
                <span>{{ sale.discount|floatformat:2 }} د.м</span>
            </div>
            {% endif %}
            <hr>
            <div class="d-flex justify-content-between">
                <strong>{% trans "المجموع الكلي:" %}</strong>
                <strong class="total-amount">{{ sale.total_amount|floatformat:2 }} د.م</strong>
            </div>
        </div>
    </div>
</div>

<!-- Notes -->
{% if sale.notes %}
<div class="mt-4">
    <h5>{% trans "ملاحظات" %}</h5>
    <p>{{ sale.notes }}</p>
</div>
{% endif %}

<!-- Signature Area -->
<div class="row signature-area" id="signature-area">
    <div class="signature-box">
        <p>{% trans "توقيع المستلم" %}</p>
        <div style="height: 70px;"></div>
        <div class="signature-line"></div>
        <small class="text-muted">{% trans "الاسم والتاريخ" %}</small>
    </div>
    <div class="signature-box">
        <p>{% trans "ختم الشركة" %}</p>
        <div style="height: 70px;"></div>
        <div class="signature-line"></div>
        <small class="text-muted">{% trans "ختم الشركة" %}</small>
    </div>
    <div class="signature-box">
        <p>{% trans "توقيع البائع" %}</p>
        <div style="height: 70px;"></div>
        <div class="signature-line"></div>
        <small class="text-muted">{{ sale.employee.get_full_name|default:sale.employee.username }}</small>
    </div>
</div>

<!-- Terms and Conditions -->
<div class="terms-conditions" id="terms-conditions">
    <h6><i class="fas fa-gavel me-2"></i>{% trans "الشروط والأحكام" %}</h6>
    <ol>
        <li>{% trans "جميع المبيعات نهائية ولا يمكن استردادها بعد مغادرة المتجر." %}</li>
        <li>{% trans "يمكن استبدال المنتجات في غضون 14 يومًا من تاريخ الشراء مع تقديم الفاتورة الأصلية." %}</li>
        <li>{% trans "لا يتم قبول المنتجات المستخدمة أو التالفة للاستبدال." %}</li>
        <li>{% trans "تطبق ضمانات الشركة المصنعة على المنتجات المؤهلة." %}</li>
        <li>{% trans "يحتفظ المتجر بالحق في تغيير هذه الشروط والأحكام في أي وقت." %}</li>
    </ol>
</div>

<!-- Invoice Footer -->
<div class="invoice-footer text-center">
    <div class="row">
        <div class="col-md-4">
            <i class="fas fa-phone-alt me-2"></i> {{ company_info.phone|default:"" }}
        </div>
        <div class="col-md-4">
            <i class="fas fa-envelope me-2"></i> {{ company_info.email|default:"" }}
        </div>
        <div class="col-md-4">
            <i class="fas fa-globe me-2"></i> {{ company_info.website|default:"" }}
        </div>
    </div>
    <hr class="my-3">
    <p>{% trans "شكراً لتعاملكم معنا" %}</p>
    {% if company_info.footer_text %}
    <p class="small">{{ company_info.footer_text }}</p>
    {% endif %}
    <p class="small">{% trans "تم إنشاء هذه الفاتورة بواسطة" %} {{ company_info.name }} &copy; {% now "Y" %}</p>
    <p class="small">{% trans "تاريخ الطباعة:" %} {% now "Y-m-d H:i" %}</p>
</div>