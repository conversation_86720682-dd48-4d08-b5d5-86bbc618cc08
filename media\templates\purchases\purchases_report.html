{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "تقرير المشتريات" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.2.2/css/buttons.bootstrap5.min.css">
<style>
    .filter-section {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .status-badge {
        font-size: 0.8rem;
        padding: 0.4rem 0.6rem;
    }

    .payment-badge {
        font-size: 0.75rem;
    }

    .summary-card {
        transition: all 0.3s;
    }

    .summary-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    .summary-icon {
        font-size: 2rem;
        opacity: 0.7;
    }

    @media print {
        .no-print {
            display: none !important;
        }

        .card {
            border: none !important;
            box-shadow: none !important;
        }

        .card-header {
            background-color: white !important;
            border-bottom: 1px solid #ddd !important;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4 no-print">
    <h1 class="h3 mb-0 text-gray-800">{% trans "تقرير المشتريات" %}</h1>
    <div>
        <button type="button" class="btn btn-secondary me-2" onclick="window.print()">
            <i class="fas fa-print me-1"></i> {% trans "طباعة" %}
        </button>
        <a href="{% url 'purchases:export_report' report_type='purchases' %}" class="btn btn-success me-2">
            <i class="fas fa-file-csv me-1"></i> {% trans "تصدير CSV" %}
        </a>
        <a href="{% url 'purchases:reports' %}" class="btn btn-primary">
            <i class="fas fa-arrow-right me-1"></i> {% trans "العودة إلى التقارير" %}
        </a>
    </div>
</div>

<!-- Filters -->
<div class="card shadow mb-4 no-print">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">{% trans "تصفية النتائج" %}</h6>
    </div>
    <div class="card-body">
        <form action="{% url 'purchases:purchases_report' %}" method="get" id="filterForm">
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label for="date_from" class="form-label">{% trans "من تاريخ" %}</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="{{ date_from }}">
                </div>
                <div class="col-md-4 mb-3">
                    <label for="date_to" class="form-label">{% trans "إلى تاريخ" %}</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="{{ date_to }}">
                </div>
                <div class="col-md-4 mb-3">
                    <label for="supplier" class="form-label">{% trans "المورد" %}</label>
                    <select class="form-select" id="supplier" name="supplier">
                        <option value="">{% trans "الكل" %}</option>
                        {% for supplier in suppliers %}
                        <option value="{{ supplier.id }}" {% if supplier_id == supplier.id|stringformat:"i" %}selected{% endif %}>{{ supplier.name }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label for="status" class="form-label">{% trans "حالة الطلب" %}</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">{% trans "الكل" %}</option>
                        {% for status_code, status_name in status_choices %}
                        <option value="{{ status_code }}" {% if status == status_code %}selected{% endif %}>{{ status_name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-4 mb-3">
                    <label for="payment_status" class="form-label">{% trans "حالة الدفع" %}</label>
                    <select class="form-select" id="payment_status" name="payment_status">
                        <option value="">{% trans "الكل" %}</option>
                        {% for status_code, status_name in payment_status_choices %}
                        <option value="{{ status_code }}" {% if payment_status == status_code %}selected{% endif %}>{{ status_name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-4 mb-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-filter me-1"></i> {% trans "تصفية" %}
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Report Header for Print -->
<div class="d-none d-print-block mb-4">
    <div class="text-center">
        <h1>{% trans "تقرير المشتريات" %}</h1>
        <p>
            {% if date_from %}{% trans "من تاريخ" %}: {{ date_from }}{% endif %}
            {% if date_to %} - {% trans "إلى تاريخ" %}: {{ date_to }}{% endif %}
            {% if supplier_id %} - {% trans "المورد" %}: {{ suppliers|filter_by_id:supplier_id|first|get_attr:"name" }}{% endif %}
        </p>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-primary shadow h-100 py-2 summary-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            {% trans "إجمالي الطلبات" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-shopping-cart fa-2x text-primary summary-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-success shadow h-100 py-2 summary-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            {% trans "المبالغ المدفوعة" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ paid_amount|floatformat:2 }} ر.س</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-success summary-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-warning shadow h-100 py-2 summary-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            {% trans "المبالغ المدفوعة جزئياً" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ partial_amount|floatformat:2 }} ر.س</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-percentage fa-2x text-warning summary-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-danger shadow h-100 py-2 summary-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                            {% trans "المبالغ غير المدفوعة" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ unpaid_amount|floatformat:2 }} ر.س</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exclamation-circle fa-2x text-danger summary-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Purchases Table -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">{% trans "قائمة طلبات الشراء" %}</h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover" id="purchasesTable" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th>{% trans "رقم المرجع" %}</th>
                        <th>{% trans "المورد" %}</th>
                        <th>{% trans "التاريخ" %}</th>
                        <th>{% trans "تاريخ التسليم" %}</th>
                        <th>{% trans "المبلغ الإجمالي" %}</th>
                        <th>{% trans "حالة الطلب" %}</th>
                        <th>{% trans "حالة الدفع" %}</th>
                        <th class="no-print">{% trans "الإجراءات" %}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for purchase in purchases %}
                    <tr>
                        <td>{{ purchase.reference_number }}</td>
                        <td>{{ purchase.supplier.name }}</td>
                        <td>{{ purchase.date|date:"Y-m-d" }}</td>
                        <td>
                            {% if purchase.actual_delivery_date %}
                                {{ purchase.actual_delivery_date|date:"Y-m-d" }}
                            {% elif purchase.expected_delivery_date %}
                                {{ purchase.expected_delivery_date|date:"Y-m-d" }} ({% trans "متوقع" %})
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td>{{ purchase.total_amount|floatformat:2 }} ر.س</td>
                        <td>
                            {% if purchase.status == 'pending' %}
                            <span class="badge bg-warning text-dark status-badge">{% trans "معلق" %}</span>
                            {% elif purchase.status == 'received' %}
                            <span class="badge bg-success status-badge">{% trans "تم الاستلام" %}</span>
                            {% elif purchase.status == 'cancelled' %}
                            <span class="badge bg-danger status-badge">{% trans "ملغي" %}</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if purchase.payment_status == 'unpaid' %}
                            <span class="badge bg-danger payment-badge">{% trans "غير مدفوع" %}</span>
                            {% elif purchase.payment_status == 'partial' %}
                            <span class="badge bg-warning text-dark payment-badge">{% trans "مدفوع جزئياً" %}</span>
                            {% elif purchase.payment_status == 'paid' %}
                            <span class="badge bg-success payment-badge">{% trans "مدفوع بالكامل" %}</span>
                            {% endif %}
                        </td>
                        <td class="no-print">
                            <a href="{% url 'purchases:view_purchase' purchase_id=purchase.id %}" class="btn btn-sm btn-info">
                                <i class="fas fa-eye"></i>
                            </a>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="8" class="text-center">{% trans "لا توجد طلبات شراء تطابق معايير البحث" %}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Payment Status Chart -->
<div class="row">
    <div class="col-xl-6 col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">{% trans "توزيع حالة الدفع" %}</h6>
            </div>
            <div class="card-body">
                <div class="chart-pie pt-4">
                    <canvas id="paymentStatusChart"></canvas>
                </div>
                <div class="mt-4 text-center small">
                    <span class="me-2">
                        <i class="fas fa-circle text-success"></i> {% trans "مدفوع بالكامل" %}
                    </span>
                    <span class="me-2">
                        <i class="fas fa-circle text-warning"></i> {% trans "مدفوع جزئياً" %}
                    </span>
                    <span class="me-2">
                        <i class="fas fa-circle text-danger"></i> {% trans "غير مدفوع" %}
                    </span>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-6 col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">{% trans "توزيع حالة الطلبات" %}</h6>
            </div>
            <div class="card-body">
                <div class="chart-pie pt-4">
                    <canvas id="orderStatusChart"></canvas>
                </div>
                <div class="mt-4 text-center small">
                    <span class="me-2">
                        <i class="fas fa-circle text-warning"></i> {% trans "معلق" %}
                    </span>
                    <span class="me-2">
                        <i class="fas fa-circle text-success"></i> {% trans "تم الاستلام" %}
                    </span>
                    <span class="me-2">
                        <i class="fas fa-circle text-danger"></i> {% trans "ملغي" %}
                    </span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Top Purchased Products Chart -->
<div class="row">
    <div class="col-12">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">{% trans "المنتجات الأكثر شراءً" %}</h6>
            </div>
            <div class="card-body">
                <div class="chart-bar pt-4">
                    <canvas id="topProductsChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.bootstrap5.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.print.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
    $(document).ready(function() {
        // Initialize DataTable
        $('#purchasesTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json"
            },
            "order": [[2, "desc"]],
            "pageLength": 25,
            "dom": 'Bfrtip',
            "buttons": [
                {
                    extend: 'excel',
                    text: '<i class="fas fa-file-excel"></i> Excel',
                    className: 'btn btn-success no-print',
                    exportOptions: {
                        columns: ':not(.no-print)'
                    }
                },
                {
                    extend: 'pdf',
                    text: '<i class="fas fa-file-pdf"></i> PDF',
                    className: 'btn btn-danger no-print',
                    exportOptions: {
                        columns: ':not(.no-print)'
                    }
                },
                {
                    extend: 'print',
                    text: '<i class="fas fa-print"></i> Print',
                    className: 'btn btn-info no-print',
                    exportOptions: {
                        columns: ':not(.no-print)'
                    }
                }
            ]
        });

        // Payment Status Chart
        var paymentStatusCtx = document.getElementById("paymentStatusChart");
        var paymentStatusChart = new Chart(paymentStatusCtx, {
            type: 'doughnut',
            data: {
                labels: ["{% trans 'مدفوع بالكامل' %}", "{% trans 'مدفوع جزئياً' %}", "{% trans 'غير مدفوع' %}"],
                datasets: [{
                    data: [{{ paid_amount }}, {{ partial_amount }}, {{ unpaid_amount }}],
                    backgroundColor: ['#1cc88a', '#f6c23e', '#e74a3b'],
                    hoverBackgroundColor: ['#17a673', '#dda20a', '#c23b2b'],
                    hoverBorderColor: "rgba(234, 236, 244, 1)",
                }],
            },
            options: {
                maintainAspectRatio: false,
                tooltips: {
                    backgroundColor: "rgb(255,255,255)",
                    bodyFontColor: "#858796",
                    borderColor: '#dddfeb',
                    borderWidth: 1,
                    xPadding: 15,
                    yPadding: 15,
                    displayColors: false,
                    caretPadding: 10,
                },
                legend: {
                    display: false
                },
                cutoutPercentage: 80,
            },
        });

        // Order Status Chart
        var orderStatusCtx = document.getElementById("orderStatusChart");
        var orderStatusChart = new Chart(orderStatusCtx, {
            type: 'doughnut',
            data: {
                labels: ["{% trans 'معلق' %}", "{% trans 'تم الاستلام' %}", "{% trans 'ملغي' %}"],
                datasets: [{
                    data: [
                        {{ purchases|filter_by_attr:"status,pending"|length }},
                        {{ purchases|filter_by_attr:"status,received"|length }},
                        {{ purchases|filter_by_attr:"status,cancelled"|length }}
                    ],
                    backgroundColor: ['#f6c23e', '#1cc88a', '#e74a3b'],
                    hoverBackgroundColor: ['#dda20a', '#17a673', '#c23b2b'],
                    hoverBorderColor: "rgba(234, 236, 244, 1)",
                }],
            },
            options: {
                maintainAspectRatio: false,
                tooltips: {
                    backgroundColor: "rgb(255,255,255)",
                    bodyFontColor: "#858796",
                    borderColor: '#dddfeb',
                    borderWidth: 1,
                    xPadding: 15,
                    yPadding: 15,
                    displayColors: false,
                    caretPadding: 10,
                },
                legend: {
                    display: false
                },
                cutoutPercentage: 80,
            },
        });

        // Top Products Chart
        var topProductsCtx = document.getElementById("topProductsChart");

        // Get top 10 products from purchase items
        $.ajax({
            url: "{% url 'purchases:ajax/get-top-products' %}",
            type: "GET",
            dataType: "json",
            success: function(data) {
                var topProductsChart = new Chart(topProductsCtx, {
                    type: 'bar',
                    data: {
                        labels: data.product_names,
                        datasets: [{
                            label: "{% trans 'الكمية المشتراة' %}",
                            backgroundColor: "#4e73df",
                            hoverBackgroundColor: "#2e59d9",
                            borderColor: "#4e73df",
                            data: data.quantities,
                        }],
                    },
                    options: {
                        maintainAspectRatio: false,
                        layout: {
                            padding: {
                                left: 10,
                                right: 25,
                                top: 25,
                                bottom: 0
                            }
                        },
                        scales: {
                            xAxes: [{
                                gridLines: {
                                    display: false,
                                    drawBorder: false
                                },
                                ticks: {
                                    maxTicksLimit: 10
                                },
                                maxBarThickness: 25,
                            }],
                            yAxes: [{
                                ticks: {
                                    min: 0,
                                    maxTicksLimit: 5,
                                    padding: 10,
                                },
                                gridLines: {
                                    color: "rgb(234, 236, 244)",
                                    zeroLineColor: "rgb(234, 236, 244)",
                                    drawBorder: false,
                                    borderDash: [2],
                                    zeroLineBorderDash: [2]
                                }
                            }],
                        },
                        legend: {
                            display: false
                        },
                        tooltips: {
                            titleMarginBottom: 10,
                            titleFontColor: '#6e707e',
                            titleFontSize: 14,
                            backgroundColor: "rgb(255,255,255)",
                            bodyFontColor: "#858796",
                            borderColor: '#dddfeb',
                            borderWidth: 1,
                            xPadding: 15,
                            yPadding: 15,
                            displayColors: false,
                            caretPadding: 10,
                        },
                    }
                });
            },
            error: function(xhr, status, error) {
                console.error("Error fetching top products: " + error);
                $("#topProductsChart").parent().html('<div class="alert alert-danger">{% trans "حدث خطأ في تحميل البيانات" %}</div>');
            }
        });
    });
</script>
{% endblock %}
