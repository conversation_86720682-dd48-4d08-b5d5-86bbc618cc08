"""
URL configuration for auto_parts_pos project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.contrib.auth import views as auth_views
from django.views.generic import RedirectView

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', include(('dashboard.urls', 'dashboard'), namespace='dashboard')),
    path('pos/', RedirectView.as_view(url='/', permanent=True)),  # Redirect /pos/ to /
    path('inventory/', include(('inventory.urls', 'inventory'), namespace='inventory')),
    path('customers/', include(('customers.urls', 'customers'), namespace='customers')),
    path('sales/', include(('sales.urls', 'sales'), namespace='sales')),
    # path('purchases/', include(('purchases.urls', 'purchases'), namespace='purchases')),  # Commented out
    path('finance/', include(('finance.urls', 'finance'), namespace='finance')),
    path('employees/', include(('employees.urls', 'employees'), namespace='employees')),
    path('reports/', include(('reports.urls', 'reports'), namespace='reports')),
    path('settings/', include(('settings_app.urls', 'settings_app'), namespace='settings_app')),

    # Authentication URLs
    path('login/', auth_views.LoginView.as_view(template_name='auth/login.html'), name='login'),
    path('logout/', auth_views.LogoutView.as_view(next_page='login'), name='logout'),
    path('password_change/', auth_views.PasswordChangeView.as_view(template_name='auth/password_change.html'), name='password_change'),
    path('password_change/done/', auth_views.PasswordChangeDoneView.as_view(template_name='auth/password_change_done.html'), name='password_change_done'),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
