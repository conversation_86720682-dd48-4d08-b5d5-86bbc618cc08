# Generated by Django 5.2 on 2025-04-19 09:48

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('purchases', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='SupplierCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='الاسم')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'فئة موردين',
                'verbose_name_plural': 'فئات الموردين',
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='supplier',
            name='city',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='المدينة'),
        ),
        migrations.AddField(
            model_name='supplier',
            name='country',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='الدولة'),
        ),
        migrations.CreateModel(
            name='PurchaseInvoice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('invoice_number', models.CharField(max_length=100, unique=True, verbose_name='رقم الفاتورة')),
                ('invoice_date', models.DateField(verbose_name='تاريخ الفاتورة')),
                ('due_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الاستحقاق')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='المبلغ الإجمالي')),
                ('status', models.CharField(choices=[('pending', 'معلقة'), ('verified', 'تم التحقق'), ('paid', 'مدفوعة'), ('cancelled', 'ملغية')], default='pending', max_length=20, verbose_name='حالة الفاتورة')),
                ('invoice_file', models.FileField(blank=True, null=True, upload_to='invoices/purchases/', verbose_name='ملف الفاتورة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('purchase', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='invoices', to='purchases.purchase', verbose_name='الشراء')),
            ],
            options={
                'verbose_name': 'فاتورة شراء',
                'verbose_name_plural': 'فواتير الشراء',
                'ordering': ['-invoice_date'],
            },
        ),
        migrations.AddField(
            model_name='supplierpayment',
            name='invoice',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='payments', to='purchases.purchaseinvoice', verbose_name='الفاتورة'),
        ),
        migrations.AddField(
            model_name='supplier',
            name='category',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='suppliers', to='purchases.suppliercategory', verbose_name='الفئة'),
        ),
    ]
