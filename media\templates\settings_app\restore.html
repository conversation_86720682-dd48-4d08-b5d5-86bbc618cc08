{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "استعادة النسخة الاحتياطية" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/settings.css' %}">
<style>
    .restore-options {
        margin-top: 20px;
        margin-bottom: 30px;
    }
    .backup-info {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    .backup-info h5 {
        margin-bottom: 15px;
        color: #4e73df;
    }
    .backup-info .row {
        margin-bottom: 10px;
    }
    .backup-info .label {
        font-weight: bold;
    }
    .restore-warning {
        color: #e74a3b;
        font-weight: bold;
    }
    .restore-progress {
        margin-top: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{% trans "استعادة النسخة الاحتياطية" %}</h1>
        <div>
            <a href="{% url 'settings_app:backup_logs' %}" class="btn btn-info me-2">
                <i class="fas fa-history me-1"></i> {% trans "سجل العمليات" %}
            </a>
            <a href="{% url 'settings_app:backup' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> {% trans "العودة" %}
            </a>
        </div>
    </div>

    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    <div class="row">
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "استعادة من ملف" %}</h6>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label for="backupFile" class="form-label">{% trans "ملف النسخة الاحتياطية" %}</label>
                            <input type="file" class="form-control" id="backupFile" name="backup_file" accept=".zip,.sql,.gz" required>
                            <small class="form-text text-muted">{% trans "الملفات المدعومة: .zip, .sql, .gz" %}</small>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="overwriteData" name="overwrite_data">
                                <label class="form-check-label" for="overwriteData">
                                    {% trans "استبدال البيانات الحالية" %}
                                </label>
                            </div>
                            <small class="form-text text-muted">{% trans "إذا لم يتم تحديد هذا الخيار، سيتم دمج البيانات المستعادة مع البيانات الحالية." %}</small>
                        </div>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-1"></i> {% trans "تحذير: استعادة النسخة الاحتياطية قد تؤدي إلى فقدان البيانات الحالية. يرجى التأكد من وجود نسخة احتياطية حديثة قبل المتابعة." %}
                        </div>
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-upload me-1"></i> {% trans "استعادة" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "استعادة من النسخ الاحتياطية المتاحة" %}</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="thead-light">
                                <tr>
                                    <th>{% trans "اسم الملف" %}</th>
                                    <th>{% trans "التاريخ" %}</th>
                                    <th>{% trans "الإجراء" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for log in backup_logs %}
                                <tr>
                                    <td>{{ log.file_name }}</td>
                                    <td>{{ log.created_at|date:"Y-m-d H:i" }}</td>
                                    <td>
                                        <form method="post" action="{% url 'settings_app:restore' %}">
                                            {% csrf_token %}
                                            <input type="hidden" name="backup_id" value="{{ log.id }}">
                                            <button type="submit" class="btn btn-sm btn-primary restore-backup">
                                                <i class="fas fa-upload me-1"></i> {% trans "استعادة" %}
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="3" class="text-center">{% trans "لا توجد نسخ احتياطية متاحة" %}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "معلومات مفيدة" %}</h6>
                </div>
                <div class="card-body">
                    <h5>{% trans "ماذا تتضمن عملية الاستعادة؟" %}</h5>
                    <p>{% trans "عملية استعادة النسخة الاحتياطية تشمل:" %}</p>
                    <ul>
                        <li>{% trans "استعادة قاعدة البيانات (المنتجات، العملاء، المبيعات، إلخ)" %}</li>
                        <li>{% trans "استعادة الملفات المرفقة (صور المنتجات، الشعارات، إلخ)" %}</li>
                        <li>{% trans "استعادة إعدادات النظام" %}</li>
                    </ul>

                    <h5>{% trans "نصائح هامة" %}</h5>
                    <ul>
                        <li>{% trans "قم بإنشاء نسخة احتياطية من البيانات الحالية قبل الاستعادة" %}</li>
                        <li>{% trans "تأكد من أن النسخة الاحتياطية متوافقة مع إصدار النظام الحالي" %}</li>
                        <li>{% trans "قد تستغرق عملية الاستعادة بعض الوقت اعتمادًا على حجم البيانات" %}</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Restore Backup Button - now handled by form submit
        $('.restore-backup').click(function() {
            return confirm('{% trans "هل أنت متأكد من رغبتك في استعادة هذه النسخة الاحتياطية؟ قد يؤدي ذلك إلى فقدان البيانات الحالية." %}');
        });
    });
</script>
{% endblock %}
