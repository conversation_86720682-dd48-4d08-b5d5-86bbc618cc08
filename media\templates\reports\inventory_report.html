{% extends 'base.html' %}
{% load i18n %}
{% load static %}
{% load report_filters %}

{% block title %}{% trans "تقرير المخزون" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/daterangepicker.css' %}">
<style>
    .chart-container {
        position: relative;
        height: 300px;
    }

    .filter-section {
        background: #f8f9fc;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        border: 1px solid #e3e6f0;
    }

    .search-section {
        background: white;
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    }

    .alert-section {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1.5rem;
    }

    .status-available { color: #1cc88a; font-weight: bold; }
    .status-low { color: #f6c23e; font-weight: bold; }
    .status-out { color: #e74a3b; font-weight: bold; }

    .product-image {
        width: 40px;
        height: 40px;
        object-fit: cover;
        border-radius: 5px;
    }

    .quantity-badge {
        font-size: 0.9rem;
        padding: 0.4rem 0.8rem;
    }

    .value-display {
        font-weight: bold;
        color: #5a5c69;
    }

    .alert-item {
        display: flex;
        align-items: center;
        padding: 0.5rem;
        margin-bottom: 0.5rem;
        background: white;
        border-radius: 5px;
        border-left: 4px solid #f6c23e;
    }

    .alert-item.critical {
        border-left-color: #e74a3b;
    }

    .btn-group .btn {
        margin: 0.25rem;
    }

    .border-right-purple {
        border-right: 0.25rem solid #6f42c1 !important;
    }

    .text-purple {
        color: #6f42c1 !important;
    }

    .border-right-dark {
        border-right: 0.25rem solid #5a5c69 !important;
    }

    .border-right-secondary {
        border-right: 0.25rem solid #858796 !important;
    }

    .border-right-info {
        border-right: 0.25rem solid #36b9cc !important;
    }

    @media (max-width: 768px) {
        .filter-section {
            padding: 1rem;
        }

        .table-responsive {
            font-size: 0.85rem;
        }

        .btn-group {
            flex-direction: column;
        }

        .btn-group .btn {
            margin-bottom: 2px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    {% csrf_token %}

    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-warehouse me-2"></i>{% trans "تقرير المخزون" %}
        </h1>
        <div>
            <a href="{% url 'reports:index' %}" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left me-1"></i> {% trans "العودة" %}
            </a>
            <div class="btn-group">
                <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-download me-1"></i> {% trans "تصدير" %}
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="#" onclick="exportReport('pdf')">
                        <i class="fas fa-file-pdf me-2"></i>PDF
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="exportReport('excel')">
                        <i class="fas fa-file-excel me-2"></i>Excel
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="exportReport('csv')">
                        <i class="fas fa-file-csv me-2"></i>CSV
                    </a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Search Section -->
    <div class="search-section">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" class="form-control" id="globalSearch"
                           placeholder="{% trans 'البحث في المنتجات (الاسم، الكود، الفئة...)' %}">
                    <button class="btn btn-outline-secondary" type="button" id="clearSearch">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="col-md-4 text-end">
                <button class="btn btn-info" type="button" data-bs-toggle="collapse"
                        data-bs-target="#filterSection" aria-expanded="false">
                    <i class="fas fa-filter me-1"></i>{% trans "فلاتر متقدمة" %}
                </button>
            </div>
        </div>
    </div>

    <!-- Advanced Filters Section -->
    <div class="collapse" id="filterSection">
        <div class="filter-section">
            <form id="filterForm" method="GET">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label for="category" class="form-label">
                            <i class="fas fa-tags me-1"></i>{% trans "الفئة" %}
                        </label>
                        <select class="form-select" id="category" name="category">
                            <option value="">{% trans "جميع الفئات" %}</option>
                            {% for category in categories %}
                                <option value="{{ category.id }}" {% if selected_category == category.id|stringformat:"s" %}selected{% endif %}>
                                    {{ category.name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="col-md-3 mb-3">
                        <label for="status" class="form-label">
                            <i class="fas fa-info-circle me-1"></i>{% trans "الحالة" %}
                        </label>
                        <select class="form-select" id="status" name="status">
                            <option value="">{% trans "جميع الحالات" %}</option>
                            <option value="available" {% if selected_status == 'available' %}selected{% endif %}>
                                <span class="text-success">✓ {% trans "متوفر" %}</span>
                            </option>
                            <option value="low" {% if selected_status == 'low' %}selected{% endif %}>
                                <span class="text-warning">⚠ {% trans "مخزون منخفض" %}</span>
                            </option>
                            <option value="out" {% if selected_status == 'out' %}selected{% endif %}>
                                <span class="text-danger">✗ {% trans "نفاد المخزون" %}</span>
                            </option>
                        </select>
                    </div>

                    <div class="col-md-3 mb-3">
                        <label for="quantity_min" class="form-label">
                            <i class="fas fa-sort-numeric-up me-1"></i>{% trans "الكمية الأدنى" %}
                        </label>
                        <input type="number" class="form-control" id="quantity_min" name="quantity_min"
                               value="{{ quantity_min }}" placeholder="0" min="0">
                    </div>

                    <div class="col-md-3 mb-3">
                        <label for="quantity_max" class="form-label">
                            <i class="fas fa-sort-numeric-down me-1"></i>{% trans "الكمية الأعلى" %}
                        </label>
                        <input type="number" class="form-control" id="quantity_max" name="quantity_max"
                               value="{{ quantity_max }}" placeholder="1000" min="0">
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="storage_location" class="form-label">
                            <i class="fas fa-map-marker-alt me-1"></i>{% trans "موقع التخزين" %}
                        </label>
                        <select class="form-select" id="storage_location" name="storage_location">
                            <option value="">{% trans "جميع المواقع" %}</option>
                            {% for location in storage_locations %}
                                <option value="{{ location.id }}" {% if selected_location == location.id|stringformat:"s" %}selected{% endif %}>
                                    {{ location.name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="last_updated" class="form-label">
                            <i class="fas fa-calendar me-1"></i>{% trans "آخر تحديث" %}
                        </label>
                        <select class="form-select" id="last_updated" name="last_updated">
                            <option value="">{% trans "أي وقت" %}</option>
                            <option value="today" {% if selected_period == 'today' %}selected{% endif %}>{% trans "اليوم" %}</option>
                            <option value="week" {% if selected_period == 'week' %}selected{% endif %}>{% trans "هذا الأسبوع" %}</option>
                            <option value="month" {% if selected_period == 'month' %}selected{% endif %}>{% trans "هذا الشهر" %}</option>
                            <option value="quarter" {% if selected_period == 'quarter' %}selected{% endif %}>{% trans "هذا الربع" %}</option>
                        </select>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <div class="d-flex justify-content-between">
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-filter me-1"></i>{% trans "تطبيق الفلاتر" %}
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="clearFilters()">
                                    <i class="fas fa-undo me-1"></i>{% trans "إعادة تعيين" %}
                                </button>
                            </div>
                            <div>
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    {% trans "عدد النتائج" %}: <span id="resultsCount">{{ products.count }}</span>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Alerts Section -->
    {% if low_stock_products or out_of_stock_products %}
    <div class="alert-section">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h5 class="mb-0">
                <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                {% trans "تنبيهات المخزون" %}
            </h5>
            <button class="btn btn-sm btn-outline-warning" type="button" data-bs-toggle="collapse"
                    data-bs-target="#alertsDetails" aria-expanded="false">
                <i class="fas fa-eye me-1"></i>{% trans "عرض التفاصيل" %}
            </button>
        </div>

        <div class="row">
            {% if out_of_stock_products %}
            <div class="col-md-6">
                <div class="alert alert-danger">
                    <h6><i class="fas fa-times-circle me-2"></i>{% trans "منتجات نفد مخزونها" %} ({{ out_of_stock_products.count }})</h6>
                    <div class="collapse" id="alertsDetails">
                        {% for product in out_of_stock_products|slice:":5" %}
                        <div class="alert-item critical">
                            <div class="flex-grow-1">
                                <strong>{{ product.name }}</strong>
                                <br><small class="text-muted">{{ product.code }}</small>
                            </div>
                            <div>
                                <a href="{% url 'inventory:edit_product' product.id %}" class="btn btn-sm btn-outline-danger">
                                    <i class="fas fa-plus me-1"></i>إضافة مخزون
                                </a>
                            </div>
                        </div>
                        {% endfor %}
                        {% if out_of_stock_products.count > 5 %}
                        <small class="text-muted">{% trans "و" %} {{ out_of_stock_products.count|add:"-5" }} {% trans "منتجات أخرى..." %}</small>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endif %}

            {% if low_stock_products %}
            <div class="col-md-6">
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>{% trans "منتجات مخزونها منخفض" %} ({{ low_stock_products.count }})</h6>
                    <div class="collapse" id="alertsDetails">
                        {% for product in low_stock_products|slice:":5" %}
                        <div class="alert-item">
                            <div class="flex-grow-1">
                                <strong>{{ product.name }}</strong>
                                <br><small class="text-muted">{{ product.code }} - متبقي: {{ product.quantity }}</small>
                            </div>
                            <div>
                                <a href="{% url 'inventory:edit_product' product.id %}" class="btn btn-sm btn-outline-warning">
                                    <i class="fas fa-plus me-1"></i>إضافة مخزون
                                </a>
                            </div>
                        </div>
                        {% endfor %}
                        {% if low_stock_products.count > 5 %}
                        <small class="text-muted">{% trans "و" %} {{ low_stock_products.count|add:"-5" }} {% trans "منتجات أخرى..." %}</small>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- Inventory Summary Cards -->
    <div class="row">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                {% trans "إجمالي المنتجات" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_products }}</div>
                            <div class="small mt-2">
                                <span class="text-success">
                                    <i class="fas fa-check-circle me-1"></i>{{ available_products }} {% trans "متوفر" %}
                                </span>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-boxes fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                {% trans "قيمة المخزون الإجمالية" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_value|floatformat:2 }} {% trans "د.م." %}</div>
                            <div class="small mt-2">
                                <span class="text-info">
                                    <i class="fas fa-chart-line me-1"></i>متوسط القيمة: {{ average_value|floatformat:2 }} د.م
                                </span>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                {% trans "مخزون منخفض" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ low_stock_count }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                {% trans "نفاد المخزون" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ out_of_stock_count }}</div>
                            <div class="small mt-2">
                                <span class="text-danger">
                                    <i class="fas fa-exclamation-triangle me-1"></i>يحتاج إعادة تعبئة
                                </span>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Statistics Row -->
    <div class="row">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                {% trans "إجمالي الكمية" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_quantity }}</div>
                            <div class="small mt-2">
                                <span class="text-muted">
                                    <i class="fas fa-cubes me-1"></i>قطعة في المخزون
                                </span>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-cubes fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-secondary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">
                                {% trans "عدد الفئات" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_categories }}</div>
                            <div class="small mt-2">
                                <span class="text-muted">
                                    <i class="fas fa-tags me-1"></i>فئة مختلفة
                                </span>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-tags fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-dark shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-dark text-uppercase mb-1">
                                {% trans "أعلى قيمة منتج" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ highest_value_product.total_value|floatformat:2 }} {% trans "د.م." %}</div>
                            <div class="small mt-2">
                                <span class="text-muted">
                                    <i class="fas fa-crown me-1"></i>{{ highest_value_product.name|truncatechars:15 }}
                                </span>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-crown fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-purple shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-purple text-uppercase mb-1">
                                {% trans "الأكثر مبيعاً" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ top_selling_product.total_sold|default:0 }}</div>
                            <div class="small mt-2">
                                <span class="text-muted">
                                    <i class="fas fa-fire me-1"></i>{{ top_selling_product.name|truncatechars:15|default:"لا يوجد" }}
                                </span>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-fire fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Inventory Value by Category Chart -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "قيمة المخزون حسب الفئة" %}</h6>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="categoryValueChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Inventory Status Chart -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "حالة المخزون" %}</h6>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="inventoryStatusChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Movements -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">{% trans "حركات المخزون الأخيرة" %}</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="movementsTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>{% trans "المنتج" %}</th>
                            <th>{% trans "النوع" %}</th>
                            <th>{% trans "الكمية" %}</th>
                            <th>{% trans "المصدر" %}</th>
                            <th>{% trans "التاريخ" %}</th>
                            <th>{% trans "ملاحظات" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for movement in recent_movements %}
                        <tr>
                            <td>{{ movement.product.name }}</td>
                            <td>
                                {% if movement.movement_type == 'in' %}
                                <span class="badge bg-success">{% trans "وارد" %}</span>
                                {% elif movement.movement_type == 'out' %}
                                <span class="badge bg-danger">{% trans "صادر" %}</span>
                                {% elif movement.movement_type == 'adjustment' %}
                                <span class="badge bg-warning">{% trans "تعديل" %}</span>
                                {% endif %}
                            </td>
                            <td>{{ movement.quantity }}</td>
                            <td>{{ movement.source }}</td>
                            <td>{{ movement.created_at|date:"Y-m-d H:i" }}</td>
                            <td>{{ movement.notes|default:"-" }}</td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center">{% trans "لا توجد حركات مخزون" %}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Inventory Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-list me-2"></i>{% trans "قائمة المخزون التفصيلية" %}
            </h6>
            <div>
                <span class="badge bg-info">
                    <i class="fas fa-items-alt me-1"></i>
                    <span id="tableResultsCount">{{ products.count }}</span> {% trans "منتج" %}
                </span>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="inventoryTable" width="100%" cellspacing="0">
                    <thead class="table-dark">
                        <tr>
                            <th width="60px">{% trans "صورة" %}</th>
                            <th>{% trans "اسم المنتج" %}</th>
                            <th>{% trans "رمز المنتج" %}</th>
                            <th>{% trans "الفئة" %}</th>
                            <th>{% trans "الكمية المتوفرة" %}</th>
                            <th>{% trans "الحد الأدنى" %}</th>
                            <th>{% trans "الوحدة" %}</th>
                            <th>{% trans "سعر الوحدة" %}</th>
                            <th>{% trans "القيمة الإجمالية" %}</th>
                            <th>{% trans "موقع التخزين" %}</th>
                            <th>{% trans "الحالة" %}</th>
                            <th>{% trans "الإجراءات" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for product in products %}
                        <tr class="product-row" data-product-id="{{ product.id }}">
                            <td>
                                {% if product.image %}
                                    <img src="{{ product.image.url }}" alt="{{ product.name }}" class="product-image">
                                {% else %}
                                    <div class="product-image bg-light d-flex align-items-center justify-content-center">
                                        <i class="fas fa-image text-muted"></i>
                                    </div>
                                {% endif %}
                            </td>
                            <td>
                                <div>
                                    <strong>{{ product.name }}</strong>
                                    {% if product.description %}
                                        <br><small class="text-muted">{{ product.description|truncatechars:50 }}</small>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                <code>{{ product.code }}</code>
                                {% if product.barcode %}
                                    <br><small class="text-muted">
                                        <i class="fas fa-barcode me-1"></i>{{ product.barcode }}
                                    </small>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-secondary">{{ product.category.name }}</span>
                            </td>
                            <td>
                                <span class="quantity-badge badge
                                    {% if product.quantity <= 0 %}bg-danger
                                    {% elif product.quantity <= product.min_quantity %}bg-warning
                                    {% else %}bg-success{% endif %}">
                                    {{ product.quantity }}
                                </span>
                            </td>
                            <td>
                                <span class="text-muted">{{ product.min_quantity }}</span>
                            </td>
                            <td>
                                <span class="badge bg-light text-dark">{{ product.display_unit }}</span>
                            </td>
                            <td>
                                <div class="value-display">{{ product.selling_price|floatformat:2 }} د.م</div>
                                <small class="text-muted">شراء: {{ product.purchase_price|floatformat:2 }} د.م</small>
                            </td>
                            <td>
                                <div class="value-display">
                                    {% widthratio product.quantity 1 product.selling_price as total_value %}
                                    {{ total_value|floatformat:2 }} د.م
                                </div>
                                <small class="text-success">
                                    {% widthratio product.selling_price 1 product.purchase_price as profit_ratio %}
                                    {% widthratio profit_ratio 1 product.purchase_price as profit_percent %}
                                    ربح: {{ profit_percent|floatformat:1 }}%
                                </small>
                            </td>
                            <td>
                                {% if product.storage_location %}
                                    <span class="badge bg-info">{{ product.storage_location.name }}</span>
                                {% else %}
                                    <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if product.quantity <= 0 %}
                                    <span class="badge bg-danger status-out">
                                        <i class="fas fa-times-circle me-1"></i>نفاد المخزون
                                    </span>
                                {% elif product.quantity <= product.min_quantity %}
                                    <span class="badge bg-warning status-low">
                                        <i class="fas fa-exclamation-triangle me-1"></i>مخزون منخفض
                                    </span>
                                {% else %}
                                    <span class="badge bg-success status-available">
                                        <i class="fas fa-check-circle me-1"></i>متوفر
                                    </span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{% url 'inventory:edit_product' product.id %}"
                                       class="btn btn-sm btn-outline-primary" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'inventory:product_detail' product.id %}"
                                       class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    {% if product.quantity <= product.min_quantity %}
                                        <button class="btn btn-sm btn-outline-warning restock-btn"
                                                data-product-id="{{ product.id }}" title="إعادة تعبئة">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="12" class="text-center py-4">
                                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                <p class="text-muted">{% trans "لا توجد منتجات تطابق معايير البحث" %}</p>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/chart.min.js' %}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize DataTables with Advanced Features
        var inventoryTable = $('#inventoryTable').DataTable({
            "language": {
                "url": "{% static 'js/dataTables.arabic.json' %}",
                "lengthMenu": "إظهار _MENU_ من العناصر",
                "info": "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل",
                "search": "البحث:",
                "paginate": {
                    "first": "الأول",
                    "last": "الأخير",
                    "next": "التالي",
                    "previous": "السابق"
                }
            },
            "order": [[1, "asc"]],
            "pageLength": 25,
            "lengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, "الكل"]],
            "dom": 'Bfrtip',
            "buttons": [
                {
                    extend: 'excel',
                    text: '<i class="fas fa-file-excel"></i> Excel',
                    className: 'btn btn-success btn-sm'
                },
                {
                    extend: 'pdf',
                    text: '<i class="fas fa-file-pdf"></i> PDF',
                    className: 'btn btn-danger btn-sm'
                },
                {
                    extend: 'csv',
                    text: '<i class="fas fa-file-csv"></i> CSV',
                    className: 'btn btn-info btn-sm'
                },
                {
                    extend: 'print',
                    text: '<i class="fas fa-print"></i> طباعة',
                    className: 'btn btn-secondary btn-sm'
                }
            ],
            "columnDefs": [
                { "orderable": false, "targets": [0, 11] }, // صورة وإجراءات غير قابلة للترتيب
                { "searchable": false, "targets": [0, 11] }
            ],
            "responsive": true,
            "scrollX": true
        });

        $('#movementsTable').DataTable({
            "language": {
                "url": "{% static 'js/dataTables.arabic.json' %}",
                "lengthMenu": "إظهار _MENU_ من العناصر",
                "info": "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل"
            },
            "order": [[4, "desc"]],
            "pageLength": 10,
            "lengthMenu": [[5, 10, 15, 20, 25, 50, -1], [5, 10, 15, 20, 25, 50, "الكل"]],
            "dom": 'lfrtip'
        });

        // Global Search Functionality
        $('#globalSearch').on('keyup', function() {
            inventoryTable.search(this.value).draw();
            updateResultsCount();
        });

        // Clear Search
        $('#clearSearch').on('click', function() {
            $('#globalSearch').val('');
            inventoryTable.search('').draw();
            updateResultsCount();
        });

        // Update results count
        function updateResultsCount() {
            var info = inventoryTable.page.info();
            $('#resultsCount').text(info.recordsDisplay);
            $('#tableResultsCount').text(info.recordsDisplay);
        }

        // Filter form submission
        $('#filterForm').on('submit', function(e) {
            e.preventDefault();
            applyFilters();
        });

        // Apply filters function
        function applyFilters() {
            var formData = $('#filterForm').serialize();

            // Show loading
            showLoading();

            // Make AJAX request
            $.get(window.location.pathname, formData)
                .done(function(data) {
                    // Reload page with new data
                    window.location.search = formData;
                })
                .fail(function() {
                    hideLoading();
                    showAlert('error', 'حدث خطأ أثناء تطبيق الفلاتر');
                });
        }

        // Clear filters
        window.clearFilters = function() {
            $('#filterForm')[0].reset();
            window.location.href = window.location.pathname;
        };

        // Export functions
        window.exportReport = function(format) {
            var currentFilters = $('#filterForm').serialize();
            var exportUrl = '{% url "reports:export_inventory_report" %}?format=' + format;
            if (currentFilters) {
                exportUrl += '&' + currentFilters;
            }
            window.open(exportUrl, '_blank');
        };

        // Restock button functionality
        $('.restock-btn').on('click', function() {
            var productId = $(this).data('product-id');
            var productName = $(this).closest('tr').find('td:nth-child(2) strong').text();

            if (confirm('هل تريد إضافة مخزون للمنتج: ' + productName + '؟')) {
                window.location.href = '{% url "inventory:edit_product" 0 %}'.replace('0', productId);
            }
        });

        // Loading and alert functions
        function showLoading() {
            $('body').append('<div id="loadingOverlay" class="position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center" style="background: rgba(0,0,0,0.5); z-index: 9999;"><div class="spinner-border text-light" role="status"><span class="visually-hidden">جاري التحميل...</span></div></div>');
        }

        function hideLoading() {
            $('#loadingOverlay').remove();
        }

        function showAlert(type, message) {
            var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
            var alertHtml = '<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert">' +
                message + '<button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>';
            $('.container-fluid').prepend(alertHtml);

            setTimeout(function() {
                $('.alert').fadeOut();
            }, 5000);
        }

        // Inventory Status Chart
        const statusCtx = document.getElementById('inventoryStatusChart').getContext('2d');
        const inventoryStatusChart = new Chart(statusCtx, {
            type: 'pie',
            data: {
                labels: [
                    '{% trans "متوفر" %}',
                    '{% trans "مخزون منخفض" %}',
                    '{% trans "نفاد المخزون" %}'
                ],
                datasets: [{
                    data: [
                        {{ total_products|subtract:low_stock_count|subtract:out_of_stock_count }},
                        {{ low_stock_count }},
                        {{ out_of_stock_count }}
                    ],
                    backgroundColor: [
                        '#1cc88a',
                        '#f6c23e',
                        '#e74a3b'
                    ],
                    hoverBackgroundColor: [
                        '#17a673',
                        '#dda20a',
                        '#be2617'
                    ],
                    hoverBorderColor: "rgba(234, 236, 244, 1)",
                }]
            },
            options: {
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    },
                    tooltip: {
                        backgroundColor: "rgb(255,255,255)",
                        bodyColor: "#858796",
                        borderColor: '#dddfeb',
                        borderWidth: 1,
                        xPadding: 15,
                        yPadding: 15,
                        displayColors: false,
                        caretPadding: 10,
                    }
                },
                cutout: '70%'
            }
        });

        // Category Value Chart - This is a placeholder, you would need to modify this with actual data
        const categoryCtx = document.getElementById('categoryValueChart').getContext('2d');
        const categoryValueChart = new Chart(categoryCtx, {
            type: 'bar',
            data: {
                labels: [
                    // This would be populated with actual category names from your data
                    '{% trans "محركات" %}',
                    '{% trans "فرامل" %}',
                    '{% trans "تعليق" %}',
                    '{% trans "كهرباء" %}',
                    '{% trans "زيوت" %}',
                    '{% trans "فلاتر" %}'
                ],
                datasets: [{
                    label: '{% trans "قيمة المخزون" %}',
                    data: [
                        // This would be populated with actual values from your data
                        5000, 3000, 2500, 1800, 1200, 900
                    ],
                    backgroundColor: 'rgba(78, 115, 223, 0.8)',
                    borderColor: 'rgba(78, 115, 223, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value + ' {% trans "د.م." %}';
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                var label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.parsed.y !== null) {
                                    label += context.parsed.y + ' {% trans "د.م." %}';
                                }
                                return label;
                            }
                        }
                    }
                }
            }
        });
    });
</script>
{% endblock %}
