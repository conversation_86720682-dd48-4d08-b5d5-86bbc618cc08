{% load i18n %}
{% load static %}

<div class="row">
    <!-- Customer Information -->
    <div class="col-md-6">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h6 class="mb-0">
                    <i class="fas fa-user me-2"></i>بيانات العميل
                </h6>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-4">
                        <div class="customer-avatar mx-auto" style="width: 60px; height: 60px; font-size: 24px;">
                            {{ customer.name|first|upper }}
                        </div>
                    </div>
                    <div class="col-8">
                        <h5 class="mb-1">{{ customer.name }}</h5>
                        {% if customer.company %}
                            <p class="text-muted mb-0">{{ customer.company }}</p>
                        {% endif %}
                        <span class="badge {% if customer.is_active %}bg-success{% else %}bg-secondary{% endif %}">
                            {% if customer.is_active %}نشط{% else %}غير نشط{% endif %}
                        </span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <table class="table table-sm">
                            <tr>
                                <td><strong><i class="fas fa-phone me-2"></i>الهاتف:</strong></td>
                                <td>{{ customer.phone }}</td>
                            </tr>
                            {% if customer.phone2 %}
                            <tr>
                                <td><strong><i class="fas fa-phone me-2"></i>هاتف 2:</strong></td>
                                <td>{{ customer.phone2 }}</td>
                            </tr>
                            {% endif %}
                            {% if customer.email %}
                            <tr>
                                <td><strong><i class="fas fa-envelope me-2"></i>البريد:</strong></td>
                                <td>{{ customer.email }}</td>
                            </tr>
                            {% endif %}
                            {% if customer.address %}
                            <tr>
                                <td><strong><i class="fas fa-map-marker-alt me-2"></i>العنوان:</strong></td>
                                <td>{{ customer.address }}</td>
                            </tr>
                            {% endif %}
                            {% if customer.city %}
                            <tr>
                                <td><strong><i class="fas fa-city me-2"></i>المدينة:</strong></td>
                                <td>{{ customer.city }}</td>
                            </tr>
                            {% endif %}
                            <tr>
                                <td><strong><i class="fas fa-calendar me-2"></i>تاريخ التسجيل:</strong></td>
                                <td>{{ customer.created_at|date:"Y-m-d" }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Customer Statistics -->
    <div class="col-md-6">
        <div class="card h-100">
            <div class="card-header bg-success text-white">
                <h6 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>إحصائيات المشتريات
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="border rounded p-3">
                            <h4 class="text-success mb-1">{{ total_spent|floatformat:2 }}</h4>
                            <small class="text-muted">إجمالي المشتريات (د.م)</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="border rounded p-3">
                            <h4 class="text-info mb-1">{{ total_invoices }}</h4>
                            <small class="text-muted">عدد الفواتير</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="border rounded p-3">
                            <h4 class="text-warning mb-1">{{ average_invoice|floatformat:2 }}</h4>
                            <small class="text-muted">متوسط الفاتورة (د.م)</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="border rounded p-3">
                            <h4 class="text-purple mb-1">{{ loyalty_points }}</h4>
                            <small class="text-muted">نقاط الولاء</small>
                        </div>
                    </div>
                </div>

                {% if last_purchase %}
                <div class="alert alert-info">
                    <strong><i class="fas fa-shopping-cart me-2"></i>آخر شراء:</strong>
                    <br>{{ last_purchase.date|date:"Y-m-d" }} - {{ last_purchase.total_amount|floatformat:2 }} د.م
                    <br><small class="text-muted">فاتورة رقم: {{ last_purchase.invoice_number }}</small>
                </div>
                {% else %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>لا توجد مشتريات سابقة
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Purchase History -->
{% if sales %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">
                    <i class="fas fa-history me-2"></i>سجل المشتريات (آخر 10 فواتير)
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>التاريخ</th>
                                <th>المبلغ</th>
                                <th>حالة الدفع</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for sale in sales %}
                            <tr>
                                <td>
                                    <strong>{{ sale.invoice_number }}</strong>
                                </td>
                                <td>{{ sale.date|date:"Y-m-d H:i" }}</td>
                                <td>
                                    <span class="text-success font-weight-bold">
                                        {{ sale.total_amount|floatformat:2 }} د.م
                                    </span>
                                </td>
                                <td>
                                    {% if sale.is_paid %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-check me-1"></i>مدفوع
                                        </span>
                                    {% else %}
                                        <span class="badge bg-danger">
                                            <i class="fas fa-times me-1"></i>غير مدفوع
                                        </span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{% url 'sales:view_sale' sale.id %}"
                                       class="btn btn-sm btn-outline-primary" target="_blank">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'sales:invoice' sale.id %}"
                                       class="btn btn-sm btn-outline-secondary" target="_blank">
                                        <i class="fas fa-print"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Action Buttons -->
<div class="row mt-4">
    <div class="col-12 text-center">
        <a href="{% url 'customers:edit_customer' customer.id %}" class="btn btn-primary me-2">
            <i class="fas fa-edit me-1"></i>تعديل بيانات العميل
        </a>
        <a href="{% url 'sales:new_sale' %}?customer={{ customer.id }}" class="btn btn-success me-2">
            <i class="fas fa-plus me-1"></i>إنشاء فاتورة جديدة
        </a>
        {% if customer.phone %}
        <a href="tel:{{ customer.phone }}" class="btn btn-info me-2">
            <i class="fas fa-phone me-1"></i>اتصال
        </a>
        {% endif %}
        {% if customer.email %}
        <a href="mailto:{{ customer.email }}" class="btn btn-secondary">
            <i class="fas fa-envelope me-1"></i>إرسال بريد
        </a>
        {% endif %}
    </div>
</div>

<style>
.customer-avatar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    border-radius: 50%;
}

.text-purple {
    color: #6f42c1 !important;
}
</style>
