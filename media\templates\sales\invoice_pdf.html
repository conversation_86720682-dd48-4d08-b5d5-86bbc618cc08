{% extends 'invoice_base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "فاتورة" %} #{{ sale.invoice_number }} | {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    /* Add styles optimized for PDF printing */
    body {
        font-family: 'Tajawal', sans-serif;
        font-size: 10pt;
    }
    .invoice-container {
        width: 100%;
        padding: 10mm;
    }
    .no-print {
        display: none !important;
    }
    .action-buttons {
        display: none;
    }
    /* Add other necessary styles for PDF */
</style>
{% endblock %}

{% block content %}
<div class="invoice-container">
    <!-- Invoice content same as invoice.html but without action buttons -->
    {% include 'sales/invoice_content.html' %}
</div>
{% endblock %}

{% block extra_js %}
<!-- Remove all JavaScript for PDF version -->
{% endblock %}