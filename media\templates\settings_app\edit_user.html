{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/settings.css' %}">
{% endblock %}

{% block title %}{% trans "تعديل المستخدم" %} | {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{% trans "تعديل المستخدم" %}: {{ user_obj.username }}</h1>
        <a href="{% url 'settings_app:users' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i> {% trans "العودة" %}
        </a>
    </div>

    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">{% trans "معلومات المستخدم" %}</h6>
        </div>
        <div class="card-body">
            <form method="post">
                {% csrf_token %}

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="username" class="form-label">{% trans "اسم المستخدم" %}</label>
                        <input type="text" class="form-control" id="username" value="{{ user_obj.username }}" readonly>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="email" class="form-label">{% trans "البريد الإلكتروني" %}</label>
                        <input type="email" class="form-control" id="email" name="email" value="{{ user_obj.email }}">
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="first_name" class="form-label">{% trans "الاسم الأول" %}</label>
                        <input type="text" class="form-control" id="first_name" name="first_name" value="{{ user_obj.first_name }}">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="last_name" class="form-label">{% trans "الاسم الأخير" %}</label>
                        <input type="text" class="form-control" id="last_name" name="last_name" value="{{ user_obj.last_name }}">
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="new_password" class="form-label">{% trans "كلمة المرور الجديدة" %}</label>
                        <input type="password" class="form-control" id="new_password" name="new_password">
                        <small class="form-text text-muted">{% trans "اتركها فارغة إذا كنت لا ترغب في تغيير كلمة المرور" %}</small>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="confirm_password" class="form-label">{% trans "تأكيد كلمة المرور" %}</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">{% trans "المجموعات" %}</label>
                        <div class="card">
                            <div class="card-body" style="max-height: 200px; overflow-y: auto;">
                                {% for group in groups %}
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="groups" value="{{ group.id }}" id="group_{{ group.id }}"
                                        {% if group in user_obj.groups.all %}checked{% endif %}>
                                    <label class="form-check-label" for="group_{{ group.id }}">
                                        {{ group.name }}
                                    </label>
                                </div>
                                {% empty %}
                                <p class="text-muted">{% trans "لا توجد مجموعات" %}</p>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">{% trans "الخيارات" %}</label>
                        <div class="card">
                            <div class="card-body">
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" name="is_active" id="is_active" {% if user_obj.is_active %}checked{% endif %}>
                                    <label class="form-check-label" for="is_active">
                                        {% trans "نشط" %}
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="is_superuser" id="is_superuser" {% if user_obj.is_superuser %}checked{% endif %}>
                                    <label class="form-check-label" for="is_superuser">
                                        {% trans "مدير النظام" %}
                                    </label>
                                    <small class="form-text text-muted d-block">{% trans "يمنح جميع الصلاحيات بما في ذلك الوصول إلى لوحة الإدارة" %}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> {% trans "حفظ التغييرات" %}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Password confirmation validation
        const passwordInput = document.getElementById('new_password');
        const confirmPasswordInput = document.getElementById('confirm_password');
        const form = document.querySelector('form');

        form.addEventListener('submit', function(event) {
            if (passwordInput.value && passwordInput.value !== confirmPasswordInput.value) {
                event.preventDefault();
                alert('{% trans "كلمات المرور غير متطابقة" %}');
            }
        });
    });
</script>
{% endblock %}
