{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "إضافة دفعة" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .purchase-header {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .purchase-info {
        margin-bottom: 0;
    }
    
    .purchase-info dt {
        font-weight: bold;
    }
    
    .purchase-info dd {
        margin-bottom: 0.5rem;
    }
    
    .required-field::after {
        content: " *";
        color: red;
    }
    
    .form-section {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .form-section-title {
        border-bottom: 1px solid #dee2e6;
        padding-bottom: 10px;
        margin-bottom: 20px;
    }
    
    .invoice-card {
        transition: all 0.3s;
        cursor: pointer;
    }
    
    .invoice-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }
    
    .invoice-card.selected {
        border: 2px solid #4e73df;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0 text-gray-800">{% trans "إضافة دفعة" %}</h1>
    <div>
        <a href="{% url 'purchases:view_purchase' purchase_id=purchase.id %}" class="btn btn-primary">
            <i class="fas fa-arrow-right me-1"></i> {% trans "العودة إلى تفاصيل الطلب" %}
        </a>
    </div>
</div>

{% if messages %}
    {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    {% endfor %}
{% endif %}

<!-- Purchase Header -->
<div class="purchase-header">
    <div class="row">
        <div class="col-md-6">
            <h2>{{ purchase.reference_number }}</h2>
            <p class="text-muted">{% trans "تاريخ الطلب" %}: {{ purchase.date|date:"Y-m-d" }}</p>
        </div>
        <div class="col-md-6 text-md-end">
            <div class="mb-2">
                {% if purchase.status == 'pending' %}
                <span class="badge bg-warning text-dark">{% trans "معلق" %}</span>
                {% elif purchase.status == 'received' %}
                <span class="badge bg-success">{% trans "تم الاستلام" %}</span>
                {% elif purchase.status == 'cancelled' %}
                <span class="badge bg-danger">{% trans "ملغي" %}</span>
                {% endif %}
            </div>
            <div>
                {% if purchase.payment_status == 'unpaid' %}
                <span class="badge bg-danger">{% trans "غير مدفوع" %}</span>
                {% elif purchase.payment_status == 'partial' %}
                <span class="badge bg-warning text-dark">{% trans "مدفوع جزئياً" %}</span>
                {% elif purchase.payment_status == 'paid' %}
                <span class="badge bg-success">{% trans "مدفوع بالكامل" %}</span>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-md-6">
            <h5>{% trans "معلومات المورد" %}</h5>
            <dl class="row purchase-info">
                <dt class="col-sm-4">{% trans "اسم المورد" %}</dt>
                <dd class="col-sm-8">{{ purchase.supplier.name }}</dd>
                
                <dt class="col-sm-4">{% trans "رقم الهاتف" %}</dt>
                <dd class="col-sm-8">{{ purchase.supplier.phone }}</dd>
                
                {% if purchase.supplier.email %}
                <dt class="col-sm-4">{% trans "البريد الإلكتروني" %}</dt>
                <dd class="col-sm-8">{{ purchase.supplier.email }}</dd>
                {% endif %}
            </dl>
        </div>
        <div class="col-md-6">
            <h5>{% trans "معلومات الدفع" %}</h5>
            <dl class="row purchase-info">
                <dt class="col-sm-5">{% trans "المبلغ الإجمالي" %}</dt>
                <dd class="col-sm-7">{{ purchase.total_amount|floatformat:2 }} ر.س</dd>
                
                <dt class="col-sm-5">{% trans "المبلغ المدفوع" %}</dt>
                <dd class="col-sm-7">{{ purchase.payments.all|sum:'amount'|floatformat:2 }} ر.س</dd>
                
                <dt class="col-sm-5">{% trans "المبلغ المتبقي" %}</dt>
                <dd class="col-sm-7">{{ remaining_amount|floatformat:2 }} ر.س</dd>
            </dl>
        </div>
    </div>
</div>

<!-- Payment Form -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">{% trans "معلومات الدفعة" %}</h6>
    </div>
    <div class="card-body">
        <form method="post">
            {% csrf_token %}
            
            <div class="form-section">
                <h5 class="form-section-title">{% trans "المعلومات الأساسية" %}</h5>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="amount" class="form-label required-field">{% trans "المبلغ" %}</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="amount" name="amount" step="0.01" min="0" max="{{ remaining_amount }}" value="{{ remaining_amount }}" required>
                            <span class="input-group-text">ر.س</span>
                        </div>
                        <div class="form-text">{% trans "المبلغ المتبقي" %}: {{ remaining_amount|floatformat:2 }} ر.س</div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="payment_method" class="form-label required-field">{% trans "طريقة الدفع" %}</label>
                        <select class="form-select" id="payment_method" name="payment_method" required>
                            <option value="">{% trans "اختر طريقة الدفع" %}</option>
                            {% for method_code, method_name in payment_methods %}
                            <option value="{{ method_code }}">{{ method_name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="payment_date" class="form-label required-field">{% trans "تاريخ الدفع" %}</label>
                        <input type="date" class="form-control" id="payment_date" name="payment_date" value="{{ today }}" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="reference" class="form-label">{% trans "المرجع" %}</label>
                        <input type="text" class="form-control" id="reference" name="reference" placeholder="{% trans 'رقم الشيك، رقم التحويل، إلخ...' %}">
                    </div>
                </div>
            </div>
            
            {% if invoices %}
            <div class="form-section">
                <h5 class="form-section-title">{% trans "ربط بفاتورة" %}</h5>
                <div class="row">
                    <div class="col-md-12 mb-3">
                        <label for="invoice" class="form-label">{% trans "الفاتورة" %}</label>
                        <select class="form-select" id="invoice" name="invoice">
                            <option value="">{% trans "اختر الفاتورة (اختياري)" %}</option>
                            {% for invoice in invoices %}
                            <option value="{{ invoice.id }}">{{ invoice.invoice_number }} - {{ invoice.amount|floatformat:2 }} ر.س ({{ invoice.invoice_date|date:"Y-m-d" }})</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                
                <div class="row mt-3" id="invoiceCards">
                    {% for invoice in invoices %}
                    <div class="col-md-4 mb-3">
                        <div class="card invoice-card h-100" data-invoice-id="{{ invoice.id }}">
                            <div class="card-header">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">{{ invoice.invoice_number }}</h5>
                                    <span class="badge {% if invoice.status == 'pending' %}bg-warning text-dark{% elif invoice.status == 'verified' %}bg-info{% elif invoice.status == 'paid' %}bg-success{% elif invoice.status == 'cancelled' %}bg-danger{% endif %}">
                                        {% if invoice.status == 'pending' %}{% trans "معلقة" %}
                                        {% elif invoice.status == 'verified' %}{% trans "تم التحقق" %}
                                        {% elif invoice.status == 'paid' %}{% trans "مدفوعة" %}
                                        {% elif invoice.status == 'cancelled' %}{% trans "ملغية" %}
                                        {% endif %}
                                    </span>
                                </div>
                            </div>
                            <div class="card-body">
                                <p class="card-text">
                                    <strong>{% trans "تاريخ الفاتورة" %}:</strong> {{ invoice.invoice_date|date:"Y-m-d" }}<br>
                                    {% if invoice.due_date %}
                                    <strong>{% trans "تاريخ الاستحقاق" %}:</strong> {{ invoice.due_date|date:"Y-m-d" }}<br>
                                    {% endif %}
                                    <strong>{% trans "المبلغ" %}:</strong> {{ invoice.amount|floatformat:2 }} ر.س
                                </p>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
            
            <div class="form-section">
                <h5 class="form-section-title">{% trans "معلومات إضافية" %}</h5>
                <div class="row">
                    <div class="col-md-12 mb-3">
                        <label for="notes" class="form-label">{% trans "ملاحظات" %}</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>
                </div>
            </div>
            
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                {% trans "سيتم تحديث حالة الدفع للطلب تلقائيًا بناءً على المبلغ المدفوع." %}
            </div>
            
            <div class="mt-4">
                <button type="submit" class="btn btn-success">
                    <i class="fas fa-save me-1"></i> {% trans "حفظ الدفعة" %}
                </button>
                <a href="{% url 'purchases:view_purchase' purchase_id=purchase.id %}" class="btn btn-secondary">
                    {% trans "إلغاء" %}
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Invoice card selection
        $('.invoice-card').click(function() {
            var invoiceId = $(this).data('invoice-id');
            $('#invoice').val(invoiceId);
            
            // Update UI
            $('.invoice-card').removeClass('selected');
            $(this).addClass('selected');
        });
        
        // Update invoice card when dropdown changes
        $('#invoice').change(function() {
            var invoiceId = $(this).val();
            $('.invoice-card').removeClass('selected');
            if (invoiceId) {
                $('.invoice-card[data-invoice-id="' + invoiceId + '"]').addClass('selected');
            }
        });
        
        // Validate form before submit
        $('form').submit(function(e) {
            var isValid = true;
            
            // Check if all required fields are filled
            $(this).find('[required]').each(function() {
                if ($(this).val() === '') {
                    isValid = false;
                    $(this).addClass('is-invalid');
                } else {
                    $(this).removeClass('is-invalid');
                }
            });
            
            if (!isValid) {
                e.preventDefault();
                alert('{% trans "يرجى ملء جميع الحقول المطلوبة" %}');
                return false;
            }
            
            // Check if amount is valid
            var amount = parseFloat($('#amount').val());
            var maxAmount = parseFloat('{{ remaining_amount }}');
            if (amount <= 0) {
                e.preventDefault();
                alert('{% trans "يجب أن يكون المبلغ أكبر من صفر" %}');
                return false;
            }
            if (amount > maxAmount) {
                e.preventDefault();
                alert('{% trans "المبلغ أكبر من المبلغ المتبقي" %}');
                return false;
            }
            
            return true;
        });
    });
</script>
{% endblock %}
