{% extends 'base.html' %}
{% load i18n %}
{% load static %}
{% load report_filters %}

{% block title %}{% trans "التقرير المالي" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/daterangepicker.css' %}">
<style>
    .chart-container {
        position: relative;
        height: 300px;
    }

    .filter-section {
        background: #f8f9fc;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        border: 1px solid #e3e6f0;
    }

    .search-section {
        background: white;
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    }

    .financial-summary-card {
        transition: transform 0.2s;
    }

    .financial-summary-card:hover {
        transform: translateY(-2px);
    }

    .profit-positive {
        color: #1cc88a !important;
    }

    .profit-negative {
        color: #e74a3b !important;
    }

    .border-right-purple {
        border-right: 0.25rem solid #6f42c1 !important;
    }

    .border-right-orange {
        border-right: 0.25rem solid #fd7e14 !important;
    }

    .border-right-dark {
        border-right: 0.25rem solid #5a5c69 !important;
    }

    .text-purple {
        color: #6f42c1 !important;
    }

    .text-orange {
        color: #fd7e14 !important;
    }

    .financial-table {
        font-size: 0.9rem;
    }

    .financial-table th {
        background-color: #f8f9fc;
        border-color: #e3e6f0;
        font-weight: 600;
    }

    .period-selector {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 20px;
        padding: 0.5rem 1rem;
        margin: 0.25rem;
        transition: all 0.3s;
    }

    .period-selector:hover {
        background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        color: white;
        transform: translateY(-1px);
    }

    .period-selector.active {
        background: linear-gradient(135deg, #4c51bf 0%, #553c9a 100%);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }

    @media (max-width: 768px) {
        .filter-section {
            padding: 1rem;
        }

        .financial-table {
            font-size: 0.8rem;
        }

        .period-selector {
            padding: 0.4rem 0.8rem;
            font-size: 0.85rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    {% csrf_token %}

    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-chart-pie me-2"></i>{% trans "التقرير المالي" %}
        </h1>
        <div>
            <a href="{% url 'reports:index' %}" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left me-1"></i> {% trans "العودة" %}
            </a>
            <div class="btn-group">
                <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-download me-1"></i> {% trans "تصدير" %}
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="#" onclick="exportReport('pdf')">
                        <i class="fas fa-file-pdf me-2"></i>PDF
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="exportReport('excel')">
                        <i class="fas fa-file-excel me-2"></i>Excel
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="exportReport('csv')">
                        <i class="fas fa-file-csv me-2"></i>CSV
                    </a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Search Section -->
    <div class="search-section">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" class="form-control" id="globalSearch"
                           placeholder="{% trans 'البحث في البيانات المالية (الوصف، الفئة، المبلغ...)' %}">
                    <button class="btn btn-outline-secondary" type="button" id="clearSearch">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="col-md-4 text-end">
                <button class="btn btn-info" type="button" data-bs-toggle="collapse"
                        data-bs-target="#filterSection" aria-expanded="false">
                    <i class="fas fa-filter me-1"></i>{% trans "فلاتر متقدمة" %}
                </button>
            </div>
        </div>
    </div>

    <!-- Advanced Filters Section -->
    <div class="collapse" id="filterSection">
        <div class="filter-section">
            <form id="filterForm" method="GET">
                    <div class="col-md-4 mb-3">
                        <label for="daterange" class="form-label">
                            <i class="fas fa-calendar me-1"></i>{% trans "نطاق التاريخ" %}
                        </label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="daterange" name="daterange" value="{{ start_date|date:'Y-m-d' }} - {{ end_date|date:'Y-m-d' }}">
                            <button class="btn btn-outline-secondary" type="button" id="daterange-btn">
                                <i class="fas fa-calendar"></i>
                            </button>
                        </div>
                        <input type="hidden" id="start_date" name="start_date" value="{{ start_date|date:'Y-m-d' }}">
                        <input type="hidden" id="end_date" name="end_date" value="{{ end_date|date:'Y-m-d' }}">
                    </div>

                    <div class="col-md-4 mb-3">
                        <label for="period_type" class="form-label">
                            <i class="fas fa-clock me-1"></i>{% trans "نوع الفترة" %}
                        </label>
                        <select class="form-select" id="period_type" name="period_type">
                            <option value="daily" {% if selected_period_type == 'daily' %}selected{% endif %}>{% trans "يومي" %}</option>
                            <option value="weekly" {% if selected_period_type == 'weekly' %}selected{% endif %}>{% trans "أسبوعي" %}</option>
                            <option value="monthly" {% if selected_period_type == 'monthly' %}selected{% endif %}>{% trans "شهري" %}</option>
                            <option value="yearly" {% if selected_period_type == 'yearly' %}selected{% endif %}>{% trans "سنوي" %}</option>
                        </select>
                    </div>

                    <div class="col-md-4 mb-3">
                        <label for="report_type" class="form-label">
                            <i class="fas fa-chart-bar me-1"></i>{% trans "نوع التقرير" %}
                        </label>
                        <select class="form-select" id="report_type" name="report_type">
                            <option value="all" {% if selected_report_type == 'all' %}selected{% endif %}>{% trans "جميع التقارير" %}</option>
                            <option value="revenues" {% if selected_report_type == 'revenues' %}selected{% endif %}>
                                💰 {% trans "الإيرادات فقط" %}
                            </option>
                            <option value="expenses" {% if selected_report_type == 'expenses' %}selected{% endif %}>
                                💸 {% trans "المصروفات فقط" %}
                            </option>
                            <option value="profits" {% if selected_report_type == 'profits' %}selected{% endif %}>
                                📈 {% trans "الأرباح فقط" %}
                            </option>
                            <option value="losses" {% if selected_report_type == 'losses' %}selected{% endif %}>
                                📉 {% trans "الخسائر فقط" %}
                            </option>
                        </select>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="category_filter" class="form-label">
                            <i class="fas fa-tags me-1"></i>{% trans "فئة المعاملة" %}
                        </label>
                        <select class="form-select" id="category_filter" name="category_filter">
                            <option value="">{% trans "جميع الفئات" %}</option>
                            {% for category in financial_categories %}
                                <option value="{{ category.id }}" {% if selected_category == category.id|stringformat:"s" %}selected{% endif %}>
                                    {{ category.name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="col-md-4 mb-3">
                        <label for="min_amount" class="form-label">
                            <i class="fas fa-sort-numeric-up me-1"></i>{% trans "الحد الأدنى للمبلغ" %}
                        </label>
                        <input type="number" class="form-control" id="min_amount" name="min_amount"
                               value="{{ min_amount }}" placeholder="0" min="0" step="0.01">
                    </div>

                    <div class="col-md-4 mb-3">
                        <label for="max_amount" class="form-label">
                            <i class="fas fa-sort-numeric-down me-1"></i>{% trans "الحد الأعلى للمبلغ" %}
                        </label>
                        <input type="number" class="form-control" id="max_amount" name="max_amount"
                               value="{{ max_amount }}" placeholder="100000" min="0" step="0.01">
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="payment_method" class="form-label">
                            <i class="fas fa-credit-card me-1"></i>{% trans "طريقة الدفع" %}
                        </label>
                        <select class="form-select" id="payment_method" name="payment_method">
                            <option value="">{% trans "جميع طرق الدفع" %}</option>
                            <option value="cash" {% if selected_payment_method == 'cash' %}selected{% endif %}>
                                💵 {% trans "نقدي" %}
                            </option>
                            <option value="card" {% if selected_payment_method == 'card' %}selected{% endif %}>
                                💳 {% trans "بطاقة ائتمان" %}
                            </option>
                            <option value="transfer" {% if selected_payment_method == 'transfer' %}selected{% endif %}>
                                🏦 {% trans "تحويل بنكي" %}
                            </option>
                            <option value="check" {% if selected_payment_method == 'check' %}selected{% endif %}>
                                📄 {% trans "شيك" %}
                            </option>
                        </select>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="sort_by" class="form-label">
                            <i class="fas fa-sort me-1"></i>{% trans "ترتيب حسب" %}
                        </label>
                        <select class="form-select" id="sort_by" name="sort_by">
                            <option value="date" {% if selected_sort == 'date' %}selected{% endif %}>{% trans "التاريخ" %}</option>
                            <option value="amount" {% if selected_sort == 'amount' %}selected{% endif %}>{% trans "المبلغ" %}</option>
                            <option value="category" {% if selected_sort == 'category' %}selected{% endif %}>{% trans "الفئة" %}</option>
                            <option value="type" {% if selected_sort == 'type' %}selected{% endif %}>{% trans "النوع" %}</option>
                        </select>
                    </div>
                </div>

                <!-- Quick Period Selectors -->
                <div class="row">
                    <div class="col-12 mb-3">
                        <label class="form-label">
                            <i class="fas fa-clock me-1"></i>{% trans "فترات سريعة" %}
                        </label>
                        <div class="d-flex flex-wrap">
                            <button type="button" class="period-selector" data-period="today">
                                {% trans "اليوم" %}
                            </button>
                            <button type="button" class="period-selector" data-period="week">
                                {% trans "هذا الأسبوع" %}
                            </button>
                            <button type="button" class="period-selector" data-period="month">
                                {% trans "هذا الشهر" %}
                            </button>
                            <button type="button" class="period-selector" data-period="quarter">
                                {% trans "هذا الربع" %}
                            </button>
                            <button type="button" class="period-selector" data-period="year">
                                {% trans "هذا العام" %}
                            </button>
                            <button type="button" class="period-selector" data-period="last-month">
                                {% trans "الشهر الماضي" %}
                            </button>
                            <button type="button" class="period-selector" data-period="last-year">
                                {% trans "العام الماضي" %}
                            </button>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <div class="d-flex justify-content-between">
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-filter me-1"></i>{% trans "تطبيق الفلاتر" %}
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="clearFilters()">
                                    <i class="fas fa-undo me-1"></i>{% trans "إعادة تعيين" %}
                                </button>
                            </div>
                            <div>
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    {% trans "عدد النتائج" %}: <span id="resultsCount">{{ total_records }}</span>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Financial Summary Cards -->
    <div class="row">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-primary shadow h-100 py-2 financial-summary-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                {% trans "إجمالي الإيرادات" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_revenues|floatformat:2 }} {% trans "د.م." %}</div>
                            <div class="small mt-2">
                                <span class="text-success">
                                    <i class="fas fa-arrow-up me-1"></i>{{ revenue_growth|floatformat:1 }}% {% trans "نمو" %}
                                </span>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-danger shadow h-100 py-2 financial-summary-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                {% trans "إجمالي المصروفات" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_expenses|floatformat:2 }} {% trans "د.م." %}</div>
                            <div class="small mt-2">
                                <span class="text-info">
                                    <i class="fas fa-percentage me-1"></i>{{ expense_ratio|floatformat:1 }}% {% trans "من الإيرادات" %}
                                </span>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-money-bill-wave fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-{% if profit >= 0 %}success{% else %}warning{% endif %} shadow h-100 py-2 financial-summary-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-{% if profit >= 0 %}success{% else %}warning{% endif %} text-uppercase mb-1">
                                {% if profit >= 0 %}{% trans "صافي الربح" %}{% else %}{% trans "صافي الخسارة" %}{% endif %}</div>
                            <div class="h5 mb-0 font-weight-bold {% if profit >= 0 %}profit-positive{% else %}profit-negative{% endif %}">
                                {{ profit|floatformat:2 }} {% trans "د.م." %}
                            </div>
                            <div class="small mt-2">
                                <span class="text-muted">
                                    <i class="fas fa-calculator me-1"></i>هامش: {{ profit_margin|floatformat:1 }}%
                                </span>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-{% if profit >= 0 %}chart-line{% else %}chart-line-down{% endif %} fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-purple shadow h-100 py-2 financial-summary-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-purple text-uppercase mb-1">
                                {% trans "متوسط المعاملة" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ average_transaction|floatformat:2 }} {% trans "د.م." %}</div>
                            <div class="small mt-2">
                                <span class="text-info">
                                    <i class="fas fa-list me-1"></i>{{ total_transactions }} {% trans "معاملة" %}
                                </span>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-bar fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Statistics Row -->
    <div class="row">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-orange shadow h-100 py-2 financial-summary-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-orange text-uppercase mb-1">
                                {% trans "أكبر إيراد" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ largest_revenue|floatformat:2 }} {% trans "د.م." %}</div>
                            <div class="small mt-2">
                                <span class="text-muted">
                                    <i class="fas fa-calendar me-1"></i>{{ largest_revenue_date|date:"Y-m-d" }}
                                </span>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-trophy fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-dark shadow h-100 py-2 financial-summary-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-dark text-uppercase mb-1">
                                {% trans "أكبر مصروف" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ largest_expense|floatformat:2 }} {% trans "د.م." %}</div>
                            <div class="small mt-2">
                                <span class="text-muted">
                                    <i class="fas fa-calendar me-1"></i>{{ largest_expense_date|date:"Y-m-d" }}
                                </span>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-info shadow h-100 py-2 financial-summary-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                {% trans "الرصيد الحالي" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ current_balance|floatformat:2 }} {% trans "د.م." %}</div>
                            <div class="small mt-2">
                                <span class="{% if balance_change >= 0 %}text-success{% else %}text-danger{% endif %}">
                                    <i class="fas fa-{% if balance_change >= 0 %}arrow-up{% else %}arrow-down{% endif %} me-1"></i>
                                    {{ balance_change|floatformat:2 }} د.م
                                </span>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-wallet fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-secondary shadow h-100 py-2 financial-summary-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">
                                {% trans "معدل النمو" %}</div>
                            <div class="h5 mb-0 font-weight-bold {% if growth_rate >= 0 %}profit-positive{% else %}profit-negative{% endif %}">
                                {{ growth_rate|floatformat:1 }}%
                            </div>
                            <div class="small mt-2">
                                <span class="text-muted">
                                    <i class="fas fa-chart-line me-1"></i>مقارنة بالفترة السابقة
                                </span>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-trending-up fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Financial Data Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-table me-2"></i>{% trans "البيانات المالية التفصيلية" %}
            </h6>
            <div>
                <span class="badge bg-info">
                    <i class="fas fa-list me-1"></i>
                    <span id="tableResultsCount">{{ financial_data.count }}</span> {% trans "معاملة" %}
                </span>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover financial-table" id="financialDataTable" width="100%" cellspacing="0">
                    <thead class="table-dark">
                        <tr>
                            <th>{% trans "التاريخ" %}</th>
                            <th>{% trans "النوع" %}</th>
                            <th>{% trans "الوصف" %}</th>
                            <th>{% trans "الفئة" %}</th>
                            <th>{% trans "الإيرادات" %}</th>
                            <th>{% trans "المصروفات" %}</th>
                            <th>{% trans "الأرباح/الخسائر" %}</th>
                            <th>{% trans "طريقة الدفع" %}</th>
                            <th>{% trans "الإجراءات" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in financial_data %}
                        <tr class="financial-row" data-type="{{ item.type }}">
                            <td>
                                <div>
                                    {{ item.date|date:"Y-m-d" }}
                                    <br><small class="text-muted">{{ item.date|date:"H:i" }}</small>
                                </div>
                            </td>
                            <td>
                                {% if item.type == 'revenue' %}
                                    <span class="badge bg-success">
                                        <i class="fas fa-plus me-1"></i>إيراد
                                    </span>
                                {% else %}
                                    <span class="badge bg-danger">
                                        <i class="fas fa-minus me-1"></i>مصروف
                                    </span>
                                {% endif %}
                            </td>
                            <td>
                                <div>
                                    <strong>{{ item.description }}</strong>
                                    {% if item.notes %}
                                        <br><small class="text-muted">{{ item.notes|truncatechars:50 }}</small>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-secondary">{{ item.category.name }}</span>
                            </td>
                            <td>
                                {% if item.type == 'revenue' %}
                                    <span class="text-success font-weight-bold">
                                        +{{ item.amount|floatformat:2 }} د.م
                                    </span>
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if item.type == 'expense' %}
                                    <span class="text-danger font-weight-bold">
                                        -{{ item.amount|floatformat:2 }} د.م
                                    </span>
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if item.type == 'revenue' %}
                                    <span class="profit-positive font-weight-bold">
                                        +{{ item.amount|floatformat:2 }} د.م
                                    </span>
                                {% else %}
                                    <span class="profit-negative font-weight-bold">
                                        -{{ item.amount|floatformat:2 }} د.م
                                    </span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-light text-dark">
                                    {% if item.payment_method == 'cash' %}💵 نقدي
                                    {% elif item.payment_method == 'card' %}💳 بطاقة
                                    {% elif item.payment_method == 'transfer' %}🏦 تحويل
                                    {% elif item.payment_method == 'check' %}📄 شيك
                                    {% else %}{{ item.get_payment_method_display }}
                                    {% endif %}
                                </span>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-sm btn-outline-info"
                                            data-bs-toggle="tooltip" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-primary"
                                            data-bs-toggle="tooltip" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-secondary"
                                            data-bs-toggle="tooltip" title="طباعة">
                                        <i class="fas fa-print"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="9" class="text-center py-4">
                                <i class="fas fa-chart-pie fa-3x text-muted mb-3"></i>
                                <p class="text-muted">{% trans "لا توجد بيانات مالية تطابق معايير البحث" %}</p>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Income vs Expenses Chart -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "الإيرادات مقابل المصروفات" %}</h6>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="incomeExpensesChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profit Pie Chart -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "توزيع الإيرادات" %}</h6>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="profitPieChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Expenses by Category -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "المصروفات حسب الفئة" %}</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover" id="expensesByCategoryTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>{% trans "الفئة" %}</th>
                                    <th>{% trans "المبلغ" %}</th>
                                    <th>{% trans "النسبة" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for expense in expenses_by_category %}
                                <tr>
                                    <td>{{ expense.category__name }}</td>
                                    <td>{{ expense.total|floatformat:2 }} {% trans "د.م." %}</td>
                                    <td>
                                        {% if total_expenses > 0 %}
                                            {{ expense.total|div:total_expenses|mul:100|floatformat:1 }}%
                                        {% else %}
                                            0%
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="3" class="text-center">{% trans "لا توجد بيانات" %}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Incomes by Category -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "الإيرادات حسب الفئة" %}</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover" id="incomesByCategoryTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>{% trans "الفئة" %}</th>
                                    <th>{% trans "المبلغ" %}</th>
                                    <th>{% trans "النسبة" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for income in incomes_by_category %}
                                <tr>
                                    <td>{{ income.category__name }}</td>
                                    <td>{{ income.total|floatformat:2 }} {% trans "د.م." %}</td>
                                    <td>
                                        {% if total_incomes > 0 %}
                                            {{ income.total|div:total_incomes|mul:100|floatformat:1 }}%
                                        {% else %}
                                            0%
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="3" class="text-center">{% trans "لا توجد بيانات" %}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Expenses Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">{% trans "قائمة المصروفات" %}</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="expensesTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>{% trans "التاريخ" %}</th>
                            <th>{% trans "الوصف" %}</th>
                            <th>{% trans "الفئة" %}</th>
                            <th>{% trans "المبلغ" %}</th>
                            <th>{% trans "طريقة الدفع" %}</th>
                            <th>{% trans "ملاحظات" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for expense in expenses %}
                        <tr>
                            <td>{{ expense.date|date:"Y-m-d" }}</td>
                            <td>{{ expense.description }}</td>
                            <td>{{ expense.category.name }}</td>
                            <td>{{ expense.amount|floatformat:2 }} {% trans "د.م." %}</td>
                            <td>{{ expense.get_payment_method_display }}</td>
                            <td>{{ expense.notes|default:"-" }}</td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center">{% trans "لا توجد مصروفات" %}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Incomes Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">{% trans "قائمة الإيرادات" %}</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="incomesTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>{% trans "التاريخ" %}</th>
                            <th>{% trans "الوصف" %}</th>
                            <th>{% trans "الفئة" %}</th>
                            <th>{% trans "المبلغ" %}</th>
                            <th>{% trans "طريقة الدفع" %}</th>
                            <th>{% trans "ملاحظات" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for income in incomes %}
                        <tr>
                            <td>{{ income.date|date:"Y-m-d" }}</td>
                            <td>{{ income.description }}</td>
                            <td>{{ income.category.name }}</td>
                            <td>{{ income.amount|floatformat:2 }} {% trans "د.م." %}</td>
                            <td>{{ income.get_payment_method_display }}</td>
                            <td>{{ income.notes|default:"-" }}</td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center">{% trans "لا توجد إيرادات" %}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/moment.min.js' %}"></script>
<script src="{% static 'js/daterangepicker.js' %}"></script>
<script src="{% static 'js/chart.min.js' %}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize DataTables with Advanced Features
        var financialDataTable = $('#financialDataTable').DataTable({
            "language": {
                "url": "{% static 'js/dataTables.arabic.json' %}",
                "lengthMenu": "إظهار _MENU_ من العناصر",
                "info": "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل",
                "search": "البحث:",
                "paginate": {
                    "first": "الأول",
                    "last": "الأخير",
                    "next": "التالي",
                    "previous": "السابق"
                }
            },
            "order": [[0, "desc"]],
            "pageLength": 25,
            "lengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, "الكل"]],
            "dom": 'Bfrtip',
            "buttons": [
                {
                    extend: 'excel',
                    text: '<i class="fas fa-file-excel"></i> Excel',
                    className: 'btn btn-success btn-sm'
                },
                {
                    extend: 'pdf',
                    text: '<i class="fas fa-file-pdf"></i> PDF',
                    className: 'btn btn-danger btn-sm'
                },
                {
                    extend: 'csv',
                    text: '<i class="fas fa-file-csv"></i> CSV',
                    className: 'btn btn-info btn-sm'
                },
                {
                    extend: 'print',
                    text: '<i class="fas fa-print"></i> طباعة',
                    className: 'btn btn-secondary btn-sm'
                }
            ],
            "columnDefs": [
                { "orderable": false, "targets": [8] }, // إجراءات غير قابلة للترتيب
                { "searchable": false, "targets": [8] }
            ],
            "responsive": true,
            "scrollX": true
        });

        $('#expensesTable').DataTable({
            "language": {
                "url": "{% static 'js/dataTables.arabic.json' %}"
            },
            "order": [[0, "desc"]]
        });

        $('#incomesTable').DataTable({
            "language": {
                "url": "{% static 'js/dataTables.arabic.json' %}"
            },
            "order": [[0, "desc"]]
        });

        // Global Search Functionality
        $('#globalSearch').on('keyup', function() {
            financialDataTable.search(this.value).draw();
            updateResultsCount();
        });

        // Clear Search
        $('#clearSearch').on('click', function() {
            $('#globalSearch').val('');
            financialDataTable.search('').draw();
            updateResultsCount();
        });

        // Update results count
        function updateResultsCount() {
            var info = financialDataTable.page.info();
            $('#resultsCount').text(info.recordsDisplay);
            $('#tableResultsCount').text(info.recordsDisplay);
        }

        // Filter form submission
        $('#filterForm').on('submit', function(e) {
            e.preventDefault();
            applyFilters();
        });

        // Apply filters function
        function applyFilters() {
            var formData = $('#filterForm').serialize();

            // Show loading
            showLoading();

            // Make AJAX request
            $.get(window.location.pathname, formData)
                .done(function(data) {
                    // Reload page with new data
                    window.location.search = formData;
                })
                .fail(function() {
                    hideLoading();
                    showAlert('error', 'حدث خطأ أثناء تطبيق الفلاتر');
                });
        }

        // Clear filters
        window.clearFilters = function() {
            $('#filterForm')[0].reset();
            window.location.href = window.location.pathname;
        };

        // Export functions
        window.exportReport = function(format) {
            var currentFilters = $('#filterForm').serialize();
            var exportUrl = '{% url "reports:export_financial_report" %}?format=' + format;
            if (currentFilters) {
                exportUrl += '&' + currentFilters;
            }
            window.open(exportUrl, '_blank');
        };

        // Period selector functionality
        $('.period-selector').on('click', function() {
            var period = $(this).data('period');
            var today = new Date();
            var startDate, endDate;

            $('.period-selector').removeClass('active');
            $(this).addClass('active');

            switch(period) {
                case 'today':
                    startDate = endDate = today.toISOString().split('T')[0];
                    break;
                case 'week':
                    var weekStart = new Date(today.setDate(today.getDate() - today.getDay()));
                    startDate = weekStart.toISOString().split('T')[0];
                    endDate = new Date().toISOString().split('T')[0];
                    break;
                case 'month':
                    startDate = new Date(today.getFullYear(), today.getMonth(), 1).toISOString().split('T')[0];
                    endDate = new Date().toISOString().split('T')[0];
                    break;
                case 'quarter':
                    var quarter = Math.floor(today.getMonth() / 3);
                    startDate = new Date(today.getFullYear(), quarter * 3, 1).toISOString().split('T')[0];
                    endDate = new Date().toISOString().split('T')[0];
                    break;
                case 'year':
                    startDate = new Date(today.getFullYear(), 0, 1).toISOString().split('T')[0];
                    endDate = new Date().toISOString().split('T')[0];
                    break;
                case 'last-month':
                    var lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
                    startDate = lastMonth.toISOString().split('T')[0];
                    endDate = new Date(today.getFullYear(), today.getMonth(), 0).toISOString().split('T')[0];
                    break;
                case 'last-year':
                    startDate = new Date(today.getFullYear() - 1, 0, 1).toISOString().split('T')[0];
                    endDate = new Date(today.getFullYear() - 1, 11, 31).toISOString().split('T')[0];
                    break;
            }

            $('#start_date').val(startDate);
            $('#end_date').val(endDate);
            $('#daterange').val(startDate + ' - ' + endDate);
        });

        // Loading and alert functions
        function showLoading() {
            $('body').append('<div id="loadingOverlay" class="position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center" style="background: rgba(0,0,0,0.5); z-index: 9999;"><div class="spinner-border text-light" role="status"><span class="visually-hidden">جاري التحميل...</span></div></div>');
        }

        function hideLoading() {
            $('#loadingOverlay').remove();
        }

        function showAlert(type, message) {
            var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
            var alertHtml = '<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert">' +
                message + '<button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>';
            $('.container-fluid').prepend(alertHtml);

            setTimeout(function() {
                $('.alert').fadeOut();
            }, 5000);
        }

        // Initialize Date Range Picker
        $('#daterange').daterangepicker({
            opens: 'left',
            locale: {
                format: 'YYYY-MM-DD',
                applyLabel: '{% trans "تطبيق" %}',
                cancelLabel: '{% trans "إلغاء" %}',
                fromLabel: '{% trans "من" %}',
                toLabel: '{% trans "إلى" %}',
                customRangeLabel: '{% trans "مخصص" %}',
                daysOfWeek: ['{% trans "أحد" %}', '{% trans "إثنين" %}', '{% trans "ثلاثاء" %}', '{% trans "أربعاء" %}', '{% trans "خميس" %}', '{% trans "جمعة" %}', '{% trans "سبت" %}'],
                monthNames: ['{% trans "يناير" %}', '{% trans "فبراير" %}', '{% trans "مارس" %}', '{% trans "أبريل" %}', '{% trans "مايو" %}', '{% trans "يونيو" %}', '{% trans "يوليو" %}', '{% trans "أغسطس" %}', '{% trans "سبتمبر" %}', '{% trans "أكتوبر" %}', '{% trans "نوفمبر" %}', '{% trans "ديسمبر" %}'],
                firstDay: 0
            }
        }, function(start, end, label) {
            $('#start_date').val(start.format('YYYY-MM-DD'));
            $('#end_date').val(end.format('YYYY-MM-DD'));
        });

        // Income vs Expenses Chart
        const ctx = document.getElementById('incomeExpensesChart').getContext('2d');
        const incomeExpensesChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['{% trans "المبيعات" %}', '{% trans "الإيرادات الأخرى" %}', '{% trans "المصروفات" %}', '{% trans "صافي الربح" %}'],
                datasets: [{
                    label: '{% trans "المبلغ" %}',
                    data: [
                        {{ total_sales }},
                        {{ total_incomes }},
                        {{ total_expenses }},
                        {{ profit }}
                    ],
                    backgroundColor: [
                        'rgba(78, 115, 223, 0.8)',
                        'rgba(28, 200, 138, 0.8)',
                        'rgba(231, 74, 59, 0.8)',
                        'rgba(54, 185, 204, 0.8)'
                    ],
                    borderColor: [
                        'rgba(78, 115, 223, 1)',
                        'rgba(28, 200, 138, 1)',
                        'rgba(231, 74, 59, 1)',
                        'rgba(54, 185, 204, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value + ' {% trans "د.م." %}';
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                var label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.parsed.y !== null) {
                                    label += context.parsed.y + ' {% trans "د.م." %}';
                                }
                                return label;
                            }
                        }
                    }
                }
            }
        });

        // Profit Pie Chart
        const pieCtx = document.getElementById('profitPieChart').getContext('2d');
        const profitPieChart = new Chart(pieCtx, {
            type: 'pie',
            data: {
                labels: ['{% trans "المبيعات" %}', '{% trans "الإيرادات الأخرى" %}', '{% trans "المصروفات" %}'],
                datasets: [{
                    data: [
                        {{ total_sales }},
                        {{ total_incomes }},
                        {{ total_expenses }}
                    ],
                    backgroundColor: [
                        '#4e73df',
                        '#1cc88a',
                        '#e74a3b'
                    ],
                    hoverBackgroundColor: [
                        '#2e59d9',
                        '#17a673',
                        '#be2617'
                    ],
                    hoverBorderColor: "rgba(234, 236, 244, 1)",
                }]
            },
            options: {
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    },
                    tooltip: {
                        backgroundColor: "rgb(255,255,255)",
                        bodyColor: "#858796",
                        borderColor: '#dddfeb',
                        borderWidth: 1,
                        xPadding: 15,
                        yPadding: 15,
                        displayColors: false,
                        caretPadding: 10,
                        callbacks: {
                            label: function(context) {
                                var label = context.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.parsed !== null) {
                                    label += context.parsed + ' {% trans "د.م." %}';
                                }
                                return label;
                            }
                        }
                    }
                },
                cutout: '70%'
            }
        });
    });
</script>
{% endblock %}
