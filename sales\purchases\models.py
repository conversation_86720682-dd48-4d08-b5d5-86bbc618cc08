from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth.models import User
import uuid

class SupplierCategory(models.Model):
    name = models.CharField(_('الاسم'), max_length=100)
    description = models.TextField(_('الوصف'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('فئة موردين')
        verbose_name_plural = _('فئات الموردين')
        ordering = ['name']

    def __str__(self):
        return self.name

class Supplier(models.Model):
    name = models.CharField(_('الاسم'), max_length=200)
    category = models.ForeignKey(SupplierCategory, on_delete=models.SET_NULL, related_name='suppliers', verbose_name=_('الفئة'), blank=True, null=True)
    contact_person = models.CharField(_('الشخص المسؤول'), max_length=200, blank=True, null=True)
    phone = models.CharField(_('رقم الهاتف'), max_length=20)
    email = models.EmailField(_('البريد الإلكتروني'), blank=True, null=True)
    address = models.TextField(_('العنوان'), blank=True, null=True)
    city = models.CharField(_('المدينة'), max_length=100, blank=True, null=True)
    country = models.CharField(_('الدولة'), max_length=100, blank=True, null=True)
    tax_number = models.CharField(_('الرقم الضريبي'), max_length=50, blank=True, null=True)
    website = models.URLField(_('الموقع الإلكتروني'), blank=True, null=True)
    notes = models.TextField(_('ملاحظات'), blank=True, null=True)
    is_active = models.BooleanField(_('نشط'), default=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('مورد')
        verbose_name_plural = _('الموردين')
        ordering = ['name']

    def __str__(self):
        return self.name

    @property
    def total_purchases(self):
        return self.purchases.aggregate(models.Sum('total_amount'))['total_amount__sum'] or 0

class Purchase(models.Model):
    STATUS_CHOICES = (
        ('pending', _('معلق')),
        ('received', _('تم الاستلام')),
        ('cancelled', _('ملغي')),
    )

    PAYMENT_STATUS_CHOICES = (
        ('unpaid', _('غير مدفوع')),
        ('partial', _('مدفوع جزئياً')),
        ('paid', _('مدفوع بالكامل')),
    )

    reference_number = models.CharField(_('رقم المرجع'), max_length=50, unique=True, default=uuid.uuid4)
    supplier = models.ForeignKey(Supplier, on_delete=models.CASCADE, related_name='purchases', verbose_name=_('المورد'))
    employee = models.ForeignKey(User, on_delete=models.CASCADE, related_name='purchases', verbose_name=_('الموظف'))
    date = models.DateTimeField(_('تاريخ الشراء'))
    expected_delivery_date = models.DateField(_('تاريخ التسليم المتوقع'), blank=True, null=True)
    actual_delivery_date = models.DateField(_('تاريخ التسليم الفعلي'), blank=True, null=True)
    subtotal = models.DecimalField(_('المجموع الفرعي'), max_digits=10, decimal_places=2)
    tax_rate = models.DecimalField(_('نسبة الضريبة'), max_digits=5, decimal_places=2, default=15.0)  # 15% VAT in Saudi Arabia
    tax_amount = models.DecimalField(_('مبلغ الضريبة'), max_digits=10, decimal_places=2)
    shipping_cost = models.DecimalField(_('تكلفة الشحن'), max_digits=10, decimal_places=2, default=0)
    total_amount = models.DecimalField(_('المجموع الكلي'), max_digits=10, decimal_places=2)
    status = models.CharField(_('حالة الطلب'), max_length=20, choices=STATUS_CHOICES, default='pending')
    payment_status = models.CharField(_('حالة الدفع'), max_length=20, choices=PAYMENT_STATUS_CHOICES, default='unpaid')
    notes = models.TextField(_('ملاحظات'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('شراء')
        verbose_name_plural = _('المشتريات')
        ordering = ['-date']

    def __str__(self):
        return f"{self.reference_number} - {self.supplier.name}"

    def save(self, *args, **kwargs):
        # Calculate tax amount and total
        self.tax_amount = self.subtotal * (self.tax_rate / 100)
        self.total_amount = self.subtotal + self.tax_amount + self.shipping_cost
        super().save(*args, **kwargs)

class PurchaseItem(models.Model):
    purchase = models.ForeignKey(Purchase, on_delete=models.CASCADE, related_name='items', verbose_name=_('الشراء'))
    product = models.ForeignKey('inventory.Product', on_delete=models.CASCADE, related_name='purchase_items', verbose_name=_('المنتج'))
    quantity = models.PositiveIntegerField(_('الكمية'))
    unit_price = models.DecimalField(_('سعر الوحدة'), max_digits=10, decimal_places=2)
    subtotal = models.DecimalField(_('المجموع الفرعي'), max_digits=10, decimal_places=2)
    received_quantity = models.PositiveIntegerField(_('الكمية المستلمة'), default=0)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)

    class Meta:
        verbose_name = _('عنصر الشراء')
        verbose_name_plural = _('عناصر الشراء')

    def __str__(self):
        return f"{self.product.name} x {self.quantity}"

    def save(self, *args, **kwargs):
        # Calculate subtotal
        self.subtotal = self.quantity * self.unit_price

        # Check if this is a new item or an update
        is_new = self._state.adding

        super().save(*args, **kwargs)

        # If the purchase status is 'received' and this is a new item or the received_quantity has changed
        if self.purchase.status == 'received' and (is_new or self._original_received_quantity != self.received_quantity):
            # Update product quantity
            quantity_difference = self.received_quantity - (0 if is_new else self._original_received_quantity)
            if quantity_difference > 0:
                self.product.quantity += quantity_difference
                self.product.save()

                # Create product movement record
                from inventory.models import ProductMovement
                ProductMovement.objects.create(
                    product=self.product,
                    movement_type='in',
                    quantity=quantity_difference,
                    reference=f"Purchase #{self.purchase.reference_number}"
                )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Store original received_quantity to track changes
        self._original_received_quantity = self.received_quantity

class PurchaseInvoice(models.Model):
    INVOICE_STATUS_CHOICES = (
        ('pending', _('معلقة')),
        ('verified', _('تم التحقق')),
        ('paid', _('مدفوعة')),
        ('cancelled', _('ملغية')),
    )

    purchase = models.ForeignKey(Purchase, on_delete=models.CASCADE, related_name='invoices', verbose_name=_('الشراء'))
    invoice_number = models.CharField(_('رقم الفاتورة'), max_length=100, unique=True)
    invoice_date = models.DateField(_('تاريخ الفاتورة'))
    due_date = models.DateField(_('تاريخ الاستحقاق'), blank=True, null=True)
    amount = models.DecimalField(_('المبلغ الإجمالي'), max_digits=10, decimal_places=2)
    status = models.CharField(_('حالة الفاتورة'), max_length=20, choices=INVOICE_STATUS_CHOICES, default='pending')
    invoice_file = models.FileField(_('ملف الفاتورة'), upload_to='invoices/purchases/', blank=True, null=True)
    notes = models.TextField(_('ملاحظات'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('فاتورة شراء')
        verbose_name_plural = _('فواتير الشراء')
        ordering = ['-invoice_date']

    def __str__(self):
        return f"{self.invoice_number} - {self.purchase.supplier.name}"

class SupplierPayment(models.Model):
    PAYMENT_METHOD_CHOICES = (
        ('cash', _('نقدي')),
        ('bank_transfer', _('تحويل بنكي')),
        ('check', _('شيك')),
        ('credit_card', _('بطاقة ائتمان')),
    )

    purchase = models.ForeignKey(Purchase, on_delete=models.CASCADE, related_name='payments', verbose_name=_('الشراء'))
    invoice = models.ForeignKey(PurchaseInvoice, on_delete=models.SET_NULL, related_name='payments', verbose_name=_('الفاتورة'), blank=True, null=True)
    amount = models.DecimalField(_('المبلغ'), max_digits=10, decimal_places=2)
    payment_method = models.CharField(_('طريقة الدفع'), max_length=20, choices=PAYMENT_METHOD_CHOICES)
    payment_date = models.DateTimeField(_('تاريخ الدفع'))
    reference = models.CharField(_('المرجع'), max_length=100, blank=True, null=True)
    notes = models.TextField(_('ملاحظات'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)

    class Meta:
        verbose_name = _('دفعة للمورد')
        verbose_name_plural = _('دفعات للموردين')
        ordering = ['-payment_date']

    def __str__(self):
        return f"{self.purchase.reference_number} - {self.amount}"

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)

        # Update purchase payment status
        total_paid = self.purchase.payments.aggregate(models.Sum('amount'))['amount__sum'] or 0

        if total_paid >= self.purchase.total_amount:
            self.purchase.payment_status = 'paid'
        elif total_paid > 0:
            self.purchase.payment_status = 'partial'
        else:
            self.purchase.payment_status = 'unpaid'

        self.purchase.save(update_fields=['payment_status'])

        # Update invoice status if associated with an invoice
        if self.invoice:
            invoice_payments = self.invoice.payments.aggregate(models.Sum('amount'))['amount__sum'] or 0
            if invoice_payments >= self.invoice.amount:
                self.invoice.status = 'paid'
                self.invoice.save(update_fields=['status'])
