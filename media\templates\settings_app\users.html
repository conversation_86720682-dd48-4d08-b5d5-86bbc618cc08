{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/settings.css' %}">
{% endblock %}

{% block title %}{% trans "إدارة المستخدمين" %} | {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{% trans "إدارة المستخدمين" %}</h1>
        <a href="{% url 'settings_app:add_user' %}" class="btn btn-primary">
            <i class="fas fa-user-plus me-1"></i> {% trans "إضافة مستخدم جديد" %}
        </a>
    </div>

    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    <div class="card shadow mb-5">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">{% trans "قائمة المستخدمين" %}</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="usersTable" width="100%" cellspacing="0">
                    <thead class="thead-light">
                        <tr>
                            <th>{% trans "اسم المستخدم" %}</th>
                            <th>{% trans "الاسم الكامل" %}</th>
                            <th>{% trans "البريد الإلكتروني" %}</th>
                            <th>{% trans "المجموعات" %}</th>
                            <th>{% trans "آخر دخول" %}</th>
                            <th>{% trans "الحالة" %}</th>
                            <th>{% trans "الإجراءات" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users %}
                        <tr>
                            <td>{{ user.username }}</td>
                            <td>{{ user.get_full_name }}</td>
                            <td>{{ user.email }}</td>
                            <td>
                                {% for group in user.groups.all %}
                                    <span class="badge bg-primary">{{ group.name }}</span>
                                {% empty %}
                                    <span class="text-muted">-</span>
                                {% endfor %}
                            </td>
                            <td>
                                {% if user.last_login %}
                                    {{ user.last_login|date:"Y-m-d H:i" }}
                                {% else %}
                                    <span class="text-muted">{% trans "لم يسجل الدخول بعد" %}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if user.is_active %}
                                    <span class="badge bg-success">{% trans "نشط" %}</span>
                                {% else %}
                                    <span class="badge bg-danger">{% trans "غير نشط" %}</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{% url 'settings_app:edit_user' user.id %}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal{{ user.id }}">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>

                                <!-- Delete Modal -->
                                <div class="modal fade" id="deleteModal{{ user.id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ user.id }}" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="deleteModalLabel{{ user.id }}">{% trans "تأكيد الحذف" %}</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                <p>{% trans "هل أنت متأكد من رغبتك في حذف المستخدم" %} <strong>{{ user.username }}</strong>؟</p>
                                                <p class="text-danger">{% trans "هذا الإجراء لا يمكن التراجع عنه." %}</p>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                                                <form method="post" action="{% url 'settings_app:delete_user' user.id %}">
                                                    {% csrf_token %}
                                                    <button type="submit" class="btn btn-danger">{% trans "حذف" %}</button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="7" class="text-center">{% trans "لا يوجد مستخدمين" %}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- مسافة إضافية لضمان عدم تداخل الشريط السفلي مع المحتوى -->
    <div style="height: 40px;"></div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        $('#usersTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/ar.json"
            }
        });
    });
</script>
{% endblock %}
