{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans 'إنشاء باركود' %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{% trans 'إنشاء باركود' %}</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <form id="generateBarcodeForm" method="post">
                                {% csrf_token %}
                                <div class="form-group">
                                    <label for="barcode_type">{% trans 'نوع الباركود' %}</label>
                                    <select class="form-control" id="barcode_type" name="barcode_type" required>
                                        <option value="">{% trans 'اختر نوع الباركود' %}</option>
                                        {% for type in barcode_types %}
                                            <option value="{{ type.id }}" data-code="{{ type.code }}">{{ type.name }} ({{ type.code }})</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="barcode_number">{% trans 'رقم الباركود' %}</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="barcode_number" name="barcode_number" readonly>
                                        <div class="input-group-append">
                                            <button type="button" class="btn btn-primary" id="generateButton">{% trans 'توليد' %}</button>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group text-center mt-4">
                                    <button type="button" class="btn btn-success" id="showBarcodeButton" disabled>{% trans 'عرض الباركود' %}</button>
                                    <button type="button" class="btn btn-info ml-2" id="printBarcodeButton" disabled>{% trans 'طباعة الباركود' %}</button>
                                </div>
                            </form>
                        </div>
                        <div class="col-md-6">
                            <div class="text-center">
                                <h4>{% trans 'معاينة الباركود' %}</h4>
                                <div id="barcodePreview" class="mt-3 p-3 border">
                                    <p class="text-muted">{% trans 'سيظهر الباركود هنا بعد التوليد' %}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // زر توليد الباركود
        $('#generateButton').click(function() {
            var barcodeType = $('#barcode_type').val();
            if (!barcodeType) {
                alert("{% trans 'يرجى اختيار نوع الباركود أولاً' %}");
                return;
            }

            // طلب AJAX لتوليد رقم باركود
            $.ajax({
                url: "{% url 'inventory:barcode:generate_barcode' %}",
                type: "POST",
                data: {
                    'barcode_type': barcodeType,
                    'csrfmiddlewaretoken': '{{ csrf_token }}'
                },
                success: function(response) {
                    $('#barcode_number').val(response.barcode_number);
                    $('#showBarcodeButton').prop('disabled', false);
                    $('#printBarcodeButton').prop('disabled', false);
                },
                error: function(xhr) {
                    var errorMsg = "{% trans 'حدث خطأ أثناء توليد الباركود' %}";
                    if (xhr.responseJSON && xhr.responseJSON.error) {
                        errorMsg = xhr.responseJSON.error;
                    }
                    alert(errorMsg);
                }
            });
        });

        // زر عرض الباركود
        $('#showBarcodeButton').click(function() {
            var barcodeTypeId = $('#barcode_type').val();
            var barcodeNumber = $('#barcode_number').val();

            if (!barcodeNumber) {
                alert("{% trans 'يرجى توليد رقم باركود أولاً' %}");
                return;
            }

            if (!barcodeTypeId) {
                alert("{% trans 'يرجى اختيار نوع الباركود' %}");
                return;
            }

            // الحصول على كود نوع الباركود من البيانات
            var barcodeTypeCode = $('#barcode_type option:selected').data('code');
            if (!barcodeTypeCode) {
                // إذا لم يكن هناك data-code، استخدم القيمة المختارة مباشرة
                var optionText = $('#barcode_type option:selected').text();
                if (optionText.includes('(') && optionText.includes(')')) {
                    barcodeTypeCode = optionText.split('(')[1].split(')')[0];
                } else {
                    barcodeTypeCode = 'code128'; // افتراضي
                }
            }

            console.log('Barcode Type Code:', barcodeTypeCode);
            console.log('Barcode Number:', barcodeNumber);

            // عرض الباركود
            var barcodeUrl = "{% url 'inventory:barcode:simple_barcode' %}?type=" + encodeURIComponent(barcodeTypeCode.toLowerCase()) + "&data=" + encodeURIComponent(barcodeNumber);

            console.log('Barcode URL:', barcodeUrl);

            // إظهار مؤشر التحميل
            $('#barcodePreview').html('<div class="text-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div><p class="mt-2">{% trans "جاري تحميل الباركود..." %}</p></div>');

            // إنشاء صورة الباركود
            var img = new Image();
            img.onload = function() {
                $('#barcodePreview').html('<div class="text-center"><img src="' + barcodeUrl + '" class="img-fluid" alt="Barcode" style="max-width: 300px; border: 1px solid #ddd; padding: 10px; background: white;"><p class="mt-2 text-muted">{% trans "رقم الباركود: " %}' + barcodeNumber + '</p><p class="small text-info">نوع الباركود: ' + barcodeTypeCode.toUpperCase() + '</p></div>');
            };
            img.onerror = function() {
                console.error('فشل تحميل صورة الباركود:', barcodeUrl);
                console.error('Barcode Type Code:', barcodeTypeCode);
                console.error('Barcode Number:', barcodeNumber);

                // محاولة أخرى باستخدام AJAX
                $.ajax({
                    url: barcodeUrl,
                    type: 'GET',
                    success: function(data, textStatus, xhr) {
                        if (xhr.getResponseHeader('content-type').indexOf('image') !== -1) {
                            $('#barcodePreview').html('<div class="text-center"><img src="' + barcodeUrl + '" class="img-fluid" alt="Barcode" style="max-width: 300px; border: 1px solid #ddd; padding: 10px; background: white;"><p class="mt-2 text-muted">{% trans "رقم الباركود: " %}' + barcodeNumber + '</p></div>');
                        } else {
                            $('#barcodePreview').html('<div class="text-center text-danger"><i class="fas fa-exclamation-triangle fa-3x mb-3"></i><p>{% trans "فشل في تحميل صورة الباركود" %}</p><p class="small text-muted">Response: ' + data + '</p></div>');
                        }
                    },
                    error: function(xhr, status, error) {
                        $('#barcodePreview').html('<div class="text-center text-danger"><i class="fas fa-exclamation-triangle fa-3x mb-3"></i><p>{% trans "فشل في تحميل صورة الباركود" %}</p><p class="small text-muted">Error: ' + error + '</p><p class="small text-muted">Status: ' + status + '</p><p class="small text-muted">URL: ' + barcodeUrl + '</p></div>');
                    }
                });
            };
            img.src = barcodeUrl;
        });

        // زر طباعة الباركود
        $('#printBarcodeButton').click(function() {
            var barcodeTypeId = $('#barcode_type').val();
            var barcodeNumber = $('#barcode_number').val();

            if (!barcodeNumber) {
                alert("{% trans 'يرجى توليد رقم باركود أولاً' %}");
                return;
            }

            if (!barcodeTypeId) {
                alert("{% trans 'يرجى اختيار نوع الباركود' %}");
                return;
            }

            // الحصول على كود نوع الباركود
            var barcodeTypeCode = $('#barcode_type option:selected').data('code');
            if (!barcodeTypeCode) {
                var optionText = $('#barcode_type option:selected').text();
                if (optionText.includes('(') && optionText.includes(')')) {
                    barcodeTypeCode = optionText.split('(')[1].split(')')[0];
                } else {
                    barcodeTypeCode = 'code128';
                }
            }

            // فتح نافذة جديدة للطباعة
            var printUrl = "{% url 'inventory:barcode:simple_barcode' %}?type=" + encodeURIComponent(barcodeTypeCode.toLowerCase()) + "&data=" + encodeURIComponent(barcodeNumber);
            var printWindow = window.open(printUrl, '_blank', 'width=800,height=600');

            // طباعة تلقائية عند تحميل الصفحة
            if (printWindow) {
                printWindow.onload = function() {
                    setTimeout(function() {
                        printWindow.print();
                    }, 1000);
                };
            }
        });
    });
</script>
{% endblock %}