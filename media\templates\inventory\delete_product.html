{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "حذف المنتج" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .product-image {
        max-width: 200px;
        max-height: 200px;
        object-fit: contain;
        border-radius: 5px;
        margin-bottom: 15px;
    }
    
    .warning-icon {
        font-size: 4rem;
        color: #dc3545;
        margin-bottom: 20px;
    }
    
    .product-info {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .product-info-item {
        margin-bottom: 10px;
    }
    
    .product-info-label {
        font-weight: bold;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0 text-gray-800">{% trans "حذف المنتج" %}</h1>
    <a href="{% url 'inventory:edit_product' product.id %}" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-1"></i> {% trans "العودة إلى تفاصيل المنتج" %}
    </a>
</div>

{% if messages %}
    {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    {% endfor %}
{% endif %}

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-danger">{% trans "تأكيد الحذف" %}</h6>
    </div>
    <div class="card-body">
        <div class="text-center mb-4">
            <i class="fas fa-exclamation-triangle warning-icon"></i>
            <h4 class="text-danger">{% trans "هل أنت متأكد من رغبتك في حذف هذا المنتج؟" %}</h4>
            <p class="text-muted">{% trans "هذا الإجراء لا يمكن التراجع عنه. سيتم حذف المنتج وجميع البيانات المرتبطة به نهائيًا." %}</p>
        </div>
        
        <div class="row">
            <div class="col-md-4 text-center">
                {% if product.image %}
                <img src="{{ product.image.url }}" alt="{{ product.name }}" class="product-image">
                {% else %}
                <div class="text-center mb-3">
                    <i class="fas fa-image fa-5x text-muted"></i>
                    <p class="text-muted">{% trans "لا توجد صورة" %}</p>
                </div>
                {% endif %}
            </div>
            <div class="col-md-8">
                <div class="product-info">
                    <div class="product-info-item">
                        <span class="product-info-label">{% trans "كود المنتج:" %}</span>
                        <span>{{ product.code }}</span>
                    </div>
                    <div class="product-info-item">
                        <span class="product-info-label">{% trans "اسم المنتج:" %}</span>
                        <span>{{ product.name }}</span>
                    </div>
                    <div class="product-info-item">
                        <span class="product-info-label">{% trans "الفئة:" %}</span>
                        <span>{{ product.category.name }}</span>
                    </div>
                    <div class="product-info-item">
                        <span class="product-info-label">{% trans "نوع السيارة:" %}</span>
                        <span>{{ product.car_type.name }} ({{ product.car_type.get_origin_display }})</span>
                    </div>
                    <div class="product-info-item">
                        <span class="product-info-label">{% trans "الكمية الحالية:" %}</span>
                        <span>{{ product.quantity }}</span>
                    </div>
                    <div class="product-info-item">
                        <span class="product-info-label">{% trans "سعر البيع:" %}</span>
                        <span>{{ product.selling_price }} د.م</span>
                    </div>
                </div>
                
                {% if related_sales or related_purchases %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-circle me-1"></i>
                    <strong>{% trans "تحذير:" %}</strong>
                    <p class="mb-0">{% trans "هذا المنتج مرتبط بسجلات أخرى في النظام:" %}</p>
                    <ul class="mb-0 mt-2">
                        {% if related_sales %}
                        <li>{% trans "عدد المبيعات المرتبطة:" %} {{ related_sales }}</li>
                        {% endif %}
                        {% if related_purchases %}
                        <li>{% trans "عدد المشتريات المرتبطة:" %} {{ related_purchases }}</li>
                        {% endif %}
                    </ul>
                    <p class="mt-2 mb-0">{% trans "حذف هذا المنتج قد يؤثر على تقارير المبيعات والمشتريات السابقة." %}</p>
                </div>
                {% endif %}
            </div>
        </div>
        
        <form method="post" class="mt-4">
            {% csrf_token %}
            <div class="form-check mb-3">
                <input class="form-check-input" type="checkbox" id="confirmDelete" required>
                <label class="form-check-label" for="confirmDelete">
                    {% trans "أنا أفهم أن هذا الإجراء لا يمكن التراجع عنه وأوافق على حذف هذا المنتج." %}
                </label>
            </div>
            
            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="{% url 'inventory:edit_product' product.id %}" class="btn btn-secondary me-md-2">
                    {% trans "إلغاء" %}
                </a>
                <button type="submit" class="btn btn-danger" id="deleteBtn" disabled>
                    <i class="fas fa-trash me-1"></i> {% trans "حذف المنتج" %}
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Enable/disable delete button based on checkbox
        $('#confirmDelete').change(function() {
            $('#deleteBtn').prop('disabled', !$(this).is(':checked'));
        });
    });
</script>
{% endblock %}
