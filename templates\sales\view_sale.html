{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "عرض تفاصيل البيع" %} #{{ sale.id }} | {{ block.super }}{% endblock %}

{% block extra_css %}
<!-- DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<!-- Google Fonts -->
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
<!-- Animate.css -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

<style>
    :root {
        --primary-color: hsl(197, 99.20%, 49.60%);
        --primary-dark: hsl(197, 99.20%, 49.60%);
        --secondary-color: #f8fafc;
        --accent-color: #06b6d4;
        --success-color: #10b981;
        --warning-color: #f59e0b;
        --danger-color: #ef4444;
        --text-primary: #1e293b;
        --text-secondary: #64748b;
        --border-color: #e2e8f0;
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
        --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    }

    * {
        font-family: 'Cairo', sans-serif;
    }

    body {
        background: linear-gradient(135deg, hsl(197, 87.60%, 68.40%) 0%, hsl(197, 87.60%, 68.40%) 100%);
        min-height: 100vh;
    }

    .main-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        box-shadow: var(--shadow-xl);
        margin: 20px;
        padding: 30px;
        animation: fadeInUp 0.6s ease-out;
    }

    .page-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
        color: white;
        padding: 25px 30px;
        border-radius: 15px;
        margin-bottom: 30px;
        box-shadow: var(--shadow-lg);
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        pointer-events: none;
    }

    .page-title {
        font-size: 2rem;
        font-weight: 700;
        margin: 0;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .info-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: var(--shadow-md);
        border: 1px solid var(--border-color);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .info-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 4px;
        background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    }

    .info-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .info-card-title {
        color: var(--text-primary);
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 2px solid var(--border-color);
        position: relative;
    }

    .info-card-title::before {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 50px;
        height: 2px;
        background: var(--primary-color);
    }

    .status-badge {
        padding: 8px 16px;
        border-radius: 25px;
        font-weight: 600;
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .status-completed {
        background: linear-gradient(135deg, var(--success-color), #059669);
        color: white;
    }

    .status-pending {
        background: linear-gradient(135deg, var(--warning-color), #d97706);
        color: white;
    }

    .status-cancelled {
        background: linear-gradient(135deg, var(--danger-color), #dc2626);
        color: white;
    }

    .payment-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.75rem;
    }

    .payment-paid {
        background: rgba(16, 185, 129, 0.1);
        color: var(--success-color);
        border: 2px solid var(--success-color);
    }

    .payment-unpaid {
        background: rgba(239, 68, 68, 0.1);
        color: var(--danger-color);
        border: 2px solid var(--danger-color);
    }

    .payment-partial {
        background: rgba(245, 158, 11, 0.1);
        color: var(--warning-color);
        border: 2px solid var(--warning-color);
    }

    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 20px;
    }

    .info-item {
        display: flex;
        align-items: center;
        padding: 15px;
        background: var(--secondary-color);
        border-radius: 10px;
        transition: all 0.3s ease;
    }

    .info-item:hover {
        background: rgba(79, 70, 229, 0.05);
        transform: translateX(5px);
    }

    .info-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        color: white;
        margin-left: 15px;
        font-size: 1.1rem;
    }

    .info-content h6 {
        margin: 0;
        font-size: 0.875rem;
        color: var(--text-secondary);
        font-weight: 500;
    }

    .info-content p {
        margin: 0;
        font-size: 1rem;
        color: var(--text-primary);
        font-weight: 600;
    }

    .table {
        border-radius: 15px;
        overflow: hidden;
        box-shadow: var(--shadow-md);
        border: none;
        background: white;
    }

    .table thead th {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        color: white;
        border: none;
        padding: 15px 12px;
        font-weight: 600;
        text-align: center;
        position: relative;
    }

    .table thead th::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="white" stroke-width="0.5" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        pointer-events: none;
    }

    .table tbody td {
        padding: 15px 12px;
        border: none;
        border-bottom: 1px solid var(--border-color);
        vertical-align: middle;
        text-align: center;
    }

    .table tbody tr {
        transition: all 0.3s ease;
    }

    .table tbody tr:hover {
        background: linear-gradient(135deg, #f8fafc, #f1f5f9);
        transform: scale(1.01);
        box-shadow: var(--shadow-sm);
    }

    .product-image-small {
        width: 50px;
        height: 50px;
        object-fit: contain;
        border-radius: 8px;
        border: 2px solid var(--border-color);
        padding: 4px;
        background: white;
        transition: all 0.3s ease;
        box-shadow: var(--shadow-sm);
    }

    .product-image-small:hover {
        transform: scale(1.1) rotate(2deg);
        box-shadow: var(--shadow-md);
        z-index: 100;
    }

    .btn {
        border-radius: 10px;
        padding: 12px 20px;
        font-weight: 600;
        transition: all 0.3s ease;
        border: none;
        position: relative;
        overflow: hidden;
    }

    .btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .btn:hover::before {
        left: 100%;
    }

    .btn-primary {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        color: white;
        box-shadow: var(--shadow-md);
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .btn-success {
        background: linear-gradient(135deg, var(--success-color), #059669);
        color: white;
        box-shadow: var(--shadow-md);
    }

    .btn-success:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .btn-warning {
        background: linear-gradient(135deg, var(--warning-color), #d97706);
        color: white;
        box-shadow: var(--shadow-md);
    }

    .btn-warning:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .btn-danger {
        background: linear-gradient(135deg, var(--danger-color), #dc2626);
        color: white;
        box-shadow: var(--shadow-md);
    }

    .btn-danger:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .chart-container {
        position: relative;
        height: 300px;
        margin: 20px 0;
    }

    .filter-section {
        background: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: var(--shadow-md);
        border: 1px solid var(--border-color);
    }

    .filter-section .form-control, .filter-section .form-select {
        border: 2px solid var(--border-color);
        border-radius: 10px;
        padding: 10px 15px;
        transition: all 0.3s ease;
    }

    .filter-section .form-control:focus, .filter-section .form-select:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
    }

    .summary-card {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        color: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: var(--shadow-lg);
        position: relative;
        overflow: hidden;
    }

    .summary-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="lines" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 0,10 l 10,-10 M -2.5,2.5 l 5,-5 M 7.5,12.5 l 5,-5" stroke="white" stroke-width="0.5" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23lines)"/></svg>');
        pointer-events: none;
    }

    .summary-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid rgba(255,255,255,0.2);
    }

    .summary-item:last-child {
        border-bottom: none;
        font-weight: 700;
        font-size: 1.2rem;
        margin-top: 15px;
        padding-top: 15px;
        border-top: 2px solid rgba(255,255,255,0.3);
    }

    .no-print {
        display: block;
    }

    .print-only {
        display: none;
    }

    @media print {
        .no-print {
            display: none !important;
        }

        .print-only {
            display: block !important;
        }

        body {
            background: white !important;
        }

        .main-container {
            background: white !important;
            box-shadow: none !important;
            margin: 0 !important;
            padding: 20px !important;
        }
    }

    /* تأثيرات الحركة */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(30px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    .animate-slide-in {
        animation: slideInRight 0.6s ease-out;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="main-container">
        <!-- Page Header -->
        <div class="page-header no-print">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="page-title mb-2">
                        <i class="fas fa-file-invoice me-3"></i>{% trans "تفاصيل الفاتورة" %} #{{ sale.id }}
                    </h1>
                    <p class="mb-0">{% trans "عرض شامل لتفاصيل الفاتورة والمنتجات المباعة" %}</p>
                </div>
                <div class="d-flex gap-2">
                    <!-- Export Buttons -->
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-success" onclick="exportToExcel()">
                            <i class="fas fa-file-excel me-2"></i>Excel
                        </button>
                        <button type="button" class="btn btn-danger" onclick="exportToPDF()">
                            <i class="fas fa-file-pdf me-2"></i>PDF
                        </button>
                        <button type="button" class="btn btn-primary" onclick="window.print()">
                            <i class="fas fa-print me-2"></i>{% trans "طباعة" %}
                        </button>
                    </div>

                    <!-- Action Buttons -->
                    <div class="btn-group">
                        <button type="button" class="btn btn-secondary dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-cog me-2"></i>{% trans "خيارات" %}
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            {% if sale.status != 'cancelled' %}
                            <li>
                                <a class="dropdown-item" href="{% url 'sales:edit_sale' sale.id %}">
                                    <i class="fas fa-edit me-2"></i>{% trans "تعديل الفاتورة" %}
                                </a>
                            </li>
                            {% endif %}
                            <li>
                                <a class="dropdown-item" href="#" onclick="emailInvoice()">
                                    <i class="fas fa-envelope me-2"></i>{% trans "إرسال بالبريد الإلكتروني" %}
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="#" onclick="viewCustomerHistory()">
                                    <i class="fas fa-history me-2"></i>{% trans "سجل العميل" %}
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item text-danger" href="{% url 'sales:delete_sale' sale.id %}" onclick="return confirm('{% trans "هل أنت متأكد من حذف هذه الفاتورة؟" %}')">
                                    <i class="fas fa-trash me-2"></i>{% trans "حذف الفاتورة" %}
                                </a>
                            </li>
                        </ul>
                    </div>

                    <a href="{% url 'sales:index' %}" class="btn btn-light">
                        <i class="fas fa-arrow-right me-2"></i>{% trans "العودة للمبيعات" %}
                    </a>
                </div>
            </div>
        </div>

        <!-- Print Header -->
        <div class="print-only mb-4">
            <div class="text-center">
                <h2>{% trans "فاتورة مبيعات" %}</h2>
                <p>{% trans "نظام إدارة متجر قطع غيار السيارات" %}</p>
                <hr>
            </div>
        </div>

        <!-- Messages -->
        {% if messages %}
            <div class="no-print">
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}

        <!-- 1. معلومات الفاتورة الأساسية -->
        <div class="info-card animate-slide-in">
            <h5 class="info-card-title">
                <i class="fas fa-file-invoice me-2"></i>{% trans "معلومات الفاتورة" %}
            </h5>
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-icon">
                        <i class="fas fa-hashtag"></i>
                    </div>
                    <div class="info-content">
                        <h6>{% trans "رقم الفاتورة" %}</h6>
                        <p>#{{ sale.id }}</p>
                    </div>
                </div>
                <div class="info-item">
                    <div class="info-icon">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <div class="info-content">
                        <h6>{% trans "تاريخ البيع" %}</h6>
                        <p>{{ sale.date|date:"d/m/Y H:i" }}</p>
                    </div>
                </div>
                <div class="info-item">
                    <div class="info-icon">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="info-content">
                        <h6>{% trans "اسم العميل" %}</h6>
                        <p>{{ sale.customer.name|default:"عميل غير محدد" }}</p>
                    </div>
                </div>
                <div class="info-item">
                    <div class="info-icon">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <div class="info-content">
                        <h6>{% trans "إجمالي المبلغ" %}</h6>
                        <p>{{ sale.total_amount }} د.م</p>
                    </div>
                </div>
                <div class="info-item">
                    <div class="info-icon">
                        <i class="fas fa-info-circle"></i>
                    </div>
                    <div class="info-content">
                        <h6>{% trans "حالة الفاتورة" %}</h6>
                        <p>
                            {% if sale.status == 'completed' %}
                                <span class="status-badge status-completed">{% trans "مكتملة" %}</span>
                            {% elif sale.status == 'pending' %}
                                <span class="status-badge status-pending">{% trans "معلقة" %}</span>
                            {% else %}
                                <span class="status-badge status-cancelled">{% trans "ملغية" %}</span>
                            {% endif %}
                        </p>
                    </div>
                </div>
                <div class="info-item">
                    <div class="info-icon">
                        <i class="fas fa-credit-card"></i>
                    </div>
                    <div class="info-content">
                        <h6>{% trans "حالة الدفع" %}</h6>
                        <p>
                            {% if sale.is_paid %}
                                <span class="payment-badge payment-paid">{% trans "مدفوع" %}</span>
                            {% elif sale.paid_amount > 0 %}
                                <span class="payment-badge payment-partial">{% trans "مدفوع جزئياً" %}</span>
                            {% else %}
                                <span class="payment-badge payment-unpaid">{% trans "غير مدفوع" %}</span>
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 2. معلومات العميل -->
        {% if sale.customer %}
        <div class="info-card animate-slide-in">
            <h5 class="info-card-title">
                <i class="fas fa-user-circle me-2"></i>{% trans "تفاصيل العميل" %}
            </h5>
            <div class="row">
                <div class="col-md-8">
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="info-content">
                                <h6>{% trans "الاسم الكامل" %}</h6>
                                <p>{{ sale.customer.name }}</p>
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-phone"></i>
                            </div>
                            <div class="info-content">
                                <h6>{% trans "رقم الهاتف" %}</h6>
                                <p>{{ sale.customer.phone|default:"غير محدد" }}</p>
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="info-content">
                                <h6>{% trans "البريد الإلكتروني" %}</h6>
                                <p>{{ sale.customer.email|default:"غير محدد" }}</p>
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div class="info-content">
                                <h6>{% trans "العنوان" %}</h6>
                                <p>{{ sale.customer.address|default:"غير محدد" }}</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-center">
                        <button type="button" class="btn btn-primary mb-3" onclick="viewCustomerHistory()">
                            <i class="fas fa-history me-2"></i>{% trans "سجل المشتريات السابقة" %}
                        </button>
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                            <div class="info-content">
                                <h6>{% trans "إجمالي المشتريات" %}</h6>
                                <p>{{ sale.customer.total_purchases|default:"0" }} د.م</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- 3. فلترة وتصفية البيانات -->
        <div class="filter-section no-print">
            <h6 class="mb-3">
                <i class="fas fa-filter me-2"></i>{% trans "تصفية المنتجات" %}
            </h6>
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label">{% trans "فلترة حسب الفئة" %}</label>
                    <select class="form-select" id="categoryFilter">
                        <option value="">{% trans "جميع الفئات" %}</option>
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">{% trans "فلترة حسب الكمية" %}</label>
                    <select class="form-select" id="quantityFilter">
                        <option value="">{% trans "جميع الكميات" %}</option>
                        <option value="1-5">1-5</option>
                        <option value="6-10">6-10</option>
                        <option value="11+">11+</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">{% trans "نطاق السعر" %}</label>
                    <select class="form-select" id="priceFilter">
                        <option value="">{% trans "جميع الأسعار" %}</option>
                        <option value="0-100">0-100 د.م</option>
                        <option value="101-500">101-500 د.م</option>
                        <option value="501+">501+ د.م</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">{% trans "بحث في المنتجات" %}</label>
                    <input type="text" class="form-control" id="productSearch" placeholder="{% trans 'ابحث عن منتج...' %}">
                </div>
            </div>
        </div>

        <!-- 4. عرض القطع المباعة -->
        <div class="info-card">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="info-card-title mb-0">
                    <i class="fas fa-shopping-cart me-2"></i>{% trans "المنتجات المباعة" %}
                    <span class="badge bg-primary ms-2">{{ sale.items.count }} {% trans "منتج" %}</span>
                </h5>
                <div class="no-print">
                    <button type="button" class="btn btn-success btn-sm me-2" onclick="exportProductsToExcel()">
                        <i class="fas fa-file-excel me-1"></i>Excel
                    </button>
                    <button type="button" class="btn btn-danger btn-sm" onclick="exportProductsToPDF()">
                        <i class="fas fa-file-pdf me-1"></i>PDF
                    </button>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-hover mb-0" id="productsTable">
                    <thead>
                        <tr>
                            <th style="width: 80px;">{% trans "صورة" %}</th>
                            <th>{% trans "اسم المنتج" %}</th>
                            <th style="width: 120px;">{% trans "رمز المنتج" %}</th>
                            <th style="width: 100px;">{% trans "الفئة" %}</th>
                            <th style="width: 80px;">{% trans "الكمية" %}</th>
                            <th style="width: 120px;">{% trans "سعر الوحدة" %}</th>
                            <th style="width: 120px;">{% trans "الإجمالي" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in sale.items.all %}
                        <tr class="product-row" data-category="{{ item.product.category.name|default:'غير محدد' }}">
                            <td class="text-center">
                                {% if item.product.image %}
                                    <img src="{{ item.product.image.url }}" alt="{{ item.product.name }}" class="product-image-small">
                                {% else %}
                                    <div class="d-flex align-items-center justify-content-center" style="width: 50px; height: 50px; background: #f8f9fa; border-radius: 8px;">
                                        <i class="fas fa-image text-muted"></i>
                                    </div>
                                {% endif %}
                            </td>
                            <td>
                                <div>
                                    <strong>{{ item.product.name }}</strong>
                                    {% if item.product.description %}
                                        <br><small class="text-muted">{{ item.product.description|truncatewords:10 }}</small>
                                    {% endif %}
                                </div>
                            </td>
                            <td class="text-center">
                                <strong>{{ item.product.code }}</strong>
                                {% if item.product.barcode %}
                                    <br><small class="text-muted">{{ item.product.barcode }}</small>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                <span class="badge bg-secondary">{{ item.product.category.name|default:"غير محدد" }}</span>
                            </td>
                            <td class="text-center">
                                <span class="badge bg-primary fs-6">{{ item.quantity }}</span>
                            </td>
                            <td class="text-center">
                                <strong>{{ item.unit_price }} د.م</strong>
                            </td>
                            <td class="text-center">
                                <strong class="text-success">{{ item.subtotal }} د.م</strong>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="7" class="text-center py-4">
                                <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                                <p class="text-muted">{% trans "لا توجد منتجات في هذه الفاتورة" %}</p>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 5. الرسوم البيانية والتحليلات -->
        <div class="row no-print">
            <div class="col-md-6">
                <div class="info-card">
                    <h6 class="info-card-title">
                        <i class="fas fa-chart-pie me-2"></i>{% trans "توزيع المنتجات حسب الفئات" %}
                    </h6>
                    <div class="chart-container">
                        <canvas id="categoryChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="info-card">
                    <h6 class="info-card-title">
                        <i class="fas fa-chart-bar me-2"></i>{% trans "توزيع المبيعات حسب السعر" %}
                    </h6>
                    <div class="chart-container">
                        <canvas id="priceChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- 6. ملخص الفاتورة -->
        <div class="row">
            <div class="col-md-8">
                <!-- معلومات إضافية -->
                <div class="info-card">
                    <h6 class="info-card-title">
                        <i class="fas fa-info-circle me-2"></i>{% trans "معلومات إضافية" %}
                    </h6>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-item">
                                <div class="info-icon">
                                    <i class="fas fa-user-tie"></i>
                                </div>
                                <div class="info-content">
                                    <h6>{% trans "الموظف المسؤول" %}</h6>
                                    <p>{{ sale.employee.get_full_name|default:sale.employee.username }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <div class="info-icon">
                                    <i class="fas fa-credit-card"></i>
                                </div>
                                <div class="info-content">
                                    <h6>{% trans "طريقة الدفع" %}</h6>
                                    <p>
                                        {% if sale.payment_method == 'cash' %}
                                            <i class="fas fa-money-bill-wave me-1"></i>{% trans "نقدي" %}
                                        {% elif sale.payment_method == 'credit' %}
                                            <i class="fas fa-credit-card me-1"></i>{% trans "آجل" %}
                                        {% else %}
                                            {% trans "غير محدد" %}
                                        {% endif %}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% if sale.notes %}
                    <div class="mt-3">
                        <h6>{% trans "ملاحظات" %}</h6>
                        <div class="alert alert-info">
                            <i class="fas fa-sticky-note me-2"></i>{{ sale.notes }}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
            <div class="col-md-4">
                <!-- ملخص مالي -->
                <div class="summary-card">
                    <h6 class="mb-4">
                        <i class="fas fa-calculator me-2"></i>{% trans "الملخص المالي" %}
                    </h6>
                    <div class="summary-item">
                        <span>{% trans "المجموع الفرعي:" %}</span>
                        <span>{{ sale.subtotal }} د.م</span>
                    </div>
                    {% if sale.tax_amount %}
                    <div class="summary-item">
                        <span>{% trans "الضريبة ({{ sale.tax_rate }}%):" %}</span>
                        <span>{{ sale.tax_amount }} د.م</span>
                    </div>
                    {% endif %}
                    {% if sale.discount %}
                    <div class="summary-item">
                        <span>{% trans "الخصم:" %}</span>
                        <span class="text-warning">-{{ sale.discount }} د.م</span>
                    </div>
                    {% endif %}
                    <div class="summary-item">
                        <span>{% trans "الإجمالي النهائي:" %}</span>
                        <span>{{ sale.total_amount }} د.م</span>
                    </div>
                    <div class="summary-item">
                        <span>{% trans "المبلغ المدفوع:" %}</span>
                        <span class="text-success">{{ sale.paid_amount }} د.م</span>
                    </div>
                    <div class="summary-item">
                        <span>{% trans "المبلغ المتبقي:" %}</span>
                        <span class="text-danger">{{ sale.remaining_amount }} د.م</span>
                    </div>
                </div>
            </div>
        </div>

    </div> <!-- End main-container -->
</div> <!-- End container-fluid -->



{% endblock %}

{% block extra_js %}
<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
<!-- SheetJS for Excel export -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<!-- jsPDF for PDF export -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>

<script>
$(document).ready(function() {
    // Initialize DataTable
    var table = $('#productsTable').DataTable({
        language: {
            url: '//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json'
        },
        pageLength: 10,
        responsive: true,
        dom: 'Bfrtip',
        buttons: [
            'copy', 'csv', 'excel', 'pdf', 'print'
        ],
        columnDefs: [
            { targets: [0], orderable: false }, // صورة
            { targets: [4, 5, 6], className: 'text-center' } // كمية، سعر، إجمالي
        ]
    });

    // Filter functionality
    $('#categoryFilter').on('change', function() {
        var category = $(this).val();
        if (category) {
            table.column(3).search(category).draw();
        } else {
            table.column(3).search('').draw();
        }
    });

    $('#quantityFilter').on('change', function() {
        var range = $(this).val();
        if (range) {
            var min, max;
            if (range === '1-5') {
                min = 1; max = 5;
            } else if (range === '6-10') {
                min = 6; max = 10;
            } else if (range === '11+') {
                min = 11; max = 999;
            }

            $.fn.dataTable.ext.search.push(function(settings, data, dataIndex) {
                var quantity = parseInt(data[4]) || 0;
                return quantity >= min && (max === 999 ? true : quantity <= max);
            });
            table.draw();
            $.fn.dataTable.ext.search.pop();
        } else {
            table.draw();
        }
    });

    $('#priceFilter').on('change', function() {
        var range = $(this).val();
        if (range) {
            var min, max;
            if (range === '0-100') {
                min = 0; max = 100;
            } else if (range === '101-500') {
                min = 101; max = 500;
            } else if (range === '501+') {
                min = 501; max = 999999;
            }

            $.fn.dataTable.ext.search.push(function(settings, data, dataIndex) {
                var price = parseFloat(data[5].replace(' د.م', '')) || 0;
                return price >= min && (max === 999999 ? true : price <= max);
            });
            table.draw();
            $.fn.dataTable.ext.search.pop();
        } else {
            table.draw();
        }
    });

    $('#productSearch').on('keyup', function() {
        table.search($(this).val()).draw();
    });

    // Populate category filter
    var categories = [];
    $('#productsTable tbody tr').each(function() {
        var category = $(this).data('category');
        if (category && categories.indexOf(category) === -1) {
            categories.push(category);
        }
    });

    categories.sort().forEach(function(category) {
        $('#categoryFilter').append('<option value="' + category + '">' + category + '</option>');
    });

    // Initialize Charts
    initializeCharts();
});

// Chart initialization
function initializeCharts() {
    // Category Chart
    var categoryData = {};
    $('#productsTable tbody tr').each(function() {
        var category = $(this).data('category') || 'غير محدد';
        var quantity = parseInt($(this).find('td:eq(4)').text()) || 0;
        categoryData[category] = (categoryData[category] || 0) + quantity;
    });

    var categoryCtx = document.getElementById('categoryChart');
    if (categoryCtx) {
        new Chart(categoryCtx, {
            type: 'pie',
            data: {
                labels: Object.keys(categoryData),
                datasets: [{
                    data: Object.values(categoryData),
                    backgroundColor: [
                        '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
                        '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    title: {
                        display: true,
                        text: 'توزيع المنتجات حسب الفئات'
                    }
                }
            }
        });
    }

    // Price Chart
    var priceRanges = {'0-100': 0, '101-500': 0, '501+': 0};
    $('#productsTable tbody tr').each(function() {
        var price = parseFloat($(this).find('td:eq(5)').text().replace(' د.م', '')) || 0;
        if (price <= 100) {
            priceRanges['0-100']++;
        } else if (price <= 500) {
            priceRanges['101-500']++;
        } else {
            priceRanges['501+']++;
        }
    });

    var priceCtx = document.getElementById('priceChart');
    if (priceCtx) {
        new Chart(priceCtx, {
            type: 'bar',
            data: {
                labels: ['0-100 د.م', '101-500 د.م', '501+ د.م'],
                datasets: [{
                    label: 'عدد المنتجات',
                    data: Object.values(priceRanges),
                    backgroundColor: ['#36A2EB', '#FFCE56', '#FF6384']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'توزيع المنتجات حسب نطاق السعر'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });
    }
}

// Export functions
function exportToExcel() {
    var wb = XLSX.utils.book_new();

    // Invoice data
    var invoiceData = [
        ['رقم الفاتورة', '{{ sale.id }}'],
        ['تاريخ البيع', '{{ sale.date|date:"d/m/Y H:i" }}'],
        ['اسم العميل', '{{ sale.customer.name|default:"عميل غير محدد" }}'],
        ['إجمالي المبلغ', '{{ sale.total_amount }} د.م'],
        ['حالة الدفع', '{% if sale.is_paid %}مدفوع{% elif sale.paid_amount > 0 %}مدفوع جزئياً{% else %}غير مدفوع{% endif %}'],
        [''],
        ['اسم المنتج', 'رمز المنتج', 'الفئة', 'الكمية', 'سعر الوحدة', 'الإجمالي']
    ];

    // Products data
    $('#productsTable tbody tr').each(function() {
        if ($(this).find('td').length > 1) {
            var row = [
                $(this).find('td:eq(1)').text().trim(),
                $(this).find('td:eq(2)').text().trim(),
                $(this).find('td:eq(3)').text().trim(),
                $(this).find('td:eq(4)').text().trim(),
                $(this).find('td:eq(5)').text().trim(),
                $(this).find('td:eq(6)').text().trim()
            ];
            invoiceData.push(row);
        }
    });

    var ws = XLSX.utils.aoa_to_sheet(invoiceData);
    XLSX.utils.book_append_sheet(wb, ws, 'فاتورة_{{ sale.id }}');
    XLSX.writeFile(wb, 'فاتورة_{{ sale.id }}.xlsx');
}

function exportToPDF() {
    window.print();
}

function exportProductsToExcel() {
    var wb = XLSX.utils.book_new();
    var data = [['اسم المنتج', 'رمز المنتج', 'الفئة', 'الكمية', 'سعر الوحدة', 'الإجمالي']];

    $('#productsTable tbody tr').each(function() {
        if ($(this).find('td').length > 1) {
            var row = [
                $(this).find('td:eq(1)').text().trim(),
                $(this).find('td:eq(2)').text().trim(),
                $(this).find('td:eq(3)').text().trim(),
                $(this).find('td:eq(4)').text().trim(),
                $(this).find('td:eq(5)').text().trim(),
                $(this).find('td:eq(6)').text().trim()
            ];
            data.push(row);
        }
    });

    var ws = XLSX.utils.aoa_to_sheet(data);
    XLSX.utils.book_append_sheet(wb, ws, 'منتجات_فاتورة_{{ sale.id }}');
    XLSX.writeFile(wb, 'منتجات_فاتورة_{{ sale.id }}.xlsx');
}

function exportProductsToPDF() {
    window.print();
}

function emailInvoice() {
    // يمكن تطوير هذه الوظيفة لاحقاً
    alert('سيتم تطوير ميزة إرسال الفاتورة بالبريد الإلكتروني قريباً');
}

function viewCustomerHistory() {
    {% if sale.customer %}
        window.open('{% url "customers:customer_detail" sale.customer.id %}', '_blank');
    {% else %}
        alert('لا يوجد عميل مرتبط بهذه الفاتورة');
    {% endif %}
}
</script>
{% endblock %}
