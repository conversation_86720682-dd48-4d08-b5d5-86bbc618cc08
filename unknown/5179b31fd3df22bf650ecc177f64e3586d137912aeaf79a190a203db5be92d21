# Generated by Django 5.2 on 2025-07-07 15:30

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0006_product_barcode'),
    ]

    operations = [
        migrations.CreateModel(
            name='ProductImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(upload_to='products/gallery/', verbose_name='الصورة')),
                ('alt_text', models.CharField(blank=True, max_length=200, null=True, verbose_name='النص البديل')),
                ('is_primary', models.BooleanField(default=False, verbose_name='صورة رئيسية')),
                ('order', models.PositiveIntegerField(default=0, verbose_name='الترتيب')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='images', to='inventory.product', verbose_name='المنتج')),
            ],
            options={
                'verbose_name': 'صورة المنتج',
                'verbose_name_plural': 'صور المنتج',
                'ordering': ['order', 'created_at'],
            },
        ),
    ]
