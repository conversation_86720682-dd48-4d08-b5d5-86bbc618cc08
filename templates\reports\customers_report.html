{% extends 'base.html' %}
{% load i18n %}
{% load static %}
{% load report_filters %}

{% block title %}{% trans "تقرير العملاء" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/daterangepicker.css' %}">
<style>
    .chart-container {
        position: relative;
        height: 300px;
    }

    .filter-section {
        background: #f8f9fc;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        border: 1px solid #e3e6f0;
    }

    .search-section {
        background: white;
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    }

    .customer-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 16px;
    }

    .loyalty-points {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: bold;
    }

    .purchase-amount {
        font-weight: bold;
        color: #1cc88a;
    }

    .customer-status {
        font-size: 0.85rem;
    }

    .customer-details-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 5px;
        font-size: 0.8rem;
    }

    .customer-details-btn:hover {
        background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        color: white;
    }

    .top-customer-badge {
        background: linear-gradient(135deg, #f6d365 0%, #fda085 100%);
        color: #8b4513;
        padding: 0.2rem 0.5rem;
        border-radius: 10px;
        font-size: 0.7rem;
        font-weight: bold;
    }

    .border-right-purple {
        border-right: 0.25rem solid #6f42c1 !important;
    }

    .border-right-orange {
        border-right: 0.25rem solid #fd7e14 !important;
    }

    .border-right-dark {
        border-right: 0.25rem solid #5a5c69 !important;
    }

    .border-right-secondary {
        border-right: 0.25rem solid #858796 !important;
    }

    .text-purple {
        color: #6f42c1 !important;
    }

    .text-orange {
        color: #fd7e14 !important;
    }

    @media (max-width: 768px) {
        .filter-section {
            padding: 1rem;
        }

        .table-responsive {
            font-size: 0.85rem;
        }

        .customer-avatar {
            width: 30px;
            height: 30px;
            font-size: 12px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    {% csrf_token %}

    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-users me-2"></i>{% trans "تقرير العملاء" %}
        </h1>
        <div>
            <a href="{% url 'reports:index' %}" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left me-1"></i> {% trans "العودة" %}
            </a>
            <div class="btn-group">
                <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-download me-1"></i> {% trans "تصدير" %}
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="#" onclick="exportReport('pdf')">
                        <i class="fas fa-file-pdf me-2"></i>PDF
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="exportReport('excel')">
                        <i class="fas fa-file-excel me-2"></i>Excel
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="exportReport('csv')">
                        <i class="fas fa-file-csv me-2"></i>CSV
                    </a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Search Section -->
    <div class="search-section">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" class="form-control" id="globalSearch"
                           placeholder="{% trans 'البحث في العملاء (الاسم، الهاتف، البريد الإلكتروني...)' %}">
                    <button class="btn btn-outline-secondary" type="button" id="clearSearch">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="col-md-4 text-end">
                <button class="btn btn-info" type="button" data-bs-toggle="collapse"
                        data-bs-target="#filterSection" aria-expanded="false">
                    <i class="fas fa-filter me-1"></i>{% trans "فلاتر متقدمة" %}
                </button>
            </div>
        </div>
    </div>

    <!-- Advanced Filters Section -->
    <div class="collapse" id="filterSection">
        <div class="filter-section">
            <form id="filterForm" method="GET">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label for="registration_period" class="form-label">
                            <i class="fas fa-calendar me-1"></i>{% trans "فترة التسجيل" %}
                        </label>
                        <select class="form-select" id="registration_period" name="registration_period">
                            <option value="">{% trans "أي وقت" %}</option>
                            <option value="today" {% if selected_registration_period == 'today' %}selected{% endif %}>{% trans "اليوم" %}</option>
                            <option value="week" {% if selected_registration_period == 'week' %}selected{% endif %}>{% trans "هذا الأسبوع" %}</option>
                            <option value="month" {% if selected_registration_period == 'month' %}selected{% endif %}>{% trans "هذا الشهر" %}</option>
                            <option value="quarter" {% if selected_registration_period == 'quarter' %}selected{% endif %}>{% trans "هذا الربع" %}</option>
                            <option value="year" {% if selected_registration_period == 'year' %}selected{% endif %}>{% trans "هذا العام" %}</option>
                        </select>
                    </div>

                    <div class="col-md-3 mb-3">
                        <label for="purchase_period" class="form-label">
                            <i class="fas fa-shopping-cart me-1"></i>{% trans "فترة المشتريات" %}
                        </label>
                        <select class="form-select" id="purchase_period" name="purchase_period">
                            <option value="">{% trans "أي وقت" %}</option>
                            <option value="today" {% if selected_purchase_period == 'today' %}selected{% endif %}>{% trans "اليوم" %}</option>
                            <option value="week" {% if selected_purchase_period == 'week' %}selected{% endif %}>{% trans "هذا الأسبوع" %}</option>
                            <option value="month" {% if selected_purchase_period == 'month' %}selected{% endif %}>{% trans "هذا الشهر" %}</option>
                            <option value="quarter" {% if selected_purchase_period == 'quarter' %}selected{% endif %}>{% trans "هذا الربع" %}</option>
                            <option value="year" {% if selected_purchase_period == 'year' %}selected{% endif %}>{% trans "هذا العام" %}</option>
                        </select>
                    </div>

                    <div class="col-md-3 mb-3">
                        <label for="min_purchases" class="form-label">
                            <i class="fas fa-dollar-sign me-1"></i>{% trans "الحد الأدنى للمشتريات" %}
                        </label>
                        <input type="number" class="form-control" id="min_purchases" name="min_purchases"
                               value="{{ min_purchases }}" placeholder="0" min="0" step="0.01">
                    </div>

                    <div class="col-md-3 mb-3">
                        <label for="max_purchases" class="form-label">
                            <i class="fas fa-dollar-sign me-1"></i>{% trans "الحد الأعلى للمشتريات" %}
                        </label>
                        <input type="number" class="form-control" id="max_purchases" name="max_purchases"
                               value="{{ max_purchases }}" placeholder="10000" min="0" step="0.01">
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="min_loyalty_points" class="form-label">
                            <i class="fas fa-star me-1"></i>{% trans "الحد الأدنى لنقاط الولاء" %}
                        </label>
                        <input type="number" class="form-control" id="min_loyalty_points" name="min_loyalty_points"
                               value="{{ min_loyalty_points }}" placeholder="0" min="0">
                    </div>

                    <div class="col-md-4 mb-3">
                        <label for="customer_status" class="form-label">
                            <i class="fas fa-user-check me-1"></i>{% trans "حالة العميل" %}
                        </label>
                        <select class="form-select" id="customer_status" name="customer_status">
                            <option value="">{% trans "جميع الحالات" %}</option>
                            <option value="active" {% if selected_status == 'active' %}selected{% endif %}>
                                <span class="text-success">✓ {% trans "نشط" %}</span>
                            </option>
                            <option value="inactive" {% if selected_status == 'inactive' %}selected{% endif %}>
                                <span class="text-secondary">○ {% trans "غير نشط" %}</span>
                            </option>
                        </select>
                    </div>

                    <div class="col-md-4 mb-3">
                        <label for="sort_by" class="form-label">
                            <i class="fas fa-sort me-1"></i>{% trans "ترتيب حسب" %}
                        </label>
                        <select class="form-select" id="sort_by" name="sort_by">
                            <option value="name" {% if selected_sort == 'name' %}selected{% endif %}>{% trans "الاسم" %}</option>
                            <option value="total_purchases" {% if selected_sort == 'total_purchases' %}selected{% endif %}>{% trans "إجمالي المشتريات" %}</option>
                            <option value="registration_date" {% if selected_sort == 'registration_date' %}selected{% endif %}>{% trans "تاريخ التسجيل" %}</option>
                            <option value="last_purchase" {% if selected_sort == 'last_purchase' %}selected{% endif %}>{% trans "آخر شراء" %}</option>
                            <option value="loyalty_points" {% if selected_sort == 'loyalty_points' %}selected{% endif %}>{% trans "نقاط الولاء" %}</option>
                        </select>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <div class="d-flex justify-content-between">
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-filter me-1"></i>{% trans "تطبيق الفلاتر" %}
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="clearFilters()">
                                    <i class="fas fa-undo me-1"></i>{% trans "إعادة تعيين" %}
                                </button>
                            </div>
                            <div>
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    {% trans "عدد النتائج" %}: <span id="resultsCount">{{ customers.count }}</span>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Customers Summary Cards -->
    <div class="row">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                {% trans "إجمالي العملاء" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_customers }}</div>
                            <div class="small mt-2">
                                <span class="text-success">
                                    <i class="fas fa-user-check me-1"></i>{{ active_customers_count }} {% trans "نشط" %}
                                </span>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                {% trans "إجمالي المشتريات" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_purchases_amount|floatformat:2 }} {% trans "د.م." %}</div>
                            <div class="small mt-2">
                                <span class="text-info">
                                    <i class="fas fa-chart-line me-1"></i>{{ total_invoices_count }} {% trans "فاتورة" %}
                                </span>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                {% trans "متوسط قيمة المشتريات" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ average_purchase_value|floatformat:2 }} {% trans "د.م." %}</div>
                            <div class="small mt-2">
                                <span class="text-muted">
                                    <i class="fas fa-calculator me-1"></i>لكل عميل
                                </span>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-bar fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-purple shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-purple text-uppercase mb-1">
                                {% trans "إجمالي نقاط الولاء" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_loyalty_points|default:0 }}</div>
                            <div class="small mt-2">
                                <span class="text-warning">
                                    <i class="fas fa-star me-1"></i>{{ customers_with_points_count }} {% trans "عميل لديه نقاط" %}
                                </span>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-star fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Statistics Row -->
    <div class="row">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                {% trans "أفضل عميل" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ top_customer.total_spent|floatformat:2 }} {% trans "د.م." %}</div>
                            <div class="small mt-2">
                                <span class="text-muted">
                                    <i class="fas fa-crown me-1"></i>{{ top_customer.name|truncatechars:15 }}
                                </span>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-crown fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-orange shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-orange text-uppercase mb-1">
                                {% trans "عملاء جدد هذا الشهر" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ new_customers_this_month }}</div>
                            <div class="small mt-2">
                                <span class="text-success">
                                    <i class="fas fa-user-plus me-1"></i>عميل جديد
                                </span>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-plus fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-secondary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">
                                {% trans "عملاء بدون مشتريات" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ customers_without_purchases }}</div>
                            <div class="small mt-2">
                                <span class="text-danger">
                                    <i class="fas fa-exclamation-triangle me-1"></i>يحتاج متابعة
                                </span>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-times fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-dark shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-dark text-uppercase mb-1">
                                {% trans "متوسط الفواتير لكل عميل" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ average_invoices_per_customer|floatformat:1 }}</div>
                            <div class="small mt-2">
                                <span class="text-info">
                                    <i class="fas fa-file-invoice me-1"></i>فاتورة/عميل
                                </span>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-file-invoice fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Top Customers Chart -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "أفضل العملاء من حيث المشتريات" %}</h6>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="topCustomersChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customers by Category Chart -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "العملاء حسب الفئة" %}</h6>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="customersByCategoryChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Customers Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">{% trans "أفضل العملاء" %}</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="topCustomersTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>{% trans "العميل" %}</th>
                            <th>{% trans "الفئة" %}</th>
                            <th>{% trans "عدد المشتريات" %}</th>
                            <th>{% trans "إجمالي المشتريات" %}</th>
                            <th>{% trans "آخر شراء" %}</th>
                            <th>{% trans "الرصيد الحالي" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for customer in top_customers %}
                        <tr>
                            <td>{{ customer.name }}</td>
                            <td>{{ customer.category.name|default:"-" }}</td>
                            <td>{{ customer.purchases_count }}</td>
                            <td>{{ customer.total_spent|floatformat:2 }} {% trans "د.م." %}</td>
                            <td>{{ customer.last_purchase_date|date:"Y-m-d"|default:"-" }}</td>
                            <td>{{ customer.balance|floatformat:2 }} {% trans "د.م." %}</td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center">{% trans "لا توجد بيانات" %}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- All Customers Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-list me-2"></i>{% trans "قائمة العملاء التفصيلية" %}
            </h6>
            <div>
                <span class="badge bg-info">
                    <i class="fas fa-users me-1"></i>
                    <span id="tableResultsCount">{{ customers.count }}</span> {% trans "عميل" %}
                </span>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="customersTable" width="100%" cellspacing="0">
                    <thead class="table-dark">
                        <tr>
                            <th width="60px">{% trans "صورة" %}</th>
                            <th>{% trans "الاسم الكامل" %}</th>
                            <th>{% trans "رقم الهاتف" %}</th>
                            <th>{% trans "البريد الإلكتروني" %}</th>
                            <th>{% trans "تاريخ التسجيل" %}</th>
                            <th>{% trans "إجمالي المشتريات" %}</th>
                            <th>{% trans "عدد الفواتير" %}</th>
                            <th>{% trans "نقاط الولاء" %}</th>
                            <th>{% trans "آخر شراء" %}</th>
                            <th>{% trans "الحالة" %}</th>
                            <th>{% trans "الإجراءات" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for customer in customers %}
                        <tr class="customer-row" data-customer-id="{{ customer.id }}">
                            <td>
                                <div class="customer-avatar">
                                    {{ customer.name|first|upper }}
                                </div>
                            </td>
                            <td>
                                <div>
                                    <strong>{{ customer.name }}</strong>
                                    {% if customer.company %}
                                        <br><small class="text-muted">{{ customer.company }}</small>
                                    {% endif %}
                                    {% if customer in top_5_customers %}
                                        <br><span class="top-customer-badge">
                                            <i class="fas fa-star me-1"></i>عميل مميز
                                        </span>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                <div>
                                    <strong>{{ customer.phone }}</strong>
                                    {% if customer.phone2 %}
                                        <br><small class="text-muted">{{ customer.phone2 }}</small>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                {% if customer.email %}
                                    <a href="mailto:{{ customer.email }}" class="text-decoration-none">
                                        {{ customer.email }}
                                    </a>
                                {% else %}
                                    <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>
                                <div>
                                    {{ customer.created_at|date:"Y-m-d" }}
                                    <br><small class="text-muted">{{ customer.created_at|timesince }} مضت</small>
                                </div>
                            </td>
                            <td>
                                <div class="purchase-amount">{{ customer.total_spent|floatformat:2 }} د.م</div>
                                {% if customer.total_spent > 0 %}
                                    <small class="text-success">
                                        <i class="fas fa-chart-line me-1"></i>
                                        {% widthratio customer.total_spent total_purchases_amount 100 as percentage %}
                                        {{ percentage|floatformat:1 }}% من الإجمالي
                                    </small>
                                {% else %}
                                    <small class="text-muted">لا توجد مشتريات</small>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-info">{{ customer.invoices_count }}</span>
                                {% if customer.invoices_count > 0 %}
                                    <br><small class="text-muted">
                                        متوسط: {{ customer.average_invoice_value|floatformat:2 }} د.م
                                    </small>
                                {% endif %}
                            </td>
                            <td>
                                {% if customer.loyalty_points > 0 %}
                                    <span class="loyalty-points">
                                        <i class="fas fa-star me-1"></i>{{ customer.loyalty_points }}
                                    </span>
                                {% else %}
                                    <span class="text-muted">0</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if customer.latest_purchase_date %}
                                    <div>
                                        {{ customer.latest_purchase_date|date:"Y-m-d" }}
                                        <br><small class="text-muted">{{ customer.latest_purchase_date|timesince }} مضت</small>
                                    </div>
                                {% elif customer.last_purchase_date %}
                                    <div>
                                        {{ customer.last_purchase_date|date:"Y-m-d" }}
                                        <br><small class="text-muted">{{ customer.last_purchase_date|timesince }} مضت</small>
                                    </div>
                                {% else %}
                                    <span class="text-muted">لا يوجد</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if customer.is_active %}
                                    <span class="badge bg-success customer-status">
                                        <i class="fas fa-check-circle me-1"></i>نشط
                                    </span>
                                {% else %}
                                    <span class="badge bg-secondary customer-status">
                                        <i class="fas fa-pause-circle me-1"></i>غير نشط
                                    </span>
                                {% endif %}
                                {% if customer.total_spent == 0 %}
                                    <br><span class="badge bg-warning mt-1">
                                        <i class="fas fa-exclamation-triangle me-1"></i>بدون مشتريات
                                    </span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-sm customer-details-btn"
                                            data-customer-id="{{ customer.id }}" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <a href="{% url 'customers:edit_customer' customer.id %}"
                                       class="btn btn-sm btn-outline-primary" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'sales:new_sale' %}?customer={{ customer.id }}"
                                       class="btn btn-sm btn-outline-success" title="إنشاء فاتورة">
                                        <i class="fas fa-plus"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="11" class="text-center py-4">
                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                <p class="text-muted">{% trans "لا توجد عملاء تطابق معايير البحث" %}</p>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Customer Details Modal -->
    <div class="modal fade" id="customerDetailsModal" tabindex="-1" aria-labelledby="customerDetailsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="customerDetailsModalLabel">
                        <i class="fas fa-user me-2"></i>تفاصيل العميل
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="customerDetailsContent">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/chart.min.js' %}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize DataTables with Advanced Features
        var customersTable = $('#customersTable').DataTable({
            "language": {
                "url": "{% static 'js/dataTables.arabic.json' %}",
                "lengthMenu": "إظهار _MENU_ من العناصر",
                "info": "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل",
                "search": "البحث:",
                "paginate": {
                    "first": "الأول",
                    "last": "الأخير",
                    "next": "التالي",
                    "previous": "السابق"
                }
            },
            "order": [[1, "asc"]],
            "pageLength": 25,
            "lengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, "الكل"]],
            "dom": 'Bfrtip',
            "buttons": [
                {
                    extend: 'excel',
                    text: '<i class="fas fa-file-excel"></i> Excel',
                    className: 'btn btn-success btn-sm'
                },
                {
                    extend: 'pdf',
                    text: '<i class="fas fa-file-pdf"></i> PDF',
                    className: 'btn btn-danger btn-sm'
                },
                {
                    extend: 'csv',
                    text: '<i class="fas fa-file-csv"></i> CSV',
                    className: 'btn btn-info btn-sm'
                },
                {
                    extend: 'print',
                    text: '<i class="fas fa-print"></i> طباعة',
                    className: 'btn btn-secondary btn-sm'
                }
            ],
            "columnDefs": [
                { "orderable": false, "targets": [0, 10] }, // صورة وإجراءات غير قابلة للترتيب
                { "searchable": false, "targets": [0, 10] }
            ],
            "responsive": true,
            "scrollX": true
        });

        $('#topCustomersTable').DataTable({
            "language": {
                "url": "{% static 'js/dataTables.arabic.json' %}"
            },
            "order": [[3, "desc"]],
            "paging": false,
            "searching": false
        });

        // Global Search Functionality
        $('#globalSearch').on('keyup', function() {
            customersTable.search(this.value).draw();
            updateResultsCount();
        });

        // Clear Search
        $('#clearSearch').on('click', function() {
            $('#globalSearch').val('');
            customersTable.search('').draw();
            updateResultsCount();
        });

        // Update results count
        function updateResultsCount() {
            var info = customersTable.page.info();
            $('#resultsCount').text(info.recordsDisplay);
            $('#tableResultsCount').text(info.recordsDisplay);
        }

        // Filter form submission
        $('#filterForm').on('submit', function(e) {
            e.preventDefault();
            applyFilters();
        });

        // Apply filters function
        function applyFilters() {
            var formData = $('#filterForm').serialize();

            // Show loading
            showLoading();

            // Make AJAX request
            $.get(window.location.pathname, formData)
                .done(function(data) {
                    // Reload page with new data
                    window.location.search = formData;
                })
                .fail(function() {
                    hideLoading();
                    showAlert('error', 'حدث خطأ أثناء تطبيق الفلاتر');
                });
        }

        // Clear filters
        window.clearFilters = function() {
            $('#filterForm')[0].reset();
            window.location.href = window.location.pathname;
        };

        // Export functions
        window.exportReport = function(format) {
            var currentFilters = $('#filterForm').serialize();
            var exportUrl = '{% url "reports:export_customers_report" %}?format=' + format;
            if (currentFilters) {
                exportUrl += '&' + currentFilters;
            }
            window.open(exportUrl, '_blank');
        };

        // Customer details functionality
        $('.customer-details-btn').on('click', function() {
            var customerId = $(this).data('customer-id');
            loadCustomerDetails(customerId);
        });

        function loadCustomerDetails(customerId) {
            $('#customerDetailsModal').modal('show');
            $('#customerDetailsContent').html('<div class="text-center"><div class="spinner-border" role="status"><span class="visually-hidden">جاري التحميل...</span></div></div>');

            $.get('{% url "reports:customer_details" 0 %}'.replace('0', customerId))
                .done(function(data) {
                    $('#customerDetailsContent').html(data);
                })
                .fail(function() {
                    $('#customerDetailsContent').html('<div class="alert alert-danger">حدث خطأ أثناء تحميل بيانات العميل</div>');
                });
        }

        // Loading and alert functions
        function showLoading() {
            $('body').append('<div id="loadingOverlay" class="position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center" style="background: rgba(0,0,0,0.5); z-index: 9999;"><div class="spinner-border text-light" role="status"><span class="visually-hidden">جاري التحميل...</span></div></div>');
        }

        function hideLoading() {
            $('#loadingOverlay').remove();
        }

        function showAlert(type, message) {
            var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
            var alertHtml = '<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert">' +
                message + '<button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>';
            $('.container-fluid').prepend(alertHtml);

            setTimeout(function() {
                $('.alert').fadeOut();
            }, 5000);
        }

        // Top Customers Chart
        const topCustomersCtx = document.getElementById('topCustomersChart').getContext('2d');
        const topCustomersChart = new Chart(topCustomersCtx, {
            type: 'bar',
            data: {
                labels: [
                    {% for customer in top_customers|slice:":5" %}
                        '{{ customer.name }}',
                    {% endfor %}
                ],
                datasets: [{
                    label: '{% trans "إجمالي المشتريات" %}',
                    data: [
                        {% for customer in top_customers|slice:":5" %}
                            {{ customer.total_spent|default:0 }},
                        {% endfor %}
                    ],
                    backgroundColor: 'rgba(78, 115, 223, 0.8)',
                    borderColor: 'rgba(78, 115, 223, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value + ' {% trans "د.م." %}';
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                var label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.parsed.y !== null) {
                                    label += context.parsed.y + ' {% trans "د.م." %}';
                                }
                                return label;
                            }
                        }
                    }
                }
            }
        });

        // Customers by Category Chart - This is a placeholder, you would need to modify this with actual data
        const categoryCtx = document.getElementById('customersByCategoryChart').getContext('2d');
        const customersByCategoryChart = new Chart(categoryCtx, {
            type: 'pie',
            data: {
                labels: [
                    // This would be populated with actual category names from your data
                    '{% trans "عادي" %}',
                    '{% trans "VIP" %}',
                    '{% trans "تاجر" %}',
                    '{% trans "ورشة" %}',
                    '{% trans "شركة" %}'
                ],
                datasets: [{
                    data: [
                        // This would be populated with actual counts from your data
                        45, 20, 15, 10, 5
                    ],
                    backgroundColor: [
                        '#4e73df',
                        '#1cc88a',
                        '#36b9cc',
                        '#f6c23e',
                        '#e74a3b'
                    ],
                    hoverBackgroundColor: [
                        '#2e59d9',
                        '#17a673',
                        '#2c9faf',
                        '#dda20a',
                        '#be2617'
                    ],
                    hoverBorderColor: "rgba(234, 236, 244, 1)",
                }]
            },
            options: {
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    },
                    tooltip: {
                        backgroundColor: "rgb(255,255,255)",
                        bodyColor: "#858796",
                        borderColor: '#dddfeb',
                        borderWidth: 1,
                        xPadding: 15,
                        yPadding: 15,
                        displayColors: false,
                        caretPadding: 10,
                    }
                },
                cutout: '70%'
            }
        });
    });
</script>
{% endblock %}
