<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول | نظام إدارة متجر قطع غيار السيارات</title>
    
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts - Tajawal -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #f8f9fc;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            max-width: 400px;
            width: 100%;
            padding: 2rem;
            background-color: #fff;
            border-radius: 0.5rem;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        }
        
        .login-logo {
            text-align: center;
            margin-bottom: 1.5rem;
        }
        
        .login-logo i {
            font-size: 3.5rem;
            color: #4e73df;
        }
        
        .login-title {
            text-align: center;
            margin-bottom: 1.5rem;
            color: #5a5c69;
        }
        
        .btn-primary {
            background-color: #4e73df;
            border-color: #4e73df;
        }
        
        .btn-primary:hover {
            background-color: #2e59d9;
            border-color: #2653d4;
        }
        
        .form-control:focus {
            border-color: #bac8f3;
            box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25);
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-logo">
            <i class="fas fa-car-alt"></i>
        </div>
        <h2 class="login-title">نظام إدارة متجر قطع غيار السيارات</h2>
        
        {% if form.errors %}
        <div class="alert alert-danger">
            <strong>خطأ:</strong> اسم المستخدم أو كلمة المرور غير صحيحة. الرجاء المحاولة مرة أخرى.
        </div>
        {% endif %}
        
        {% if next %}
            {% if user.is_authenticated %}
            <div class="alert alert-warning">
                حسابك لا يملك صلاحيات كافية لعرض هذه الصفحة. الرجاء تسجيل الدخول بحساب آخر.
            </div>
            {% else %}
            <div class="alert alert-info">
                الرجاء تسجيل الدخول للوصول إلى هذه الصفحة.
            </div>
            {% endif %}
        {% endif %}
        
        <form method="post" action="{% url 'login' %}">
            {% csrf_token %}
            <div class="mb-3">
                <label for="id_username" class="form-label">اسم المستخدم</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                    <input type="text" name="username" id="id_username" class="form-control" placeholder="أدخل اسم المستخدم" required autofocus>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="id_password" class="form-label">كلمة المرور</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                    <input type="password" name="password" id="id_password" class="form-control" placeholder="أدخل كلمة المرور" required>
                </div>
            </div>
            
            <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="remember-me">
                <label class="form-check-label" for="remember-me">تذكرني</label>
            </div>
            
            <div class="d-grid gap-2">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt me-2"></i> تسجيل الدخول
                </button>
            </div>
            
            <input type="hidden" name="next" value="{{ next }}">
        </form>
        
        <div class="text-center mt-3">
            <a href="#" class="text-decoration-none">نسيت كلمة المرور؟</a>
        </div>
    </div>
    
    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
