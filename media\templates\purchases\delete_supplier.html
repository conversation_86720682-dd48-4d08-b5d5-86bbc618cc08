{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "حذف المورد" %} | {{ block.super }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0 text-gray-800">{% trans "حذف المورد" %}</h1>
    <div>
        <a href="{% url 'purchases:suppliers' %}" class="btn btn-primary">
            <i class="fas fa-arrow-right me-1"></i> {% trans "العودة إلى الموردين" %}
        </a>
    </div>
</div>

{% if messages %}
    {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    {% endfor %}
{% endif %}

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-danger">{% trans "تأكيد الحذف" %}</h6>
    </div>
    <div class="card-body">
        <div class="alert alert-warning">
            <h5>{% trans "هل أنت متأكد من حذف المورد" %} "{{ supplier.name }}"؟</h5>
            <p>{% trans "هذا الإجراء لا يمكن التراجع عنه." %}</p>
            
            {% if supplier.purchases.exists %}
            <div class="alert alert-danger mt-3">
                <i class="fas fa-exclamation-triangle me-2"></i>
                {% trans "لا يمكن حذف هذا المورد لأنه مرتبط بطلبات شراء. يمكنك تعطيله بدلاً من حذفه." %}
            </div>
            {% endif %}
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <h6>{% trans "معلومات المورد" %}:</h6>
                <ul class="list-group mb-3">
                    <li class="list-group-item"><strong>{% trans "الاسم" %}:</strong> {{ supplier.name }}</li>
                    <li class="list-group-item"><strong>{% trans "رقم الهاتف" %}:</strong> {{ supplier.phone }}</li>
                    {% if supplier.email %}
                    <li class="list-group-item"><strong>{% trans "البريد الإلكتروني" %}:</strong> {{ supplier.email }}</li>
                    {% endif %}
                    {% if supplier.address %}
                    <li class="list-group-item"><strong>{% trans "العنوان" %}:</strong> {{ supplier.address }}</li>
                    {% endif %}
                </ul>
            </div>
        </div>
        
        <form method="post">
            {% csrf_token %}
            <div class="mt-4">
                <button type="submit" class="btn btn-danger" {% if supplier.purchases.exists %}disabled{% endif %}>
                    <i class="fas fa-trash me-1"></i> {% trans "حذف المورد" %}
                </button>
                <a href="{% url 'purchases:suppliers' %}" class="btn btn-secondary">
                    {% trans "إلغاء" %}
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}
