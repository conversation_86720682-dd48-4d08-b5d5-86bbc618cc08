{% load static %}
<!DOCTYPE html>
<html>
<head>
    <title>Test Barcode</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>Test Barcode AJAX</h1>
    <p>User: {{ user.username }}</p>
    {% csrf_token %}
    <input type="text" id="barcodeInput" placeholder="Enter barcode" value="1282235856942741">
    <button onclick="testBarcode()">Test Barcode</button>
    <div id="result"></div>

    <script>
        // Get CSRF token from cookie
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        // Setup CSRF for all AJAX requests
        const csrftoken = getCookie('csrftoken') || $('[name=csrfmiddlewaretoken]').val();
        $.ajaxSetup({
            beforeSend: function(xhr, settings) {
                if (!this.crossDomain && !/^(GET|HEAD|OPTIONS|TRACE)$/i.test(settings.type) && !this.crossDomain) {
                    xhr.setRequestHeader("X-CSRFToken", csrftoken);
                }
            }
        });

        function testBarcode() {
            var barcode = $('#barcodeInput').val();

            console.log('Testing barcode:', barcode);
            console.log('CSRF Token:', csrftoken);
            console.log('URL:', "{% url 'inventory:barcode:scan_barcode' %}");

            $.ajax({
                url: "{% url 'inventory:barcode:scan_barcode' %}",
                type: 'POST',
                data: {
                    'barcode_number': barcode
                },
                beforeSend: function(xhr, settings) {
                    console.log('Sending request with data:', settings.data);
                },
                success: function(data) {
                    console.log('Success:', data);
                    $('#result').html('<div style="color: green; padding: 10px; border: 1px solid green;">Success: <pre>' + JSON.stringify(data, null, 2) + '</pre></div>');
                },
                error: function(xhr, status, error) {
                    console.log('Error details:');
                    console.log('Status:', xhr.status);
                    console.log('Status Text:', xhr.statusText);
                    console.log('Response Text:', xhr.responseText);
                    console.log('Error:', error);
                    $('#result').html('<div style="color: red; padding: 10px; border: 1px solid red;">Error (' + xhr.status + '): <pre>' + xhr.responseText + '</pre></div>');
                }
            });
        }
    </script>
</body>
</html>
