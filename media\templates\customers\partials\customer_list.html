{% load i18n %}

<table class="table table-bordered table-hover" id="customersTable">
    <thead>
        <tr>
            <th>{% trans "الاسم" %}</th>
            <th>{% trans "رقم الهاتف" %}</th>
            <th>{% trans "البريد الإلكتروني" %}</th>
            <th>{% trans "المدينة" %}</th>
            <th>{% trans "الفئة" %}</th>
            <th>{% trans "الحالة" %}</th>
            <th>{% trans "آخر شراء" %}</th>
            <th>{% trans "الإجراءات" %}</th>
        </tr>
    </thead>
    <tbody>
        {% if customers %}
            {% for customer in customers %}
                <tr class="customer-row">
                    <td>
                        <a href="{% url 'customers:view_customer' customer_id=customer.id %}" class="fw-bold text-primary">
                            {{ customer.name }}
                        </a>
                    </td>
                    <td dir="ltr">{{ customer.phone }}</td>
                    <td>{{ customer.email|default:"-" }}</td>
                    <td>{{ customer.city|default:"-" }}</td>
                    <td>{{ customer.category.name|default:"-" }}</td>
                    <td>
                        {% if customer.is_active %}
                            <span class="badge bg-success">{% trans "نشط" %}</span>
                        {% else %}
                            <span class="badge bg-danger">{% trans "غير نشط" %}</span>
                        {% endif %}
                    </td>
                    <td>
                        {% if customer.last_purchase_date %}
                            {{ customer.last_purchase_date|date:"Y-m-d" }}
                        {% else %}
                            <span class="text-muted">-</span>
                        {% endif %}
                    </td>
                    <td>
                        <div class="action-buttons">
                            <a href="{% url 'customers:view_customer' customer_id=customer.id %}" class="btn btn-sm btn-info" title="{% trans 'عرض' %}">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="{% url 'customers:edit_customer' customer_id=customer.id %}" class="btn btn-sm btn-primary" title="{% trans 'تعديل' %}">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a href="{% url 'customers:delete_customer' customer_id=customer.id %}" class="btn btn-sm btn-danger delete-customer" 
                               data-id="{{ customer.id }}" data-name="{{ customer.name }}" title="{% trans 'حذف' %}">
                                <i class="fas fa-trash"></i>
                            </a>
                        </div>
                    </td>
                </tr>
            {% endfor %}
        {% else %}
            <tr>
                <td colspan="8" class="text-center py-4">
                    <div class="text-muted">
                        <i class="fas fa-users fa-3x mb-3"></i>
                        <p>{% trans "لا يوجد عملاء مطابقين للبحث" %}</p>
                    </div>
                </td>
            </tr>
        {% endif %}
    </tbody>
</table>

<!-- ترقيم الصفحات -->
{% if page_obj.paginator.num_pages > 1 %}
    <nav aria-label="Page navigation">
        <ul class="pagination justify-content-center">
            {% if page_obj.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page=1">
                        <i class="fas fa-angle-double-right"></i>
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.previous_page_number }}">
                        <i class="fas fa-angle-right"></i>
                    </a>
                </li>
            {% else %}
                <li class="page-item disabled">
                    <a class="page-link" href="#"><i class="fas fa-angle-double-right"></i></a>
                </li>
                <li class="page-item disabled">
                    <a class="page-link" href="#"><i class="fas fa-angle-right"></i></a>
                </li>
            {% endif %}
            
            {% for num in page_obj.paginator.page_range %}
                {% if page_obj.number == num %}
                    <li class="page-item active">
                        <a class="page-link" href="#">{{ num }}</a>
                    </li>
                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ num }}">{{ num }}</a>
                    </li>
                {% endif %}
            {% endfor %}
            
            {% if page_obj.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.next_page_number }}">
                        <i class="fas fa-angle-left"></i>
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.paginator.num_pages }}">
                        <i class="fas fa-angle-double-left"></i>
                    </a>
                </li>
            {% else %}
                <li class="page-item disabled">
                    <a class="page-link" href="#"><i class="fas fa-angle-left"></i></a>
                </li>
                <li class="page-item disabled">
                    <a class="page-link" href="#"><i class="fas fa-angle-double-left"></i></a>
                </li>
            {% endif %}
        </ul>
    </nav>
{% endif %}
