import os

# قراءة ملف القالب
with open('templates/sales/new_sale.html', 'r', encoding='utf-8') as f:
    content = f.read()

# استبدال نسبة الضريبة الثابتة بالقيمة من إعدادات الضريبة
content = content.replace(
    '<span>{% trans "ضريبة القيمة المضافة (15%):" %}</span>',
    '<span>{% trans "ضريبة القيمة المضافة" %} ({{ tax.rate }}%):</span>'
)

# كتابة المحتوى المعدل إلى الملف
with open('templates/sales/new_sale.html', 'w', encoding='utf-8') as f:
    f.write(content)

print("تم تعديل ملف القالب بنجاح!")
