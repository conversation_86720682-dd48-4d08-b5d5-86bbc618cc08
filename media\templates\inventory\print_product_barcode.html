{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block styles %}
<style>
    .print-container {
        max-width: 800px;
        margin: 20px auto;
        padding: 20px;
        border: 1px solid #ddd;
        border-radius: 5px;
        background-color: #fff;
    }
    .barcode-area {
        text-align: center;
        margin-bottom: 20px;
    }
    .barcode-img {
        max-width: 100%;
        height: auto;
        border: 1px solid #eee;
    }
    .product-details p {
        margin-bottom: 5px;
    }
    .print-button-container {
        text-align: center;
        margin-top: 30px;
    }

    @media print {
        body * {
            visibility: hidden;
        }
        .print-area, .print-area * {
            visibility: visible;
        }
        .print-area {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
        }
        .no-print {
            display: none;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right no-print">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{% url 'dashboard:index' %}">{% trans "لوحة التحكم" %}</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'inventory:index' %}">{% trans "المخزون" %}</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'inventory:product_barcodes' product.id %}">{% trans "باركودات المنتج" %}</a></li>
                        <li class="breadcrumb-item active">{{ title }}</li>
                    </ol>
                </div>
                <h4 class="page-title">{{ title }}</h4>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card print-container">
                <div class="card-body print-area">
                    <div class="barcode-area">
                        <h4>{% trans "معاينة الباركود" %}</h4>
                        <img src="data:image/png;base64,{{ barcode_image }}" alt="{{ barcode.barcode_number }}" class="barcode-img">
                        <p class="mt-2"><strong>{{ barcode.barcode_number }}</strong></p>
                        {% if barcode.include_name %}
                            <p>{{ product.name }}</p>
                        {% endif %}
                        {% if barcode.include_price %}
                            <p>{% trans "السعر" %}: {{ product.price|default:"N/A" }}</p>
                        {% endif %}
                    </div>

                    <hr>

                    <div class="product-details">
                        <h5>{% trans "تفاصيل المنتج" %}</h5>
                        <p><strong>{% trans "اسم المنتج" %}:</strong> {{ product.name }}</p>
                        <p><strong>{% trans "رمز المنتج" %}:</strong> {{ product.sku }}</p>
                        <p><strong>{% trans "نوع الباركود" %}:</strong> {{ barcode.barcode_type.name }}</p>
                    </div>

                    <div class="print-button-container no-print mt-4">
                        <form method="post" class="d-inline-block me-2">
                            {% csrf_token %}
                            <div class="row g-2 align-items-center">
                                <div class="col-auto">
                                    <label for="copies" class="col-form-label">{% trans "عدد النسخ" %}:</label>
                                </div>
                                <div class="col-auto">
                                    <input type="number" id="copies" name="copies" class="form-control form-control-sm" value="{{ copies }}" min="1" max="100" style="width: 80px;">
                                </div>
                                <div class="col-auto">
                                    <button type="submit" class="btn btn-secondary btn-sm">{% trans "تحديث النسخ" %}</button>
                                </div>
                            </div>
                        </form>
                        <button type="button" class="btn btn-primary" onclick="window.print();">
                            <i class="mdi mdi-printer me-1"></i> {% trans "طباعة" %}
                        </button>
                        <a href="{% url 'inventory:product_barcodes' product.id %}" class="btn btn-light ms-2">
                            <i class="mdi mdi-arrow-left me-1"></i> {% trans "العودة إلى باركودات المنتج" %}
                        </a>
                    </div>
                    <p class="text-muted small text-center mt-3 no-print">{% trans "سيتم طباعة" %} {{ copies }} {% trans "نسخة من هذا الباركود." %}</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}