{% extends 'base.html' %}
{% load i18n %}
{% load static %}
{% load purchase_tags %}

{% block title %}{% trans "تقرير المدفوعات" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.2.2/css/buttons.bootstrap5.min.css">
<style>
    .filter-section {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .payment-method-badge {
        font-size: 0.8rem;
        padding: 0.4rem 0.6rem;
    }

    .summary-card {
        transition: all 0.3s;
    }

    .summary-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    .summary-icon {
        font-size: 2rem;
        opacity: 0.7;
    }

    @media print {
        .no-print {
            display: none !important;
        }

        .card {
            border: none !important;
            box-shadow: none !important;
        }

        .card-header {
            background-color: white !important;
            border-bottom: 1px solid #ddd !important;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4 no-print">
    <h1 class="h3 mb-0 text-gray-800">{% trans "تقرير المدفوعات" %}</h1>
    <div>
        <button type="button" class="btn btn-secondary me-2" onclick="window.print()">
            <i class="fas fa-print me-1"></i> {% trans "طباعة" %}
        </button>
        <a href="{% url 'purchases:export_report' report_type='payments' %}" class="btn btn-success me-2">
            <i class="fas fa-file-csv me-1"></i> {% trans "تصدير CSV" %}
        </a>
        <a href="{% url 'purchases:payments' %}" class="btn btn-primary">
            <i class="fas fa-arrow-right me-1"></i> {% trans "العودة إلى المدفوعات" %}
        </a>
    </div>
</div>

<!-- Filters -->
<div class="card shadow mb-4 no-print">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">{% trans "تصفية النتائج" %}</h6>
    </div>
    <div class="card-body">
        <form action="{% url 'purchases:payments_report' %}" method="get" id="filterForm">
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label for="date_from" class="form-label">{% trans "من تاريخ" %}</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="{{ date_from }}">
                </div>
                <div class="col-md-4 mb-3">
                    <label for="date_to" class="form-label">{% trans "إلى تاريخ" %}</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="{{ date_to }}">
                </div>
                <div class="col-md-4 mb-3">
                    <label for="supplier" class="form-label">{% trans "المورد" %}</label>
                    <select class="form-select" id="supplier" name="supplier">
                        <option value="">{% trans "الكل" %}</option>
                        {% for supplier in suppliers %}
                        <option value="{{ supplier.id }}" {% if supplier_filter == supplier.id|stringformat:"s" %}selected{% endif %}>{{ supplier.name }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label for="payment_method" class="form-label">{% trans "طريقة الدفع" %}</label>
                    <select class="form-select" id="payment_method" name="payment_method">
                        <option value="">{% trans "الكل" %}</option>
                        {% for method_code, method_name in payment_methods %}
                        <option value="{{ method_code }}" {% if payment_method == method_code %}selected{% endif %}>{{ method_name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-4 mb-3">
                    <label for="min_amount" class="form-label">{% trans "الحد الأدنى للمبلغ" %}</label>
                    <input type="number" class="form-control" id="min_amount" name="min_amount" value="{{ min_amount }}" step="0.01">
                </div>
                <div class="col-md-4 mb-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-filter me-1"></i> {% trans "تصفية" %}
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Report Header for Print -->
<div class="d-none d-print-block mb-4">
    <div class="text-center">
        <h1>{% trans "تقرير المدفوعات" %}</h1>
        <p>
            {% if date_from %}{% trans "من تاريخ" %}: {{ date_from }}{% endif %}
            {% if date_to %} - {% trans "إلى تاريخ" %}: {{ date_to }}{% endif %}
            {% if supplier_filter %} - {% trans "المورد" %}: {{ suppliers|filter_by_id:supplier_filter|first|get_attr:"name" }}{% endif %}
        </p>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-xl-4 col-md-6 mb-4">
        <div class="card border-right-primary shadow h-100 py-2 summary-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            {% trans "إجمالي عدد المدفوعات" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-money-bill-wave fa-2x text-primary summary-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-4 col-md-6 mb-4">
        <div class="card border-right-success shadow h-100 py-2 summary-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            {% trans "إجمالي المبالغ المدفوعة" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_amount|floatformat:2 }} ر.س</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-success summary-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-4 col-md-6 mb-4">
        <div class="card border-right-info shadow h-100 py-2 summary-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            {% trans "متوسط قيمة الدفعة" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ average_amount|floatformat:2 }} ر.س</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calculator fa-2x text-info summary-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Payments Table -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">{% trans "قائمة المدفوعات" %}</h6>
    </div>
    <div class="card-body">
        {% if payments %}
        <div class="table-responsive">
            <table class="table table-bordered table-hover" id="paymentsTable" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th>{% trans "رقم الطلب" %}</th>
                        <th>{% trans "المورد" %}</th>
                        <th>{% trans "تاريخ الدفع" %}</th>
                        <th>{% trans "المبلغ" %}</th>
                        <th>{% trans "طريقة الدفع" %}</th>
                        <th>{% trans "المرجع" %}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for payment in payments %}
                    <tr>
                        <td>
                            <a href="{% url 'purchases:view_purchase' purchase_id=payment.purchase.id %}">
                                {{ payment.purchase.reference_number }}
                            </a>
                        </td>
                        <td>{{ payment.purchase.supplier.name }}</td>
                        <td>{{ payment.payment_date|date:"Y-m-d" }}</td>
                        <td>{{ payment.amount|floatformat:2 }} ر.س</td>
                        <td>
                            {% if payment.payment_method == 'cash' %}
                            <span class="badge bg-success payment-method-badge">{% trans "نقدي" %}</span>
                            {% elif payment.payment_method == 'bank_transfer' %}
                            <span class="badge bg-primary payment-method-badge">{% trans "تحويل بنكي" %}</span>
                            {% elif payment.payment_method == 'check' %}
                            <span class="badge bg-info payment-method-badge">{% trans "شيك" %}</span>
                            {% elif payment.payment_method == 'credit_card' %}
                            <span class="badge bg-warning text-dark payment-method-badge">{% trans "بطاقة ائتمان" %}</span>
                            {% else %}
                            <span class="badge bg-secondary payment-method-badge">{{ payment.get_payment_method_display }}</span>
                            {% endif %}
                        </td>
                        <td>{{ payment.reference|default:"-" }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="alert alert-info">
            {% trans "لا توجد مدفوعات متاحة حالياً." %}
        </div>
        {% endif %}
    </div>
</div>

<!-- Payment Methods Chart -->
{% if payments %}
<div class="row">
    <div class="col-xl-6 col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">{% trans "توزيع طرق الدفع" %}</h6>
            </div>
            <div class="card-body">
                <div class="chart-pie pt-4">
                    <canvas id="paymentMethodsChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-6 col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">{% trans "المدفوعات الشهرية" %}</h6>
            </div>
            <div class="card-body">
                <div class="chart-bar">
                    <canvas id="monthlyPaymentsChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.bootstrap5.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.print.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
    $(document).ready(function() {
        // تهيئة جدول البيانات مع أزرار التصدير
        $('#paymentsTable').DataTable({
            "language": {
                "url": "{% static 'vendor/datatables/ar.json' %}"
            },
            "order": [[2, "desc"]], // ترتيب حسب تاريخ الدفع (تنازلي)
            "pageLength": 25,
            dom: 'Bfrtip',
            buttons: [
                {
                    extend: 'excel',
                    text: '<i class="fas fa-file-excel"></i> Excel',
                    className: 'btn btn-success btn-sm',
                    exportOptions: {
                        columns: [0, 1, 2, 3, 4, 5]
                    },
                    title: '{% trans "تقرير المدفوعات" %}'
                },
                {
                    extend: 'pdf',
                    text: '<i class="fas fa-file-pdf"></i> PDF',
                    className: 'btn btn-danger btn-sm',
                    exportOptions: {
                        columns: [0, 1, 2, 3, 4, 5]
                    },
                    title: '{% trans "تقرير المدفوعات" %}',
                    customize: function(doc) {
                        doc.defaultStyle.alignment = 'right';
                        doc.defaultStyle.direction = 'rtl';
                    }
                },
                {
                    extend: 'print',
                    text: '<i class="fas fa-print"></i> {% trans "طباعة" %}',
                    className: 'btn btn-secondary btn-sm',
                    exportOptions: {
                        columns: [0, 1, 2, 3, 4, 5]
                    },
                    title: '{% trans "تقرير المدفوعات" %}'
                }
            ]
        });

        {% if payments %}
        // رسم بياني لطرق الدفع
        var methodsCtx = document.getElementById('paymentMethodsChart').getContext('2d');
        var methodsChart = new Chart(methodsCtx, {
            type: 'pie',
            data: {
                labels: {{ payment_methods_labels|safe }},
                datasets: [{
                    data: {{ payment_methods_data|safe }},
                    backgroundColor: [
                        '#4e73df',
                        '#1cc88a',
                        '#36b9cc',
                        '#f6c23e',
                        '#e74a3b',
                        '#858796'
                    ],
                    hoverBackgroundColor: [
                        '#2e59d9',
                        '#17a673',
                        '#2c9faf',
                        '#dda20a',
                        '#be2617',
                        '#60616f'
                    ],
                    hoverBorderColor: "rgba(234, 236, 244, 1)",
                }],
            },
            options: {
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        rtl: true,
                        labels: {
                            font: {
                                family: 'Tajawal'
                            }
                        }
                    },
                    tooltip: {
                        rtl: true,
                        titleFont: {
                            family: 'Tajawal'
                        },
                        bodyFont: {
                            family: 'Tajawal'
                        },
                        callbacks: {
                            label: function(context) {
                                var label = context.label || '';
                                var value = context.raw;
                                var total = context.dataset.data.reduce(function(acc, val) { return acc + val; }, 0);
                                var percentage = Math.round((value / total) * 100);
                                return label + ': ' + percentage + '%';
                            }
                        }
                    }
                }
            },
        });

        // رسم بياني للمدفوعات الشهرية
        var monthlyCtx = document.getElementById('monthlyPaymentsChart').getContext('2d');
        var monthlyChart = new Chart(monthlyCtx, {
            type: 'bar',
            data: {
                labels: {{ monthly_labels|safe }},
                datasets: [{
                    label: '{% trans "المبلغ" %}',
                    backgroundColor: '#4e73df',
                    hoverBackgroundColor: '#2e59d9',
                    borderColor: '#4e73df',
                    data: {{ monthly_data|safe }},
                }],
            },
            options: {
                maintainAspectRatio: false,
                scales: {
                    x: {
                        ticks: {
                            font: {
                                family: 'Tajawal'
                            }
                        }
                    },
                    y: {
                        ticks: {
                            font: {
                                family: 'Tajawal'
                            },
                            callback: function(value) {
                                return value.toLocaleString() + ' ر.س';
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        titleFont: {
                            family: 'Tajawal'
                        },
                        bodyFont: {
                            family: 'Tajawal'
                        },
                        callbacks: {
                            label: function(context) {
                                var value = context.raw;
                                return value.toLocaleString() + ' ر.س';
                            }
                        }
                    }
                }
            }
        });
        {% endif %}
    });
</script>
{% endblock %}