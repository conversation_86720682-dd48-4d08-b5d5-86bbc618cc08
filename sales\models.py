from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth.models import User
from customers.models import Customer
from inventory.models import Product
import uuid

class Sale(models.Model):
    STATUS_CHOICES = (
        ('pending', _('معلق')),
        ('completed', _('مكتمل')),
        ('cancelled', _('ملغي')),
    )

    PAYMENT_METHOD_CHOICES = (
        ('cash', _('نقدي')),
        ('card', _('بطاقة ائتمان')),
        ('transfer', _('تحويل بنكي')),
        ('check', _('شيك')),
        ('credit', _('آجل')),
    )

    invoice_number = models.CharField(_('رقم الفاتورة'), max_length=50, unique=True, default=uuid.uuid4)
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name='sales', verbose_name=_('العميل'))
    employee = models.ForeignKey(User, on_delete=models.CASCADE, related_name='sales', verbose_name=_('الموظف'))
    date = models.DateTimeField(_('تاريخ البيع'), auto_now_add=True)
    subtotal = models.DecimalField(_('المجموع الفرعي'), max_digits=10, decimal_places=2)
    tax_rate = models.DecimalField(_('نسبة الضريبة'), max_digits=5, decimal_places=2, default=15.0)  # 15% VAT in Saudi Arabia
    tax_amount = models.DecimalField(_('مبلغ الضريبة'), max_digits=10, decimal_places=2)
    discount = models.DecimalField(_('الخصم'), max_digits=10, decimal_places=2, default=0)
    total_amount = models.DecimalField(_('المجموع الكلي'), max_digits=10, decimal_places=2)
    payment_method = models.CharField(_('طريقة الدفع'), max_length=20, choices=PAYMENT_METHOD_CHOICES, default='cash')
    status = models.CharField(_('الحالة'), max_length=20, choices=STATUS_CHOICES, default='completed')
    notes = models.TextField(_('ملاحظات'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('بيع')
        verbose_name_plural = _('المبيعات')
        ordering = ['-date']

    def __str__(self):
        return f"{self.invoice_number} - {self.customer.name}"

    @property
    def is_paid(self):
        """تحديد ما إذا كانت الفاتورة مدفوعة بالكامل"""
        total_payments = self.payments.aggregate(
            total=models.Sum('amount')
        )['total'] or 0
        return total_payments >= self.total_amount

    @property
    def remaining_amount(self):
        """المبلغ المتبقي للدفع"""
        total_payments = self.payments.aggregate(
            total=models.Sum('amount')
        )['total'] or 0
        return max(0, self.total_amount - total_payments)

    @property
    def paid_amount(self):
        """المبلغ المدفوع"""
        return self.payments.aggregate(
            total=models.Sum('amount')
        )['total'] or 0

    def save(self, *args, **kwargs):
        # Calculate tax amount and total
        self.tax_amount = self.subtotal * (self.tax_rate / 100)
        self.total_amount = self.subtotal + self.tax_amount - self.discount
        super().save(*args, **kwargs)

class SaleItem(models.Model):
    sale = models.ForeignKey(Sale, on_delete=models.CASCADE, related_name='items', verbose_name=_('البيع'))
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='sale_items', verbose_name=_('المنتج'))
    quantity = models.PositiveIntegerField(_('الكمية'))
    unit_price = models.DecimalField(_('سعر الوحدة'), max_digits=10, decimal_places=2)
    subtotal = models.DecimalField(_('المجموع الفرعي'), max_digits=10, decimal_places=2)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)

    class Meta:
        verbose_name = _('عنصر البيع')
        verbose_name_plural = _('عناصر البيع')

    def __str__(self):
        return f"{self.product.name} x {self.quantity}"

    def save(self, *args, **kwargs):
        # Calculate subtotal
        self.subtotal = self.quantity * self.unit_price
        super().save(*args, **kwargs)

        # Update product quantity - هنا يتم الربط بين المخزون ونقطة البيع
        # عند إنشاء عنصر بيع جديد، يتم تحديث كمية المنتج في المخزون تلقائياً
        if self._state.adding:  # Only reduce stock on creation, not on update
            # Check if product still exists
            try:
                product = Product.objects.get(id=self.product_id)
                product.quantity -= self.quantity
                product.save()

                # Create product movement record - سجل حركة المنتج للتتبع
                from inventory.models import ProductMovement
                ProductMovement.objects.create(
                    product=product,
                    movement_type='out',
                    quantity=self.quantity,
                    reference=f"Sale #{self.sale.invoice_number}"
                )
            except Product.DoesNotExist:
                # Product has been deleted, skip stock update
                pass

class Payment(models.Model):
    sale = models.ForeignKey(Sale, on_delete=models.CASCADE, related_name='payments', verbose_name=_('البيع'))
    amount = models.DecimalField(_('المبلغ'), max_digits=10, decimal_places=2)
    payment_method = models.CharField(_('طريقة الدفع'), max_length=20, choices=Sale.PAYMENT_METHOD_CHOICES)
    payment_date = models.DateTimeField(_('تاريخ الدفع'), auto_now_add=True)
    reference = models.CharField(_('المرجع'), max_length=100, blank=True, null=True)
    notes = models.TextField(_('ملاحظات'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)

    class Meta:
        verbose_name = _('دفعة')
        verbose_name_plural = _('الدفعات')
        ordering = ['-payment_date']

    def __str__(self):
        return f"{self.sale.invoice_number} - {self.amount}"
