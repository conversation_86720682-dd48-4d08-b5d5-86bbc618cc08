#!/usr/bin/env python
import os
import django
from datetime import datetime, timedelta
from decimal import Decimal
import random

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'auto_parts_pos.settings')
django.setup()

from sales.models import Sale, SaleItem
from customers.models import Customer
from inventory.models import Product, Category
from django.contrib.auth.models import User

def create_sample_data():
    print("إنشاء بيانات تجريبية للمبيعات...")
    
    # إنشاء فئات إذا لم تكن موجودة
    categories_data = [
        'قطع غيار المحرك',
        'قطع غيار الفرامل',
        'قطع غيار التعليق',
        'الإطارات والعجلات',
        'الزيوت والسوائل',
        'قطع غيار الكهرباء',
        'قطع غيار التكييف',
        'إكسسوارات السيارة'
    ]
    
    categories = []
    for cat_name in categories_data:
        category, created = Category.objects.get_or_create(
            name=cat_name,
            defaults={'description': f'فئة {cat_name}'}
        )
        categories.append(category)
        if created:
            print(f"تم إنشاء فئة: {cat_name}")
    
    # إنشاء منتجات إذا لم تكن موجودة
    products_data = [
        ('فلتر زيت', 'قطع غيار المحرك', 25.00, 35.00),
        ('فلتر هواء', 'قطع غيار المحرك', 30.00, 45.00),
        ('شمعات الإشعال', 'قطع غيار المحرك', 15.00, 25.00),
        ('أقراص فرامل', 'قطع غيار الفرامل', 80.00, 120.00),
        ('تيل فرامل', 'قطع غيار الفرامل', 60.00, 90.00),
        ('مساعدات أمامية', 'قطع غيار التعليق', 200.00, 300.00),
        ('إطار 185/65R15', 'الإطارات والعجلات', 150.00, 220.00),
        ('زيت محرك 5W30', 'الزيوت والسوائل', 40.00, 60.00),
        ('بطارية 12V', 'قطع غيار الكهرباء', 180.00, 250.00),
        ('فلتر مكيف', 'قطع غيار التكييف', 35.00, 50.00),
    ]
    
    products = []
    for prod_name, cat_name, purchase_price, selling_price in products_data:
        category = Category.objects.get(name=cat_name)
        product, created = Product.objects.get_or_create(
            name=prod_name,
            defaults={
                'code': f'P{random.randint(1000, 9999)}',
                'category': category,
                'purchase_price': Decimal(str(purchase_price)),
                'selling_price': Decimal(str(selling_price)),
                'quantity': random.randint(10, 100),
                'min_quantity': 5,
                'description': f'وصف {prod_name}'
            }
        )
        products.append(product)
        if created:
            print(f"تم إنشاء منتج: {prod_name}")
    
    # إنشاء عملاء إذا لم يكونوا موجودين
    customers_data = [
        ('أحمد محمد', '0501234567', '<EMAIL>'),
        ('فاطمة علي', '0507654321', '<EMAIL>'),
        ('محمد حسن', '0509876543', '<EMAIL>'),
        ('سارة أحمد', '0502468135', '<EMAIL>'),
        ('علي محمود', '0508642097', '<EMAIL>'),
        ('نور الدين', '0503691472', '<EMAIL>'),
        ('ليلى حسام', '0505827394', '<EMAIL>'),
        ('عمر سالم', '0504173658', '<EMAIL>'),
    ]
    
    customers = []
    for cust_name, phone, email in customers_data:
        customer, created = Customer.objects.get_or_create(
            name=cust_name,
            defaults={
                'phone': phone,
                'email': email,
                'address': f'عنوان {cust_name}'
            }
        )
        customers.append(customer)
        if created:
            print(f"تم إنشاء عميل: {cust_name}")
    
    # إنشاء مبيعات تجريبية
    print("إنشاء مبيعات تجريبية...")
    
    # الحصول على المستخدم الأول أو إنشاء واحد
    try:
        user = User.objects.first()
        if not user:
            user = User.objects.create_user('admin', '<EMAIL>', 'admin123')
            print("تم إنشاء مستخدم admin")
    except:
        user = User.objects.create_user('testuser', '<EMAIL>', 'test123')
        print("تم إنشاء مستخدم testuser")
    
    # إنشاء مبيعات للشهر الحالي والماضي
    today = datetime.now().date()
    
    for i in range(50):  # إنشاء 50 عملية بيع
        # تاريخ عشوائي في آخر 60 يوم
        days_ago = random.randint(0, 60)
        sale_date = today - timedelta(days=days_ago)
        
        # عميل عشوائي
        customer = random.choice(customers)
        
        # طريقة دفع عشوائية
        payment_methods = ['cash', 'card', 'transfer', 'check']
        payment_method = random.choice(payment_methods)
        
        # إنشاء البيع
        sale = Sale.objects.create(
            invoice_number=f'INV-{random.randint(10000, 99999)}',
            customer=customer,
            employee=user,
            payment_method=payment_method,
            status='completed',
            subtotal=Decimal('0.00'),
            tax_rate=Decimal('15.0'),
            tax_amount=Decimal('0.00'),
            total_amount=Decimal('0.00')  # سيتم تحديثه لاحقاً
        )

        # تحديث تاريخ البيع
        sale.date = datetime.combine(sale_date, datetime.now().time())
        sale.save()
        
        # إضافة منتجات للبيع
        total_amount = Decimal('0.00')
        num_items = random.randint(1, 5)  # من 1 إلى 5 منتجات
        
        for j in range(num_items):
            product = random.choice(products)
            quantity = random.randint(1, 3)
            price = product.selling_price
            subtotal = price * quantity
            
            SaleItem.objects.create(
                sale=sale,
                product=product,
                quantity=quantity,
                unit_price=price,
                subtotal=subtotal
            )
            
            total_amount += subtotal
        
        # تحديث إجماليات البيع
        sale.subtotal = total_amount
        sale.tax_amount = total_amount * (Decimal('15.0') / Decimal('100'))
        sale.total_amount = total_amount + sale.tax_amount
        sale.save()
        
        print(f"تم إنشاء بيع رقم {sale.invoice_number} بقيمة {total_amount} د.م")
    
    print(f"تم إنشاء {Sale.objects.count()} عملية بيع بنجاح!")
    print("يمكنك الآن عرض تقرير المبيعات مع البيانات الجديدة.")

if __name__ == '__main__':
    create_sample_data()
