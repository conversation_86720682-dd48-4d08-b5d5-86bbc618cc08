/* تنسيق زر إضافة منتج في صفحة المبيعات الجديدة */

/* تنسيق أساسي للزر */
.btn-add-product {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 10px 20px;
    font-weight: 600;
    box-shadow: 0 4px 6px rgba(50, 50, 93, 0.11), 0 1px 3px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

/* تأثير التحويم */
.btn-add-product:hover {
    transform: translateY(-2px);
    box-shadow: 0 7px 14px rgba(50, 50, 93, 0.1), 0 3px 6px rgba(0, 0, 0, 0.08);
    background: linear-gradient(135deg, #3a5fd9 0%, #1a3ca0 100%);
    color: white;
}

/* تأثير الضغط */
.btn-add-product:active {
    transform: translateY(1px);
    box-shadow: 0 3px 5px rgba(50, 50, 93, 0.1), 0 1px 2px rgba(0, 0, 0, 0.08);
}

/* تأثير الإضاءة عند التحويم */
.btn-add-product::after {
    content: "";
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: rgba(255, 255, 255, 0.1);
    transform: rotate(30deg);
    transition: all 0.5s ease;
    opacity: 0;
}

.btn-add-product:hover::after {
    opacity: 1;
    transform: rotate(30deg) translate(-10%, -10%);
}

/* تنسيق الأيقونة */
.btn-add-product i {
    font-size: 1.1em;
    transition: transform 0.3s ease;
}

.btn-add-product:hover i {
    transform: scale(1.2);
}

/* تنسيق زر إضافة منتج في قسم المنتجات */
.product-section .btn-add-product {
    margin-bottom: 15px;
    min-width: 140px;
}

/* تنسيق خاص للزر في الشاشات الصغيرة */
@media (max-width: 768px) {
    .btn-add-product {
        padding: 8px 15px;
        font-size: 0.9rem;
    }
}