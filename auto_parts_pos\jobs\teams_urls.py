from django.urls import path
from . import teams_views

app_name = 'teams'

urlpatterns = [
    # عرض قائمة الأقسام
    path('departments/', teams_views.department_list, name='department_list'),
    
    # إنشاء قسم جديد
    path('departments/new/', teams_views.new_department, name='new_department'),
    
    # عرض تفاصيل قسم
    path('departments/<int:department_id>/', teams_views.view_department, name='view_department'),
    
    # تعديل قسم
    path('departments/<int:department_id>/edit/', teams_views.edit_department, name='edit_department'),
    
    # حذف قسم
    path('departments/<int:department_id>/delete/', teams_views.delete_department, name='delete_department'),
    
    # عرض قائمة الفرق
    path('', teams_views.team_list, name='team_list'),
    
    # إنشاء فريق جديد
    path('new/', teams_views.new_team, name='new_team'),
    
    # عرض تفاصيل فريق
    path('<int:team_id>/', teams_views.view_team, name='view_team'),
    
    # تعديل فريق
    path('<int:team_id>/edit/', teams_views.edit_team, name='edit_team'),
    
    # حذف فريق
    path('<int:team_id>/delete/', teams_views.delete_team, name='delete_team'),
    
    # إضافة عضو إلى فريق
    path('<int:team_id>/add-member/', teams_views.add_team_member, name='add_team_member'),
    
    # حذف عضو من فريق
    path('member/<int:member_id>/delete/', teams_views.delete_team_member, name='delete_team_member'),
    
    # عرض قائمة المهارات
    path('skills/', teams_views.skill_list, name='skill_list'),
    
    # إنشاء مهارة جديدة
    path('skills/new/', teams_views.new_skill, name='new_skill'),
    
    # تعديل مهارة
    path('skills/<int:skill_id>/edit/', teams_views.edit_skill, name='edit_skill'),
    
    # حذف مهارة
    path('skills/<int:skill_id>/delete/', teams_views.delete_skill, name='delete_skill'),
    
    # إضافة مهارة لموظف
    path('employee/<int:user_id>/add-skill/', teams_views.add_employee_skill, name='add_employee_skill'),
    
    # تعديل مهارة موظف
    path('employee-skill/<int:employee_skill_id>/edit/', teams_views.edit_employee_skill, name='edit_employee_skill'),
    
    # حذف مهارة موظف
    path('employee-skill/<int:employee_skill_id>/delete/', teams_views.delete_employee_skill, name='delete_employee_skill'),
    
    # تخصيص فريق لعمل
    path('job/<int:job_id>/assign-team/', teams_views.assign_team_to_job, name='assign_team_to_job'),
    
    # إلغاء تخصيص فريق من عمل
    path('job/<int:job_id>/team/<int:team_id>/unassign/', teams_views.unassign_team_from_job, name='unassign_team_from_job'),
    
    # عرض الأعمال المخصصة لفريق
    path('<int:team_id>/jobs/', teams_views.team_jobs, name='team_jobs'),
    
    # تقارير فرق العمل
    path('reports/', teams_views.team_reports, name='team_reports'),
]
