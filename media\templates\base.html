<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ company_info.name|default:"نظام إدارة متجر قطع غيار السيارات" }}{% endblock %}</title>

    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="/static/css/style.css">

    <style>
        /* أنماط للقوائم المنسدلة */
        .dropdown-menu {
            max-height: 300px;
            overflow-y: auto;
        }

        /* تنسيق القوائم المنسدلة عند فتحها */
        select.form-select option {
            padding: 8px 12px;
        }

        /* تأكد من أن الأزرار تظهر بشكل صحيح */
        .mt-2 {
            margin-top: 0.75rem !important;
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    {% csrf_token %}
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{% url 'dashboard:index' %}">
                <i class="fas fa-car-alt me-2"></i>{{ company_info.name|default:"نظام إدارة متجر قطع غيار السيارات" }}
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.app_name == 'dashboard' %}active{% endif %}" href="{% url 'dashboard:index' %}">
                            <i class="fas fa-tachometer-alt me-1"></i> لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle {% if request.resolver_match.app_name == 'inventory' %}active{% endif %}" href="#" id="inventoryDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-boxes me-1"></i> المخزون
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="inventoryDropdown">
                            <li><a class="dropdown-item" href="{% url 'inventory:index' %}"><i class="fas fa-list me-1"></i> قائمة المنتجات</a></li>
                            <li><a class="dropdown-item" href="{% url 'inventory:stock_alerts' %}"><i class="fas fa-exclamation-triangle me-1"></i> تنبيهات المخزون</a></li>
                            <li><a class="dropdown-item" href="{% url 'inventory:categories' %}"><i class="fas fa-tags me-1"></i> إدارة الفئات</a></li>
                            <li><a class="dropdown-item" href="{% url 'inventory:storage_locations' %}"><i class="fas fa-warehouse me-1"></i> إدارة مواقع التخزين</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/inventory/barcode/dashboard/"><i class="fas fa-tachometer-alt me-1"></i> لوحة تحكم الباركود</a></li>
                            <li><a class="dropdown-item" href="/inventory/barcode/settings/"><i class="fas fa-cog me-1"></i> إعدادات الباركود</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'inventory:add_product' %}"><i class="fas fa-plus me-1"></i> إضافة منتج جديد</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.app_name == 'customers' %}active{% endif %}" href="{% url 'customers:index' %}">
                            <i class="fas fa-users me-1"></i> العملاء
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.app_name == 'sales' %}active{% endif %}" href="{% url 'sales:index' %}">
                            <i class="fas fa-shopping-cart me-1"></i> المبيعات
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle {% if request.resolver_match.app_name == 'purchases' %}active{% endif %}" href="#" id="purchasesDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-truck me-1"></i> المشتريات
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="purchasesDropdown">
                            <li><a class="dropdown-item" href="{% url 'purchases:index' %}"><i class="fas fa-tachometer-alt me-1"></i> لوحة التحكم</a></li>
                            <li><a class="dropdown-item" href="{% url 'purchases:purchase_orders' %}"><i class="fas fa-shopping-cart me-1"></i> طلبات الشراء</a></li>
                            <li><a class="dropdown-item" href="{% url 'purchases:suppliers' %}"><i class="fas fa-users me-1"></i> الموردين</a></li>
                            <li><a class="dropdown-item" href="{% url 'purchases:invoices' %}"><i class="fas fa-file-invoice-dollar me-1"></i> الفواتير</a></li>
                            <li><a class="dropdown-item" href="{% url 'purchases:payments' %}"><i class="fas fa-money-bill-wave me-1"></i> المدفوعات</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'purchases:new_purchase' %}"><i class="fas fa-plus me-1"></i> طلب شراء جديد</a></li>
                            <li><a class="dropdown-item" href="{% url 'purchases:reports' %}"><i class="fas fa-chart-bar me-1"></i> التقارير</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.app_name == 'finance' %}active{% endif %}" href="{% url 'finance:index' %}">
                            <i class="fas fa-money-bill-wave me-1"></i> المالية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.app_name == 'employees' %}active{% endif %}" href="{% url 'employees:index' %}">
                            <i class="fas fa-user-tie me-1"></i> الموظفين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.app_name == 'reports' %}active{% endif %}" href="{% url 'reports:index' %}">
                            <i class="fas fa-chart-bar me-1"></i> التقارير
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.app_name == 'settings_app' %}active{% endif %}" href="{% url 'settings_app:index' %}">
                            <i class="fas fa-cog me-1"></i> الإعدادات
                        </a>
                    </li>
                </ul>
                <div class="d-flex">
                    {% if user.is_authenticated %}
                    <div class="dropdown">
                        <button class="btn btn-light dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user me-1"></i> {{ user.username }}
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                            <li><a class="dropdown-item" href="{% url 'settings_app:profile' %}"><i class="fas fa-user-cog me-1"></i> الملف الشخصي</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'logout' %}"><i class="fas fa-sign-out-alt me-1"></i> تسجيل الخروج</a></li>
                        </ul>
                    </div>
                    {% else %}
                    <a href="{% url 'login' %}" class="btn btn-light"><i class="fas fa-sign-in-alt me-1"></i> تسجيل الدخول</a>
                    {% endif %}
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        <div class="container mt-4">
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}

            {% block content %}{% endblock %}
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-white text-center py-3" id="main-footer">
        <div class="container">
            <p class="mb-0">جميع الحقوق محفوظة &copy; {{ current_year }} {{ company_info.name|default:"نظام إدارة متجر قطع غيار السيارات" }}</p>
        </div>
    </footer>

    <!-- مسافة إضافية لضمان عدم تداخل الشريط السفلي مع المحتوى -->
    <div style="height: 50px;"></div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="/static/js/main.js"></script>
    <script src="/static/js/navbar-fix.js"></script>
    <script src="/static/js/custom.js"></script>
    <script src="/static/js/custom-dropdowns.js"></script>

    <!-- تطبيق ميزة التمرير على جميع القوائم المنسدلة -->
    <script>
        $(document).ready(function() {
            // تطبيق ميزة التمرير على القوائم المنسدلة
            $('.dropdown-menu').css('max-height', '300px').css('overflow-y', 'auto');

            // تطبيق ميزة التمرير على عناصر select عندما يكون عدد العناصر أكثر من 10
            // تم نقل هذه الوظيفة إلى ملف custom-dropdowns.js
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>

</html>
