{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "إضافة منتج جديد" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .required-field::after {
        content: " *";
        color: red;
    }
    
    .form-card {
        border-radius: 10px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    }
    
    .form-card .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #e3e6f0;
    }
    
    .form-section {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .form-section-title {
        border-bottom: 1px solid #dee2e6;
        padding-bottom: 10px;
        margin-bottom: 20px;
    }
    
    .btn-submit {
        min-width: 120px;
    }
    
    .image-preview {
        width: 200px;
        height: 200px;
        border: 2px dashed #ccc;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        margin-bottom: 15px;
    }
    
    .image-preview img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
    }
    
    .image-preview-placeholder {
        color: #6c757d;
        text-align: center;
    }
    
    /* تنسيقات ماسح الباركود */
    #scanner-viewport {
        background-color: #000;
        position: relative;
    }
    
    #scanner-viewport canvas, #scanner-viewport video {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
    }
    
    #scanner-viewport .drawingBuffer {
        position: absolute;
        top: 0;
        left: 0;
    }
    
    .barcode-scanner-border {
        position: absolute;
        top: 50%;
        left: 50%;
        width: 70%;
        height: 30%;
        transform: translate(-50%, -50%);
        border: 2px solid #007bff;
        border-radius: 10px;
        box-shadow: 0 0 0 100vw rgba(0, 0, 0, 0.5);
        pointer-events: none;
    }
    
    /* تأثير عند مسح الباركود */
    .barcode-scanned {
        animation: barcode-success 0.5s;
    }
    
    @keyframes barcode-success {
        0% { background-color: rgba(40, 167, 69, 0); }
        50% { background-color: rgba(40, 167, 69, 0.2); }
        100% { background-color: rgba(40, 167, 69, 0); }
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0 text-gray-800">{% trans "إضافة منتج جديد" %}</h1>
    <a href="{% url 'inventory:index' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-1"></i> {% trans "العودة إلى المخزون" %}
    </a>
</div>

{% if messages %}
    <div class="animated fadeIn">
    {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            <i class="fas {% if message.tags == 'success' %}fa-check-circle{% elif message.tags == 'error' %}fa-exclamation-circle{% else %}fa-info-circle{% endif %} me-2"></i>
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    {% endfor %}
    </div>
{% endif %}

<div class="card shadow mb-4 form-card">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">{% trans "معلومات المنتج" %}</h6>
    </div>
    <div class="card-body">
        <form method="post" enctype="multipart/form-data" id="productForm">
            {% csrf_token %}

            <!-- Basic Information Section -->
            <div class="form-section">
                <h5 class="form-section-title">
                    <i class="fas fa-info-circle me-2"></i>
                    {% trans "المعلومات الأساسية" %}
                </h5>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="code" class="form-label required-field">{% trans "كود المنتج" %}</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-hashtag"></i></span>
                            <input type="text" class="form-control" id="code" name="code" required>
                        </div>
                        <div class="form-text">{% trans "يجب أن يكون الكود فريدًا" %}</div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="barcode" class="form-label">{% trans "باركود المنتج" %}</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-barcode"></i></span>
                            <input type="text" class="form-control" id="barcode" name="barcode">
                            <button class="btn btn-outline-secondary" type="button" id="scanBarcodeBtn" title="{% trans "مسح الباركود" %}">
                                <i class="fas fa-camera"></i>
                            </button>
                        </div>
                        <div class="form-text">{% trans "يجب أن يكون الباركود فريدًا" %}</div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="name" class="form-label required-field">{% trans "اسم المنتج" %}</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-tag"></i></span>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="category" class="form-label required-field">{% trans "الفئة" %}</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-folder"></i></span>
                            <select class="form-select" id="category" name="category" required>
                                <option value="">{% trans "اختر الفئة" %}</option>
                                {% for category in categories %}
                                <option value="{{ category.id }}">{{ category.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="mt-2">
                            <a href="{% url 'inventory:categories' %}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-plus me-1"></i> {% trans "إدارة الفئات" %}
                            </a>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="supplier" class="form-label">{% trans "المورد" %}</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-truck"></i></span>
                            <select class="form-select" id="supplier" name="supplier">
                                <option value="">{% trans "اختر المورد" %}</option>
                                {% for supplier in suppliers %}
                                <option value="{{ supplier.id }}">{{ supplier.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="mt-2">
                            <a href="{% url 'purchases:suppliers' %}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-plus me-1"></i> {% trans "إدارة الموردين" %}
                            </a>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="storage_location" class="form-label">{% trans "مكان التخزين" %}</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-warehouse"></i></span>
                            <select class="form-select" id="storage_location" name="storage_location">
                                <option value="">{% trans "اختر مكان التخزين" %}</option>
                                {% for location in storage_locations %}
                                <option value="{{ location.id }}">{{ location.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="mt-2">
                            <a href="{% url 'inventory:storage_locations' %}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-plus me-1"></i> {% trans "إدارة مواقع التخزين" %}
                            </a>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="description" class="form-label">{% trans "وصف المنتج" %}</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-align-left"></i></span>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                </div>
            </div>

            <!-- Pricing and Stock Section -->
            <div class="form-section">
                <h5 class="form-section-title">
                    <i class="fas fa-money-bill-wave me-2"></i>
                    {% trans "التسعير والمخزون" %}
                </h5>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="purchase_price" class="form-label required-field">{% trans "سعر الشراء" %}</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-shopping-cart"></i></span>
                            <input type="number" class="form-control" id="purchase_price" name="purchase_price" step="0.01" min="0" required>
                            <span class="input-group-text">د.م</span>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="selling_price" class="form-label required-field">{% trans "سعر البيع" %}</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-tags"></i></span>
                            <input type="number" class="form-control" id="selling_price" name="selling_price" step="0.01" min="0" required>
                            <span class="input-group-text">د.م</span>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="quantity" class="form-label required-field">{% trans "الكمية الحالية" %}</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-cubes"></i></span>
                            <input type="number" class="form-control" id="quantity" name="quantity" min="0" value="0" required>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="unit" class="form-label required-field">
                            <i class="fas fa-balance-scale me-2"></i>{% trans "الوحدة" %}
                        </label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-ruler"></i></span>
                            <select class="form-select" id="unit" name="unit" required>
                                <option value="">{% trans "اختر الوحدة..." %}</option>
                                <option value="piece">📦 {% trans "قطعة" %}</option>
                                <option value="kg">⚖️ {% trans "كيلوغرام" %}</option>
                                <option value="gram">⚖️ {% trans "غرام" %}</option>
                                <option value="liter">🥤 {% trans "لتر" %}</option>
                                <option value="ml">🥤 {% trans "مليلتر" %}</option>
                                <option value="meter">📏 {% trans "متر" %}</option>
                                <option value="cm">📏 {% trans "سنتيمتر" %}</option>
                                <option value="mm">📏 {% trans "مليمتر" %}</option>
                                <option value="m2">📐 {% trans "متر مربع" %}</option>
                                <option value="cm2">📐 {% trans "سنتيمتر مربع" %}</option>
                                <option value="m3">📦 {% trans "متر مكعب" %}</option>
                                <option value="cm3">📦 {% trans "سنتيمتر مكعب" %}</option>
                                <option value="box">📦 {% trans "صندوق" %}</option>
                                <option value="pack">📦 {% trans "علبة" %}</option>
                                <option value="bottle">🍼 {% trans "زجاجة" %}</option>
                                <option value="bag">👜 {% trans "كيس" %}</option>
                                <option value="roll">🧻 {% trans "لفة" %}</option>
                                <option value="sheet">📄 {% trans "ورقة" %}</option>
                                <option value="pair">👟 {% trans "زوج" %}</option>
                                <option value="set">🎯 {% trans "طقم" %}</option>
                                <option value="dozen">🥚 {% trans "دزينة" %}</option>
                                <option value="carton">📦 {% trans "كرتونة" %}</option>
                                <option value="pallet">🚛 {% trans "منصة نقالة" %}</option>
                                <option value="ton">⚖️ {% trans "طن" %}</option>
                                <option value="inch">📏 {% trans "بوصة" %}</option>
                                <option value="foot">📏 {% trans "قدم" %}</option>
                                <option value="yard">📏 {% trans "ياردة" %}</option>
                                <option value="gallon">🥤 {% trans "غالون" %}</option>
                                <option value="other">❓ {% trans "أخرى" %}</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="min_quantity" class="form-label required-field">{% trans "الحد الأدنى للكمية" %}</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-exclamation-triangle"></i></span>
                            <input type="number" class="form-control" id="min_quantity" name="min_quantity" min="1" value="1" required>
                        </div>
                        <div class="form-text">{% trans "سيتم تنبيهك عندما تصل الكمية إلى هذا الحد أو أقل" %}</div>
                    </div>
                </div>

                <!-- حقل الوحدة المخصصة (يظهر عند اختيار "أخرى") -->
                <div class="row" id="customUnitRow" style="display: none;">
                    <div class="col-md-6 mb-3">
                        <label for="custom_unit" class="form-label">
                            <i class="fas fa-edit me-2"></i>{% trans "الوحدة المخصصة" %}
                        </label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-pencil-alt"></i></span>
                            <input type="text" class="form-control" id="custom_unit" name="custom_unit" placeholder="{% trans 'أدخل الوحدة المخصصة...' %}">
                        </div>
                        <div class="form-text">{% trans "أدخل اسم الوحدة المخصصة (مثل: علبة صغيرة، حبة، إلخ)" %}</div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="profit_margin" class="form-label">{% trans "هامش الربح" %}</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-chart-line"></i></span>
                            <input type="text" class="form-control" id="profit_margin" readonly>
                            <span class="input-group-text">%</span>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="form-check mt-4">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                            <label class="form-check-label" for="is_active">
                                {% trans "منتج نشط" %}
                            </label>
                            <div class="form-text">{% trans "إلغاء التحديد سيخفي المنتج من صفحة المبيعات" %}</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Image Section -->
            <div class="form-section">
                <h5 class="form-section-title">
                    <i class="fas fa-image me-2"></i>
                    {% trans "صورة المنتج" %}
                </h5>
                <div class="row">
                    <div class="col-md-4">
                        <div class="image-preview" id="imagePreview">
                            <div class="image-preview-placeholder">
                                <i class="fas fa-image fa-3x mb-2"></i>
                                <p>{% trans "معاينة الصورة" %}</p>
                            </div>
                        </div>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-upload"></i></span>
                            <input type="file" class="form-control" id="image" name="image" accept="image/*">
                        </div>
                    </div>
                    <div class="col-md-8">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-1"></i>
                            {% trans "نصائح للصور:" %}
                            <ul class="mb-0 mt-2">
                                <li>{% trans "استخدم صورًا واضحة وعالية الجودة" %}</li>
                                <li>{% trans "الحجم المثالي: 800×800 بكسل" %}</li>
                                <li>{% trans "الصيغ المدعومة: JPG، PNG، GIF" %}</li>
                                <li>{% trans "الحد الأقصى لحجم الملف: 5 ميجابايت" %}</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                <button type="button" class="btn btn-secondary me-md-2" onclick="window.location.href='{% url 'inventory:index' %}'">
                    <i class="fas fa-times me-1"></i> {% trans "إلغاء" %}
                </button>
                <button type="submit" class="btn btn-primary btn-submit">
                    <i class="fas fa-save me-1"></i> {% trans "حفظ المنتج" %}
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Add Storage Location Modal -->
<div class="modal fade" id="addLocationModal" tabindex="-1" aria-labelledby="addLocationModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addLocationModalLabel">{% trans "إضافة مكان تخزين جديد" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="locationForm">
                    <div class="mb-3">
                        <label for="locationName" class="form-label required-field">{% trans "اسم مكان التخزين" %}</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-warehouse"></i></span>
                            <input type="text" class="form-control" id="locationName" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="locationDescription" class="form-label">{% trans "وصف مكان التخزين" %}</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-align-left"></i></span>
                            <textarea class="form-control" id="locationDescription" rows="3"></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i> {% trans "إلغاء" %}
                </button>
                <button type="button" class="btn btn-primary" id="saveLocationBtn">
                    <i class="fas fa-save me-1"></i> {% trans "حفظ" %}
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/quagga@0.12.1/dist/quagga.min.js"></script>
<script>
    $(document).ready(function() {
        // إظهار/إخفاء حقل الوحدة المخصصة
        $('#unit').change(function() {
            const selectedUnit = $(this).val();
            if (selectedUnit === 'other') {
                $('#customUnitRow').show();
                $('#custom_unit').attr('required', true);
            } else {
                $('#customUnitRow').hide();
                $('#custom_unit').attr('required', false).val('');
            }
        });

        // Multiple Image Previews with Delete Functionality
        $('#images').change(function(e) {
            const files = e.target.files;
            const previewContainer = $('#imagePreviews');
            previewContainer.empty();

            Array.from(files).forEach((file, index) => {
                const reader = new FileReader();
                const preview = $('.image-preview-template').clone().removeClass('image-preview-template').show();

                reader.onload = function(e) {
                    preview.find('.image-preview').html(`<img src="${e.target.result}" alt="Preview ${index + 1}">`);
                    preview.find('.remove-image').click(function() {
                        preview.remove();
                        const dt = new DataTransfer();
                        Array.from($('#images')[0].files).filter(f => f !== file)
                            .forEach(f => dt.items.add(f));
                        $('#images')[0].files = dt.files;
                    });
                }
                
                reader.readAsDataURL(file);
                previewContainer.append(preview);
            });
        });
// Multiple Image Previews with Delete Functionality
        $('#images').change(function(e) {
            const files = e.target.files;
            const previewContainer = $('#imagePreviews');
            previewContainer.empty();

            Array.from(files).forEach((file, index) => {
                const reader = new FileReader();
                const preview = $('.image-preview-template').clone().removeClass('image-preview-template').show();

                reader.onload = function(e) {
                    preview.find('.image-preview').html(`<img src="${e.target.result}" alt="Preview ${index + 1}">`);
                    preview.find('.remove-image').click(function() {
                        preview.remove();
                        const dt = new DataTransfer();
                        Array.from($('#images')[0].files).filter(f => f !== file)
                            .forEach(f => dt.items.add(f));
                        $('#images')[0].files = dt.files;
                    });
                }
                
                reader.readAsDataURL(file);
                previewContainer.append(preview);
            });
        });

        // Drag and drop functionality
        const dropArea = $('#imagePreviews');
        dropArea.on('dragover', function(e) {
            e.preventDefault();
            dropArea.addClass('border-primary');
        });

        dropArea.on('dragleave', function() {
            dropArea.removeClass('border-primary');
        });

        dropArea.on('drop', function(e) {
            e.preventDefault();
            dropArea.removeClass('border-primary');
            const files = e.originalEvent.dataTransfer.files;
            $('#images')[0].files = files;
            $('#images').trigger('change');
        });

        // Image Preview
        $('#image').change(function() {
            const file = this.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    $('#imagePreview').html('<img src="' + e.target.result + '" alt="Preview">');
                }
                reader.readAsDataURL(file);
            } else {
                $('#imagePreview').html('<div class="image-preview-placeholder"><i class="fas fa-image fa-3x mb-2"></i><p>{% trans "معاينة الصورة" %}</p></div>');
            }
        });

        // Image Preview
        $('#image').change(function() {
            const file = this.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    $('#imagePreview').html('<img src="' + e.target.result + '" alt="Preview">');
                }
                reader.readAsDataURL(file);
            } else {
                $('#imagePreview').html('<div class="image-preview-placeholder"><i class="fas fa-image fa-3x mb-2"></i><p>{% trans "معاينة الصورة" %}</p></div>');
            }
        });

        // Calculate Profit Margin
        function calculateProfitMargin() {
            const purchasePrice = parseFloat($('#purchase_price').val()) || 0;
            const sellingPrice = parseFloat($('#selling_price').val()) || 0;
            
            if (purchasePrice > 0 && sellingPrice > 0) {
                const profit = sellingPrice - purchasePrice;
                const margin = (profit / purchasePrice) * 100;
                $('#profit_margin').val(margin.toFixed(2));
            } else {
                $('#profit_margin').val('0.00');
            }
        }

        $('#purchase_price, #selling_price').on('input', calculateProfitMargin);

        // Form Validation
        $('#productForm').submit(function(e) {
            // Custom validation can be added here
            return true;
        });

        // Barcode Scanner
        $('#scanBarcodeBtn').click(function() {
            // إنشاء نافذة لمسح الباركود
            const scannerModal = $(`
                <div class="modal fade" id="barcodeScannerModal" tabindex="-1" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">{% trans "مسح الباركود" %}</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <div id="scanner-container" class="mb-3">
                                    <div id="scanner-viewport" style="position: relative; width: 100%; height: 300px; overflow: hidden; border-radius: 10px;"></div>
                                </div>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-1"></i>
                                    {% trans "وجه الكاميرا نحو الباركود ليتم مسحه تلقائيًا" %}
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                    <i class="fas fa-times me-1"></i> {% trans "إلغاء" %}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `);

            // إضافة النافذة إلى الصفحة
            $('body').append(scannerModal);
            
            // عرض النافذة
            const modal = new bootstrap.Modal(document.getElementById('barcodeScannerModal'));
            modal.show();

            // إضافة إطار توجيهي للمسح
            $('#scanner-viewport').append('<div class="barcode-scanner-border"></div>');
            
            // إضافة رسالة تحميل
            $('#scanner-viewport').html('<div class="text-center p-3"><i class="fas fa-spinner fa-spin fa-2x mb-2"></i><p>{% trans "جاري تهيئة الكاميرا..." %}</p></div>');
            
            // التحقق من وجود مكتبة Quagga
            if (typeof Quagga === 'undefined') {
                $('#scanner-viewport').html('<div class="alert alert-danger"><i class="fas fa-exclamation-triangle me-2"></i>{% trans "لم يتم تحميل مكتبة ماسح الباركود بشكل صحيح" %}</div>');
                return;
            }
            
            // تهيئة ماسح الباركود
            Quagga.init({
                inputStream: {
                    name: "Live",
                    type: "LiveStream",
                    target: document.querySelector('#scanner-viewport'),
                    constraints: {
                        width: 480,
                        height: 320,
                        facingMode: "environment" // استخدام الكاميرا الخلفية
                    },
                },
                locator: {
                    patchSize: "medium",
                    halfSample: true
                },
                numOfWorkers: 2,
                frequency: 10,
                decoder: {
                    readers: [
                        "code_128_reader",
                        "ean_reader",
                        "ean_8_reader",
                        "code_39_reader",
                        "code_39_vin_reader",
                        "codabar_reader",
                        "upc_reader",
                        "upc_e_reader",
                        "i2of5_reader"
                    ],
                    debug: {
                        showCanvas: true,
                        showPatches: false,
                        showFoundPatches: false,
                        showSkeleton: false,
                        showLabels: false,
                        showPatchLabels: false,
                        showRemainingPatchLabels: false,
                        boxFromPatches: {
                            showTransformed: false,
                            showTransformedBox: false,
                            showBB: true
                        }
                    }
                },
            }, function(err) {
                if (err) {
                    console.error("خطأ في تهيئة ماسح الباركود:", err);
                    
                    // عرض رسالة خطأ مناسبة للمستخدم
                    let errorMessage = '{% trans "حدث خطأ أثناء تهيئة ماسح الباركود" %}';
                    
                    // تحديد نوع الخطأ وعرض رسالة مناسبة
                    if (err.name === 'NotAllowedError' || err.name === 'PermissionDeniedError') {
                        errorMessage = '{% trans "لم يتم السماح بالوصول إلى الكاميرا. يرجى السماح بالوصول للكاميرا في إعدادات المتصفح." %}';
                    } else if (err.name === 'NotFoundError' || err.name === 'DevicesNotFoundError') {
                        errorMessage = '{% trans "لم يتم العثور على كاميرا متصلة بجهازك." %}';
                    } else if (err.name === 'NotReadableError' || err.name === 'TrackStartError') {
                        errorMessage = '{% trans "لا يمكن الوصول إلى الكاميرا. قد تكون مستخدمة من قبل تطبيق آخر." %}';
                    } else if (err.name === 'OverconstrainedError' || err.name === 'ConstraintNotSatisfiedError') {
                        errorMessage = '{% trans "لا يمكن استخدام الكاميرا بالإعدادات المطلوبة." %}';
                    }
                    
                    // عرض رسالة الخطأ في نافذة الماسح
                    $('#scanner-viewport').html(`<div class="alert alert-danger"><i class="fas fa-exclamation-triangle me-2"></i>${errorMessage}</div>`);
                    
                    // لا نضيف زر الإدخال اليدوي هنا لأننا سنضيفه لاحقاً في جميع الحالات
                    return;
                }
                
                // بدء المسح
                try {
                    Quagga.start();
                    // إعادة إنشاء عنصر الماسح بعد التهيئة الناجحة
                    $('#scanner-viewport').html('<div id="scanner-viewport" style="position: relative; width: 100%; height: 300px; overflow: hidden; border-radius: 10px;"></div>');
                    // إضافة إطار توجيهي للمسح
                    $('#scanner-viewport').append('<div class="barcode-scanner-border"></div>');
                } catch (error) {
                    console.error("خطأ في بدء ماسح الباركود:", error);
                    $('#scanner-viewport').html(`<div class="alert alert-danger"><i class="fas fa-exclamation-triangle me-2"></i>{% trans "حدث خطأ أثناء تشغيل ماسح الباركود" %}</div>`);
                }
            });
            
            // إضافة تأثير بصري عند اكتشاف الباركود
            Quagga.onProcessed(function(result) {
                var drawingCtx = Quagga.canvas.ctx.overlay,
                    drawingCanvas = Quagga.canvas.dom.overlay;

                if (result) {
                    if (result.boxes) {
                        drawingCtx.clearRect(0, 0, parseInt(drawingCanvas.getAttribute("width")), parseInt(drawingCanvas.getAttribute("height")));
                        result.boxes.filter(function (box) {
                            return box !== result.box;
                        }).forEach(function (box) {
                            Quagga.ImageDebug.drawPath(box, {x: 0, y: 1}, drawingCtx, {color: "green", lineWidth: 2});
                        });
                    }

                    if (result.box) {
                        Quagga.ImageDebug.drawPath(result.box, {x: 0, y: 1}, drawingCtx, {color: "#00F", lineWidth: 2});
                    }

                    if (result.codeResult && result.codeResult.code) {
                        Quagga.ImageDebug.drawPath(result.line, {x: 'x', y: 'y'}, drawingCtx, {color: 'red', lineWidth: 3});
                    }
                }
            });

            // التقاط الباركود عند اكتشافه
            Quagga.onDetected(function(result) {
                if (result && result.codeResult) {
                    const code = result.codeResult.code;
                    
                    // إضافة تأثير بصري عند نجاح المسح
                    $('#scanner-viewport').addClass('barcode-scanned');
                    
                    // عرض رسالة نجاح
                    const successAlert = $('<div class="alert alert-success text-center"><i class="fas fa-check-circle me-2"></i>{% trans "تم مسح الباركود بنجاح" %}</div>');
                    $('#scanner-container').append(successAlert);
                    
                    // تأخير قبل إغلاق النافذة لإظهار التأثير البصري
                    setTimeout(function() {
                        // وضع الباركود في الحقل
                        $('#barcode').val(code);
                        $('#barcode').addClass('is-valid');
                        
                        // إغلاق النافذة وإيقاف المسح
                        Quagga.stop();
                        modal.hide();
                        $('#barcodeScannerModal').remove();
                        
                        // إزالة تأثير التحقق بعد فترة
                        setTimeout(function() {
                            $('#barcode').removeClass('is-valid');
                        }, 3000);
                    }, 1000);
                }
            });
            
            // إضافة زر لإدخال الباركود يدويًا بتصميم محسن
            $('#barcodeScannerModal .modal-footer').prepend(
                $('<button type="button" class="btn btn-primary me-auto" id="manualBarcodeBtn"><i class="fas fa-keyboard me-1"></i>{% trans "إدخال يدوي" %}</button>')
                .click(function() {
                    const manualCode = prompt('{% trans "أدخل الباركود يدويًا:" %}');
                    if (manualCode && manualCode.trim() !== '') {
                        $('#barcode').val(manualCode.trim());
                        Quagga.stop();
                        modal.hide();
                        $('#barcodeScannerModal').remove();
                    }
                })
            );

            // إيقاف المسح عند إغلاق النافذة
            $('#barcodeScannerModal').on('hidden.bs.modal', function() {
                if (Quagga) {
                    Quagga.stop();
                }
                $(this).remove();
            });
        });
    });
</script>
{% endblock %}
