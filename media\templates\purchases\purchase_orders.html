{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "طلبات الشراء" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
<style>
    .filter-section {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .status-badge {
        font-size: 0.8rem;
        padding: 0.4rem 0.6rem;
    }

    .payment-badge {
        font-size: 0.75rem;
    }

    .purchase-row {
        transition: all 0.2s;
    }

    .purchase-row:hover {
        background-color: #f8f9fa;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0 text-gray-800">{% trans "طلبات الشراء" %}</h1>
    <div>
        <a href="{% url 'purchases:new_purchase' %}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i> {% trans "طلب شراء جديد" %}
        </a>
    </div>
</div>

{% if messages %}
    {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    {% endfor %}
{% endif %}

<!-- Filters -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">{% trans "تصفية النتائج" %}</h6>
    </div>
    <div class="card-body">
        <form action="{% url 'purchases:filter_purchases' %}" method="get" id="filterForm">
            <div class="row">
                <div class="col-md-3 mb-3">
                    <label for="status" class="form-label">{% trans "الحالة" %}</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">{% trans "الكل" %}</option>
                        {% for status_code, status_name in status_choices %}
                        <option value="{{ status_code }}" {% if status_filter == status_code %}selected{% endif %}>{{ status_name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="supplier" class="form-label">{% trans "المورد" %}</label>
                    <select class="form-select" id="supplier" name="supplier">
                        <option value="">{% trans "الكل" %}</option>
                        {% for supplier in suppliers %}
                        <option value="{{ supplier.id }}" {% if supplier_filter == supplier.id|stringformat:"i" %}selected{% endif %}>{{ supplier.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="date_from" class="form-label">{% trans "من تاريخ" %}</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="{{ date_from }}">
                </div>
                <div class="col-md-3 mb-3">
                    <label for="date_to" class="form-label">{% trans "إلى تاريخ" %}</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="{{ date_to }}">
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-1"></i> {% trans "تصفية" %}
                    </button>
                    <a href="{% url 'purchases:purchase_orders' %}" class="btn btn-secondary">
                        <i class="fas fa-redo me-1"></i> {% trans "إعادة ضبط" %}
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Purchases Table -->
<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
        <h6 class="m-0 font-weight-bold text-primary">{% trans "قائمة طلبات الشراء" %}</h6>
        <div class="dropdown no-arrow">
            <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
            </a>
            <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="dropdownMenuLink">
                <div class="dropdown-header">{% trans "خيارات" %}:</div>
                <a class="dropdown-item" href="{% url 'purchases:reports' %}">
                    <i class="fas fa-file-export me-1"></i> {% trans "تصدير" %}
                </a>
                <a class="dropdown-item" href="#" id="printBtn">
                    <i class="fas fa-print me-1"></i> {% trans "طباعة" %}
                </a>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover" id="purchasesTable" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th>{% trans "رقم المرجع" %}</th>
                        <th>{% trans "المورد" %}</th>
                        <th>{% trans "التاريخ" %}</th>
                        <th>{% trans "تاريخ التسليم المتوقع" %}</th>
                        <th>{% trans "المبلغ الإجمالي" %}</th>
                        <th>{% trans "حالة الطلب" %}</th>
                        <th>{% trans "حالة الدفع" %}</th>
                        <th>{% trans "الإجراءات" %}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for purchase in purchases %}
                    <tr class="purchase-row">
                        <td>
                            <a href="{% url 'purchases:view_purchase' purchase_id=purchase.id %}">
                                {{ purchase.reference_number }}
                            </a>
                        </td>
                        <td>
                            <a href="{% url 'purchases:view_supplier' supplier_id=purchase.supplier.id %}">
                                {{ purchase.supplier.name }}
                            </a>
                        </td>
                        <td>{{ purchase.date|date:"Y-m-d" }}</td>
                        <td>
                            {% if purchase.expected_delivery_date %}
                                {{ purchase.expected_delivery_date|date:"Y-m-d" }}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td>{{ purchase.total_amount|floatformat:2 }} ر.س</td>
                        <td>
                            {% if purchase.status == 'pending' %}
                            <span class="badge bg-warning text-dark status-badge">{% trans "معلق" %}</span>
                            {% elif purchase.status == 'received' %}
                            <span class="badge bg-success status-badge">{% trans "تم الاستلام" %}</span>
                            {% elif purchase.status == 'cancelled' %}
                            <span class="badge bg-danger status-badge">{% trans "ملغي" %}</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if purchase.payment_status == 'unpaid' %}
                            <span class="badge bg-danger payment-badge">{% trans "غير مدفوع" %}</span>
                            {% elif purchase.payment_status == 'partial' %}
                            <span class="badge bg-warning text-dark payment-badge">{% trans "مدفوع جزئياً" %}</span>
                            {% elif purchase.payment_status == 'paid' %}
                            <span class="badge bg-success payment-badge">{% trans "مدفوع بالكامل" %}</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group">
                                <a href="{% url 'purchases:view_purchase' purchase_id=purchase.id %}" class="btn btn-sm btn-info" title="{% trans 'عرض' %}">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if purchase.status == 'pending' %}
                                <a href="{% url 'purchases:edit_purchase' purchase_id=purchase.id %}" class="btn btn-sm btn-primary" title="{% trans 'تعديل' %}">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{% url 'purchases:receive_purchase' purchase_id=purchase.id %}" class="btn btn-sm btn-success" title="{% trans 'استلام' %}">
                                    <i class="fas fa-check"></i>
                                </a>
                                {% endif %}
                                {% if purchase.payment_status != 'paid' %}
                                <a href="{% url 'purchases:add_payment' purchase_id=purchase.id %}" class="btn btn-sm btn-warning" title="{% trans 'إضافة دفعة' %}">
                                    <i class="fas fa-money-bill-wave"></i>
                                </a>
                                {% endif %}
                                {% if purchase.status == 'pending' %}
                                <a href="{% url 'purchases:delete_purchase' purchase_id=purchase.id %}" class="btn btn-sm btn-danger" title="{% trans 'حذف' %}">
                                    <i class="fas fa-trash"></i>
                                </a>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="8" class="text-center">{% trans "لا توجد طلبات شراء" %}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
<script>
    $(document).ready(function() {
        // Initialize DataTable
        var table = $('#purchasesTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json",
                "lengthMenu": "إظهار _MENU_ من العناصر",
                "info": "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل"
            },
            "order": [[2, "desc"]],
            "pageLength": 10,
            "lengthMenu": [[5, 10, 15, 20, 25, 50, -1], [5, 10, 15, 20, 25, 50, "الكل"]],
            "dom": 'lfrtip'
        });

        // Print functionality
        $('#printBtn').click(function(e) {
            e.preventDefault();
            window.print();
        });

        // Auto-submit form on select change
        $('#status, #supplier').change(function() {
            $('#filterForm').submit();
        });
    });
</script>
{% endblock %}
