{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "إدارة المبيعات" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.2.2/css/buttons.bootstrap5.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.bootstrap5.min.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css">
<style>
    .filter-section {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .status-badge {
        font-size: 0.8rem;
        padding: 0.4rem 0.6rem;
    }

    .payment-badge {
        font-size: 0.75rem;
    }

    .sale-row {
        transition: all 0.2s;
    }

    .sale-row:hover {
        background-color: #f8f9fa;
    }

    .action-buttons .btn {
        margin-right: 5px;
    }

    .dt-buttons {
        margin-bottom: 15px;
    }

    .dt-button {
        background-color: #f8f9fa !important;
        border-color: #dee2e6 !important;
    }

    .dt-button:hover {
        background-color: #e9ecef !important;
    }

    .dataTables_filter {
        margin-bottom: 15px;
    }

    .dataTables_length {
        margin-bottom: 15px;
        margin-right: 15px;
    }

    .dataTables_length select {
        padding: 0.375rem 2.25rem 0.375rem 0.75rem;
        font-size: 1rem;
        font-weight: 400;
        line-height: 1.5;
        color: #212529;
        background-color: #fff;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }

    .dataTables_info {
        padding-top: 0.85em;
        white-space: nowrap;
        margin-right: 15px;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0 text-gray-800">{% trans "إدارة المبيعات" %}</h1>
    <div>
        <a href="{% url 'sales:new_sale' %}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i> {% trans "بيع جديد" %}
        </a>
    </div>
</div>

{% if messages %}
    {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    {% endfor %}
{% endif %}

<!-- Filter Section -->
<div class="filter-section">
    <form id="filterForm" method="get">
        <div class="row">
            <div class="col-md-3 mb-3">
                <label for="dateRange" class="form-label">{% trans "نطاق التاريخ" %}</label>
                <div class="input-group">
                    <input type="text" class="form-control" id="dateRange" name="date_range" placeholder="{% trans 'اختر نطاق التاريخ' %}">
                    <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <label for="customerFilter" class="form-label">{% trans "العميل" %}</label>
                <select class="form-select" id="customerFilter" name="customer">
                    <option value="">{% trans "جميع العملاء" %}</option>
                    {% for customer in customers %}
                    <option value="{{ customer.id }}">{{ customer.name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3 mb-3">
                <label for="statusFilter" class="form-label">{% trans "الحالة" %}</label>
                <select class="form-select" id="statusFilter" name="status">
                    <option value="">{% trans "جميع الحالات" %}</option>
                    <option value="completed">{% trans "مكتمل" %}</option>
                    <option value="pending">{% trans "معلق" %}</option>
                    <option value="cancelled">{% trans "ملغي" %}</option>
                </select>
            </div>
            <div class="col-md-3 mb-3">
                <label for="paymentMethodFilter" class="form-label">{% trans "طريقة الدفع" %}</label>
                <select class="form-select" id="paymentMethodFilter" name="payment_method">
                    <option value="">{% trans "جميع طرق الدفع" %}</option>
                    <option value="cash">{% trans "نقدي" %}</option>
                    <option value="card">{% trans "بطاقة ائتمان" %}</option>
                    <option value="transfer">{% trans "تحويل بنكي" %}</option>
                    <option value="check">{% trans "شيك" %}</option>
                    <option value="credit">{% trans "آجل" %}</option>
                </select>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6 mb-3">
                <label for="searchInput" class="form-label">{% trans "بحث" %}</label>
                <div class="input-group">
                    <input type="text" class="form-control" id="searchInput" name="search" placeholder="{% trans 'بحث برقم الفاتورة أو اسم العميل...' %}">
                    <button class="btn btn-primary" type="submit">
                        <i class="fas fa-search"></i> {% trans "بحث" %}
                    </button>
                </div>
            </div>
            <div class="col-md-6 d-flex align-items-end justify-content-end mb-3">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-filter me-1"></i> {% trans "تصفية" %}
                </button>
                <a href="{% url 'sales:index' %}" class="btn btn-secondary">
                    <i class="fas fa-redo me-1"></i> {% trans "إعادة تعيين" %}
                </a>
            </div>
        </div>
    </form>
</div>

<!-- Sales Table -->
<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
        <h6 class="m-0 font-weight-bold text-primary">{% trans "قائمة المبيعات" %}</h6>
        <div class="dropdown">
            <button class="btn btn-link dropdown-toggle p-0 text-decoration-none" type="button" id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
            </button>
            <ul class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="dropdownMenuButton">
                <li><h6 class="dropdown-header">{% trans "خيارات التصدير:" %}</h6></li>
                <li><a class="dropdown-item" href="{% url 'sales:export_sales_excel' %}" id="exportExcel">
                    <i class="fas fa-file-excel me-1 text-success"></i> {% trans "تصدير إلى Excel" %}
                </a></li>
                <li><a class="dropdown-item" href="{% url 'sales:export_sales_pdf' %}" id="exportPDF">
                    <i class="fas fa-file-pdf me-1 text-danger"></i> {% trans "تصدير إلى PDF" %}
                </a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" href="#" id="printTable">
                    <i class="fas fa-print me-1"></i> {% trans "طباعة" %}
                </a></li>
            </ul>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover" id="salesTable" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th>{% trans "رقم الفاتورة" %}</th>
                        <th>{% trans "العميل" %}</th>
                        <th>{% trans "التاريخ" %}</th>
                        <th>{% trans "المبلغ الإجمالي" %}</th>
                        <th>{% trans "طريقة الدفع" %}</th>
                        <th>{% trans "الحالة" %}</th>
                        <th>{% trans "الإجراءات" %}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for sale in sales %}
                    <tr class="sale-row">
                        <td>
                            <a href="{% url 'sales:view_sale' sale.id %}" class="fw-bold text-primary">
                                {{ sale.invoice_number }}
                            </a>
                        </td>
                        <td>{{ sale.customer.name }}</td>
                        <td>{{ sale.date|date:"Y-m-d H:i" }}</td>
                        <td>{{ sale.total_amount|floatformat:2 }} د.م</td>
                        <td>
                            {% if sale.payment_method == 'cash' %}
                            <span class="badge bg-success payment-badge">{% trans "نقدي" %}</span>
                            {% elif sale.payment_method == 'card' %}
                            <span class="badge bg-info payment-badge">{% trans "بطاقة ائتمان" %}</span>
                            {% elif sale.payment_method == 'transfer' %}
                            <span class="badge bg-primary payment-badge">{% trans "تحويل بنكي" %}</span>
                            {% elif sale.payment_method == 'check' %}
                            <span class="badge bg-secondary payment-badge">{% trans "شيك" %}</span>
                            {% elif sale.payment_method == 'credit' %}
                            <span class="badge bg-warning payment-badge">{% trans "آجل" %}</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if sale.status == 'completed' %}
                            <span class="badge bg-success status-badge">{% trans "مكتمل" %}</span>
                            {% elif sale.status == 'pending' %}
                            <span class="badge bg-warning status-badge">{% trans "معلق" %}</span>
                            {% elif sale.status == 'cancelled' %}
                            <span class="badge bg-danger status-badge">{% trans "ملغي" %}</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="action-buttons">
                                <a href="{% url 'sales:view_sale' sale.id %}" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="{% trans 'عرض' %}">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'sales:invoice' sale.id %}" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="{% trans 'الفاتورة' %}">
                                    <i class="fas fa-file-invoice"></i>
                                </a>
                                {% if sale.status != 'cancelled' %}
                                <a href="{% url 'sales:edit_sale' sale.id %}" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="{% trans 'تعديل' %}">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% endif %}
                                <a href="{% url 'sales:delete_sale' sale.id %}" class="btn btn-sm btn-danger" data-bs-toggle="tooltip" title="{% trans 'حذف' %}">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="7" class="text-center">{% trans "لا توجد مبيعات" %}</td>
                    </tr>
                    {% endfor %}
                </tbody>
                <tfoot>
                    <tr class="table-primary">
                        <td colspan="3" class="text-end"><strong>{% trans "المجموع الإجمالي:" %}</strong></td>
                        <td id="totalAmount"></td>
                        <td colspan="3"></td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
</div>

<!-- Sales Statistics -->
<div class="row">
    <!-- Sales by Status -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">{% trans "المبيعات حسب الحالة" %}</h6>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="salesByStatusChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Sales by Payment Method -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">{% trans "المبيعات حسب طريقة الدفع" %}</h6>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="salesByPaymentMethodChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.print.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.2.9/js/dataTables.responsive.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.2.9/js/responsive.bootstrap5.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdn.jsdelivr.net/npm/moment/moment.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<!-- إصلاح مشكلة النوافذ المنسدلة -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة القوائم المنسدلة في صفحة المبيعات
    const dropdownToggle = document.querySelector('#dropdownMenuButton');
    if (dropdownToggle) {
        new bootstrap.Dropdown(dropdownToggle);
    }
});
</script>

<script>
    $(document).ready(function() {
        // Initialize DataTable
        var table = $('#salesTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json",
                "lengthMenu": "إظهار _MENU_ من العناصر",
                "info": "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل"
            },
            "order": [[2, "desc"]],
            "pageLength": 10,
            "responsive": true,
            "dom": 'Blfrtip',
            "lengthMenu": [[5, 10, 15, 20, 25, 50, -1], [5, 10, 15, 20, 25, 50, "الكل"]],
            "footerCallback": function(row, data, start, end, display) {
                var api = this.api();

                // Remove the formatting to get integer data for summation
                var intVal = function(i) {
                    if (typeof i === 'string') {
                        // استخراج الرقم من النص بغض النظر عن التنسيق
                        var numStr = i.replace(/[^\d.-]/g, '');
                        return parseFloat(numStr) || 0;
                    }
                    return typeof i === 'number' ? i : 0;
                };

                // Calculate total for the 'المبلغ الإجمالي' column (index 3)
                var total = 0;
                api.column(3, { search: 'applied' }).data().each(function(value) {
                    total += intVal(value);
                });

                // Update footer with the calculated total
                $('#totalAmount').html(total.toFixed(2) + ' د.م');
            },
            "buttons": [
                {
                    extend: 'excel',
                    text: '<i class="fas fa-file-excel"></i> Excel',
                    className: 'btn btn-success btn-sm',
                    exportOptions: {
                        columns: [0, 1, 2, 3, 4, 5]
                    }
                },
                {
                    extend: 'pdf',
                    text: '<i class="fas fa-file-pdf"></i> PDF',
                    className: 'btn btn-danger btn-sm',
                    exportOptions: {
                        columns: [0, 1, 2, 3, 4, 5]
                    },
                    customize: function(doc) {
                        doc.defaultStyle.alignment = 'right';
                        doc.defaultStyle.direction = 'rtl';
                    }
                },
                {
                    extend: 'print',
                    text: '<i class="fas fa-print"></i> طباعة',
                    className: 'btn btn-info btn-sm',
                    exportOptions: {
                        columns: [0, 1, 2, 3, 4, 5]
                    }
                }
            ]
        });

        // Initialize DateRangePicker
        $('#dateRange').daterangepicker({
            opens: 'left',
            locale: {
                format: 'YYYY/MM/DD',
                applyLabel: 'تطبيق',
                cancelLabel: 'إلغاء',
                fromLabel: 'من',
                toLabel: 'إلى',
                customRangeLabel: 'مخصص',
                daysOfWeek: ['أحد', 'إثنين', 'ثلاثاء', 'أربعاء', 'خميس', 'جمعة', 'سبت'],
                monthNames: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
                firstDay: 0
            },
            autoUpdateInput: false
        });

        $('#dateRange').on('apply.daterangepicker', function(ev, picker) {
            $(this).val(picker.startDate.format('YYYY/MM/DD') + ' - ' + picker.endDate.format('YYYY/MM/DD'));
        });

        $('#dateRange').on('cancel.daterangepicker', function(ev, picker) {
            $(this).val('');
        });

        // Export buttons with filter parameters
        $('#exportExcel').click(function(e) {
            e.preventDefault();
            // Get filter values
            var dateRange = $('#dateRange').val();
            var customerId = $('#customerFilter').val();
            var status = $('#statusFilter').val();
            var paymentMethod = $('#paymentMethodFilter').val();
            var search = $('#searchInput').val();
            
            // Build URL with filter parameters
            var url = $(this).attr('href');
            var params = [];
            
            if (dateRange) params.push('date_range=' + encodeURIComponent(dateRange));
            if (customerId) params.push('customer=' + encodeURIComponent(customerId));
            if (status) params.push('status=' + encodeURIComponent(status));
            if (paymentMethod) params.push('payment_method=' + encodeURIComponent(paymentMethod));
            if (search) params.push('search=' + encodeURIComponent(search));
            
            if (params.length > 0) {
                url += '?' + params.join('&');
            }
            
            // Navigate to the URL
            window.location.href = url;
        });

        $('#exportPDF').click(function(e) {
            e.preventDefault();
            // Get filter values
            var dateRange = $('#dateRange').val();
            var customerId = $('#customerFilter').val();
            var status = $('#statusFilter').val();
            var paymentMethod = $('#paymentMethodFilter').val();
            var search = $('#searchInput').val();
            
            // Build URL with filter parameters
            var url = $(this).attr('href');
            var params = [];
            
            if (dateRange) params.push('date_range=' + encodeURIComponent(dateRange));
            if (customerId) params.push('customer=' + encodeURIComponent(customerId));
            if (status) params.push('status=' + encodeURIComponent(status));
            if (paymentMethod) params.push('payment_method=' + encodeURIComponent(paymentMethod));
            if (search) params.push('search=' + encodeURIComponent(search));
            
            if (params.length > 0) {
                url += '?' + params.join('&');
            }
            
            // Navigate to the URL
            window.location.href = url;
        });

        $('#printTable').click(function() {
            table.button('.buttons-print').trigger();
        });

        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Sales by Status Chart
        var statusCtx = document.getElementById('salesByStatusChart').getContext('2d');
        var statusChart = new Chart(statusCtx, {
            type: 'pie',
            data: {
                labels: ['مكتمل', 'معلق', 'ملغي'],
                datasets: [{
                    data: [
                        {{ completed_count|default:0 }},
                        {{ pending_count|default:0 }},
                        {{ cancelled_count|default:0 }}
                    ],
                    backgroundColor: [
                        '#1cc88a',
                        '#f6c23e',
                        '#e74a3b'
                    ],
                    hoverBackgroundColor: [
                        '#17a673',
                        '#dda20a',
                        '#be2617'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            font: {
                                family: 'Tajawal'
                            }
                        }
                    }
                }
            }
        });

        // Sales by Payment Method Chart
        var paymentCtx = document.getElementById('salesByPaymentMethodChart').getContext('2d');
        var paymentChart = new Chart(paymentCtx, {
            type: 'doughnut',
            data: {
                labels: ['نقدي', 'بطاقة ائتمان', 'تحويل بنكي', 'شيك', 'آجل'],
                datasets: [{
                    data: [
                        {{ cash_count|default:0 }},
                        {{ card_count|default:0 }},
                        {{ transfer_count|default:0 }},
                        {{ check_count|default:0 }},
                        {{ credit_count|default:0 }}
                    ],
                    backgroundColor: [
                        '#1cc88a',
                        '#4e73df',
                        '#36b9cc',
                        '#858796',
                        '#f6c23e'
                    ],
                    hoverBackgroundColor: [
                        '#17a673',
                        '#2e59d9',
                        '#2c9faf',
                        '#60616f',
                        '#dda20a'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            font: {
                                family: 'Tajawal'
                            }
                        }
                    }
                }
            }
        });
    });
</script>
{% endblock %}
