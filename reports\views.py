from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.db.models import Sum, Count, Avg, F, Q
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from datetime import datetime, timedelta
import csv

# Permission check functions
def can_view_financial_reports(user):
    """Check if user has permission to view financial reports"""
    return user.is_superuser or user.groups.filter(name__in=['Administrators', 'Managers', 'Accountants']).exists()

def can_view_inventory_reports(user):
    """Check if user has permission to view inventory reports"""
    return user.is_superuser or user.groups.filter(name__in=['Administrators', 'Managers', 'Inventory Managers']).exists()

def can_view_sales_reports(user):
    """Check if user has permission to view sales reports"""
    return user.is_superuser or user.groups.filter(name__in=['Administrators', 'Managers', 'Sales Managers']).exists()

def can_view_customer_reports(user):
    """Check if user has permission to view customer reports"""
    return user.is_superuser or user.groups.filter(name__in=['Administrators', 'Managers', 'Sales Managers', 'Customer Service']).exists()

from .models import SavedReport, ScheduledReport, ReportExport
from sales.models import Sale, SaleItem
from inventory.models import Product, ProductMovement
from customers.models import Customer
from finance.models import Expense, Income

@login_required
def index(request):
    saved_reports = SavedReport.objects.filter(Q(created_by=request.user) | Q(is_public=True)).order_by('name')
    scheduled_reports = ScheduledReport.objects.filter(created_by=request.user).order_by('saved_report__name')

    # Handle save report form submission
    if request.method == 'POST':
        action = request.POST.get('action')

        if action == 'save_report':
            report_name = request.POST.get('report_name')
            report_type = request.POST.get('report_type')
            report_description = request.POST.get('report_description')
            is_public = request.POST.get('is_public') == 'on'

            # Get parameters from the current session or request
            parameters = {}
            if report_type == 'sales':
                parameters['start_date'] = request.POST.get('start_date') or request.GET.get('start_date')
                parameters['end_date'] = request.POST.get('end_date') or request.GET.get('end_date')
                parameters['category'] = request.POST.get('category') or request.GET.get('category')
                parameters['product'] = request.POST.get('product') or request.GET.get('product')
                parameters['customer'] = request.POST.get('customer') or request.GET.get('customer')
                parameters['payment_method'] = request.POST.get('payment_method') or request.GET.get('payment_method')
            elif report_type == 'inventory':
                parameters['category'] = request.POST.get('category') or request.GET.get('category')
                parameters['low_stock_only'] = request.POST.get('low_stock_only') == 'on'
                parameters['out_of_stock_only'] = request.POST.get('out_of_stock_only') == 'on'
            elif report_type == 'financial':
                parameters['start_date'] = request.POST.get('start_date') or request.GET.get('start_date')
                parameters['end_date'] = request.POST.get('end_date') or request.GET.get('end_date')
                parameters['expense_category'] = request.POST.get('expense_category') or request.GET.get('expense_category')
                parameters['income_category'] = request.POST.get('income_category') or request.GET.get('income_category')
            elif report_type == 'customers':
                parameters['category'] = request.POST.get('category') or request.GET.get('category')
                parameters['active_only'] = request.POST.get('active_only') == 'on'

            # Create the saved report
            SavedReport.objects.create(
                name=report_name,
                report_type=report_type,
                parameters=parameters,
                description=report_description,
                created_by=request.user,
                is_public=is_public
            )

            messages.success(request, _('تم حفظ التقرير بنجاح'))
            return redirect('reports:index')

        elif action == 'schedule_report':
            saved_report_id = request.POST.get('saved_report')
            frequency = request.POST.get('frequency')
            recipients = request.POST.get('recipients')
            subject = request.POST.get('subject')
            message = request.POST.get('message')

            # Create the scheduled report
            ScheduledReport.objects.create(
                saved_report_id=saved_report_id,
                frequency=frequency,
                recipients=recipients,
                subject=subject,
                message=message,
                created_by=request.user
            )

            messages.success(request, _('تم جدولة التقرير بنجاح'))
            return redirect('reports:index')

    # Get all categories, products, and customers for the forms
    from inventory.models import Category
    from customers.models import Customer

    categories = Category.objects.all().order_by('name')
    products = Product.objects.all().order_by('name')
    customers = Customer.objects.all().order_by('name')

    context = {
        'saved_reports': saved_reports,
        'scheduled_reports': scheduled_reports,
        'categories': categories,
        'products': products,
        'customers': customers,
    }
    return render(request, 'reports/index.html', context)

@login_required
@user_passes_test(can_view_sales_reports)
def sales_report(request):
    # Get date range from request or default to current month
    today = timezone.now().date()
    week_start = today - timedelta(days=today.weekday())
    month_start = today.replace(day=1)
    year_start = today.replace(month=1, day=1)

    start_date_str = request.GET.get('start_date')
    end_date_str = request.GET.get('end_date')
    category_id = request.GET.get('category')
    product_id = request.GET.get('product')
    customer_id = request.GET.get('customer')
    payment_method = request.GET.get('payment_method')
    payment_status = request.GET.get('payment_status')

    if start_date_str and end_date_str:
        try:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
        except ValueError:
            start_date = today.replace(day=1)
            end_date = today
    else:
        start_date = today.replace(day=1)
        end_date = today

    # Get sales data with base filter
    sales_query = Sale.objects.filter(date__date__gte=start_date, date__date__lte=end_date, status='completed')

    # Apply additional filters
    if category_id:
        sales_query = sales_query.filter(items__product__category_id=category_id)
    if product_id:
        sales_query = sales_query.filter(items__product_id=product_id)
    if customer_id:
        sales_query = sales_query.filter(customer_id=customer_id)
    if payment_method:
        sales_query = sales_query.filter(payment_method=payment_method)
    if payment_status:
        if payment_status == 'paid':
            sales_query = sales_query.filter(is_paid=True)
        elif payment_status == 'unpaid':
            sales_query = sales_query.filter(is_paid=False)

    # Make sure we have distinct results
    sales = sales_query.distinct()

    # Calculate totals
    total_sales = sales.count()
    total_amount = sales.aggregate(Sum('total_amount'))['total_amount__sum'] or 0

    # Calculate additional statistics
    total_invoices = sales.count()

    # Calculate profit
    total_profit = 0
    total_cost = 0
    for sale in sales:
        for item in sale.items.all():
            item_cost = item.product.purchase_price * item.quantity
            item_profit = item.subtotal - item_cost
            total_profit += item_profit
            total_cost += item_cost

    profit_margin = (total_profit / total_amount * 100) if total_amount > 0 else 0

    # Calculate products sold
    total_products_sold = SaleItem.objects.filter(sale__in=sales).aggregate(
        total=Sum('quantity')
    )['total'] or 0

    # Payment status statistics
    paid_sales = sales.filter(is_paid=True)
    unpaid_sales = sales.filter(is_paid=False)

    paid_sales_count = paid_sales.count()
    unpaid_sales_count = unpaid_sales.count()
    paid_sales_amount = paid_sales.aggregate(Sum('total_amount'))['total_amount__sum'] or 0
    unpaid_sales_amount = unpaid_sales.aggregate(Sum('total_amount'))['total_amount__sum'] or 0

    # Get top products
    top_products = SaleItem.objects.filter(sale__in=sales).values('product__name').annotate(
        total_quantity=Sum('quantity'),
        total_sales=Sum('subtotal')
    ).order_by('-total_quantity')[:10]

    # Get sales by day
    sales_by_day = sales.values('date__date').annotate(
        count=Count('id'),
        total=Sum('total_amount')
    ).order_by('date__date')

    # Get all categories, products, and customers for the filters
    from inventory.models import Category
    from customers.models import Customer

    categories = Category.objects.all().order_by('name')
    all_products = Product.objects.all().order_by('name')
    all_customers = Customer.objects.all().order_by('name')

    context = {
        'start_date': start_date,
        'end_date': end_date,
        'today': today,
        'week_start': week_start,
        'month_start': month_start,
        'year_start': year_start,
        'total_sales': total_sales,
        'total_amount': total_amount,
        'total_profit': total_profit,
        'profit_margin': profit_margin,
        'total_invoices': total_invoices,
        'total_products_sold': total_products_sold,
        'paid_sales_count': paid_sales_count,
        'unpaid_sales_count': unpaid_sales_count,
        'paid_sales_amount': paid_sales_amount,
        'unpaid_sales_amount': unpaid_sales_amount,
        'top_products': top_products,
        'sales_by_day': sales_by_day,
        'sales': sales,
        'categories': categories,
        'all_products': all_products,
        'all_customers': all_customers,
        'selected_category': category_id,
        'selected_product': product_id,
        'selected_customer': customer_id,
        'selected_payment_method': payment_method,
        'selected_payment_status': payment_status,
    }
    return render(request, 'reports/sales_report.html', context)

@login_required
@user_passes_test(can_view_inventory_reports)
def inventory_report(request):
    # Get inventory data
    products = Product.objects.all().order_by('name')

    # Calculate totals
    total_products = products.count()
    total_value = sum(product.quantity * product.purchase_price for product in products)
    low_stock_count = products.filter(quantity__lte=F('min_quantity')).count()
    out_of_stock_count = products.filter(quantity=0).count()

    # Get recent movements
    recent_movements = ProductMovement.objects.all().order_by('-created_at')[:20]

    context = {
        'products': products,
        'total_products': total_products,
        'total_value': total_value,
        'low_stock_count': low_stock_count,
        'out_of_stock_count': out_of_stock_count,
        'recent_movements': recent_movements,
    }
    return render(request, 'reports/inventory_report.html', context)

@login_required
@user_passes_test(can_view_customer_reports)
def customers_report(request):
    # Get customers data
    customers = Customer.objects.all().order_by('name')

    # Calculate totals
    total_customers = customers.count()

    # Get top customers
    top_customers = customers.annotate(
        purchases_count=Count('sales'),
        total_spent=Sum('sales__total_amount')
    ).order_by('-total_spent')[:10]

    context = {
        'customers': customers,
        'total_customers': total_customers,
        'top_customers': top_customers,
    }
    return render(request, 'reports/customers_report.html', context)

@login_required
@user_passes_test(can_view_financial_reports)
def financial_report(request):
    # Get date range from request or default to current month
    today = timezone.now().date()
    week_start = today - timedelta(days=today.weekday())
    month_start = today.replace(day=1)
    year_start = today.replace(month=1, day=1)

    start_date_str = request.GET.get('start_date')
    end_date_str = request.GET.get('end_date')

    if start_date_str and end_date_str:
        try:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
        except ValueError:
            start_date = today.replace(day=1)
            end_date = today
    else:
        start_date = today.replace(day=1)
        end_date = today

    # Get financial data
    expenses = Expense.objects.filter(date__gte=start_date, date__lte=end_date)
    incomes = Income.objects.filter(date__gte=start_date, date__lte=end_date)
    sales = Sale.objects.filter(date__date__gte=start_date, date__date__lte=end_date, status='completed')

    # Calculate totals
    total_expenses = expenses.aggregate(Sum('amount'))['amount__sum'] or 0
    total_incomes = incomes.aggregate(Sum('amount'))['amount__sum'] or 0
    total_sales = sales.aggregate(Sum('total_amount'))['total_amount__sum'] or 0
    profit = total_sales + total_incomes - total_expenses

    # Get expenses by category
    expenses_by_category = expenses.values('category__name').annotate(
        total=Sum('amount')
    ).order_by('-total')

    # Get incomes by category
    incomes_by_category = incomes.values('category__name').annotate(
        total=Sum('amount')
    ).order_by('-total')

    context = {
        'start_date': start_date,
        'end_date': end_date,
        'today': today,
        'week_start': week_start,
        'month_start': month_start,
        'year_start': year_start,
        'total_expenses': total_expenses,
        'total_incomes': total_incomes,
        'total_sales': total_sales,
        'profit': profit,
        'expenses_by_category': expenses_by_category,
        'incomes_by_category': incomes_by_category,
        'expenses': expenses,
        'incomes': incomes,
    }
    return render(request, 'reports/financial_report.html', context)

@login_required
@user_passes_test(can_view_sales_reports)
def export_sales_report(request):
    # Get parameters from request
    start_date_str = request.GET.get('start_date')
    end_date_str = request.GET.get('end_date')
    category_id = request.GET.get('category')
    product_id = request.GET.get('product')
    customer_id = request.GET.get('customer')
    payment_method = request.GET.get('payment_method')
    export_format = request.GET.get('format', 'csv')

    # Parse dates
    today = timezone.now().date()
    try:
        start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date() if start_date_str else today.replace(day=1)
        end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date() if end_date_str else today
    except ValueError:
        start_date = today.replace(day=1)
        end_date = today

    # Build query
    sales_query = Sale.objects.filter(date__date__gte=start_date, date__date__lte=end_date, status='completed')

    # Apply filters
    if category_id:
        sales_query = sales_query.filter(items__product__category_id=category_id)
    if product_id:
        sales_query = sales_query.filter(items__product_id=product_id)
    if customer_id:
        sales_query = sales_query.filter(customer_id=customer_id)
    if payment_method:
        sales_query = sales_query.filter(payment_method=payment_method)

    # Make sure we have distinct results
    sales = sales_query.distinct()

    # Export based on format
    if export_format == 'csv':
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="sales_report_{start_date}_{end_date}.csv"'

        # Create CSV writer
        writer = csv.writer(response)
        writer.writerow([_('رقم الفاتورة'), _('العميل'), _('التاريخ'), _('المبلغ الفرعي'),
                       _('الضريبة'), _('الإجمالي'), _('طريقة الدفع'), _('الموظف')])

        # Add data rows
        for sale in sales:
            writer.writerow([
                sale.invoice_number,
                sale.customer.name,
                sale.date.strftime('%Y-%m-%d %H:%M'),
                sale.subtotal,
                sale.tax_amount,
                sale.total_amount,
                sale.get_payment_method_display() if hasattr(sale, 'get_payment_method_display') else '',
                sale.employee.get_full_name()
            ])

        return response

    elif export_format == 'excel':
        import xlwt

        response = HttpResponse(content_type='application/ms-excel')
        response['Content-Disposition'] = f'attachment; filename="sales_report_{start_date}_{end_date}.xls"'

        # Create workbook and add sheet
        wb = xlwt.Workbook(encoding='utf-8')
        ws = wb.add_sheet(_('تقرير المبيعات'))

        # Sheet header, first row
        row_num = 0
        font_style = xlwt.XFStyle()
        font_style.font.bold = True

        columns = [_('رقم الفاتورة'), _('العميل'), _('التاريخ'), _('المبلغ الفرعي'),
                  _('الضريبة'), _('الإجمالي'), _('طريقة الدفع'), _('الموظف')]

        for col_num, column_title in enumerate(columns):
            ws.write(row_num, col_num, column_title, font_style)

        # Sheet body, remaining rows
        font_style = xlwt.XFStyle()

        for sale in sales:
            row_num += 1
            ws.write(row_num, 0, sale.invoice_number, font_style)
            ws.write(row_num, 1, sale.customer.name, font_style)
            ws.write(row_num, 2, sale.date.strftime('%Y-%m-%d %H:%M'), font_style)
            ws.write(row_num, 3, float(sale.subtotal), font_style)
            ws.write(row_num, 4, float(sale.tax_amount), font_style)
            ws.write(row_num, 5, float(sale.total_amount), font_style)
            ws.write(row_num, 6, sale.get_payment_method_display() if hasattr(sale, 'get_payment_method_display') else '', font_style)
            ws.write(row_num, 7, sale.employee.get_full_name(), font_style)

        wb.save(response)
        return response

    elif export_format == 'pdf':
        from reportlab.lib import colors
        from reportlab.lib.pagesizes import letter, landscape
        from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
        from reportlab.lib.styles import getSampleStyleSheet
        from io import BytesIO

        # Create the HttpResponse object with PDF headers
        buffer = BytesIO()
        response = HttpResponse(content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename="sales_report_{start_date}_{end_date}.pdf"'

        # Create the PDF object using the BytesIO buffer as its "file"
        doc = SimpleDocTemplate(buffer, pagesize=landscape(letter))

        # Container for the 'Flowable' objects
        elements = []

        # Get styles
        styles = getSampleStyleSheet()
        title_style = styles['Heading1']
        title_style.alignment = 1  # Center alignment

        # Add title
        title = Paragraph(_('تقرير المبيعات'), title_style)
        elements.append(title)
        elements.append(Spacer(1, 12))

        # Add date range
        date_text = f"{_('الفترة')}: {start_date} - {end_date}"
        date_paragraph = Paragraph(date_text, styles['Normal'])
        elements.append(date_paragraph)
        elements.append(Spacer(1, 12))

        # Create table data
        data = [
            [_('رقم الفاتورة'), _('العميل'), _('التاريخ'), _('المبلغ الفرعي'),
             _('الضريبة'), _('الإجمالي'), _('طريقة الدفع'), _('الموظف')]
        ]

        # Add sales data
        for sale in sales:
            data.append([
                sale.invoice_number,
                sale.customer.name,
                sale.date.strftime('%Y-%m-%d %H:%M'),
                f"{float(sale.subtotal):.2f}",
                f"{float(sale.tax_amount):.2f}",
                f"{float(sale.total_amount):.2f}",
                sale.get_payment_method_display() if hasattr(sale, 'get_payment_method_display') else '',
                sale.employee.get_full_name()
            ])

        # Create the table
        table = Table(data)

        # Style the table
        table_style = TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ])

        table.setStyle(table_style)
        elements.append(table)

        # Build the PDF
        doc.build(elements)

        # Get the value of the BytesIO buffer and write it to the response
        pdf = buffer.getvalue()
        buffer.close()
        response.write(pdf)

        return response

    # Default fallback
    return redirect('reports:sales_report')

@login_required
@user_passes_test(can_view_inventory_reports)
def export_inventory_report(request):
    # This is a placeholder for the actual export functionality
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="inventory_report.csv"'

    # Here you would generate the actual CSV content

    return response

@login_required
@user_passes_test(can_view_sales_reports)
def compare_periods(request):
    """API endpoint to compare sales data between two time periods"""
    # Get date parameters from request
    period1_start = request.GET.get('period1_start')
    period1_end = request.GET.get('period1_end')
    period2_start = request.GET.get('period2_start')
    period2_end = request.GET.get('period2_end')
    category_id = request.GET.get('category')
    product_id = request.GET.get('product')
    customer_id = request.GET.get('customer')
    payment_method = request.GET.get('payment_method')
    
    # Parse dates
    try:
        p1_start = datetime.strptime(period1_start, '%Y-%m-%d').date()
        p1_end = datetime.strptime(period1_end, '%Y-%m-%d').date()
        p2_start = datetime.strptime(period2_start, '%Y-%m-%d').date()
        p2_end = datetime.strptime(period2_end, '%Y-%m-%d').date()
    except (ValueError, TypeError):
        return JsonResponse({'error': 'تنسيق التاريخ غير صحيح'}, status=400)
    
    # Build base queries for both periods
    period1_query = Sale.objects.filter(date__date__gte=p1_start, date__date__lte=p1_end, status='completed')
    period2_query = Sale.objects.filter(date__date__gte=p2_start, date__date__lte=p2_end, status='completed')
    
    # Apply additional filters to both queries
    if category_id:
        period1_query = period1_query.filter(items__product__category_id=category_id)
        period2_query = period2_query.filter(items__product__category_id=category_id)
    if product_id:
        period1_query = period1_query.filter(items__product_id=product_id)
        period2_query = period2_query.filter(items__product_id=product_id)
    if customer_id:
        period1_query = period1_query.filter(customer_id=customer_id)
        period2_query = period2_query.filter(customer_id=customer_id)
    if payment_method:
        period1_query = period1_query.filter(payment_method=payment_method)
        period2_query = period2_query.filter(payment_method=payment_method)
    
    # Make sure we have distinct results
    period1_sales = period1_query.distinct()
    period2_sales = period2_query.distinct()
    
    # Calculate metrics for period 1
    period1_count = period1_sales.count()
    period1_total = period1_sales.aggregate(Sum('total_amount'))['total_amount__sum'] or 0
    period1_average = period1_total / period1_count if period1_count > 0 else 0
    
    # Calculate metrics for period 2
    period2_count = period2_sales.count()
    period2_total = period2_sales.aggregate(Sum('total_amount'))['total_amount__sum'] or 0
    period2_average = period2_total / period2_count if period2_count > 0 else 0
    
    # Calculate changes
    count_change = period2_count - period1_count
    count_change_percent = (count_change / period1_count * 100) if period1_count > 0 else 0
    
    total_change = period2_total - period1_total
    total_change_percent = (total_change / period1_total * 100) if period1_total > 0 else 0
    
    average_change = period2_average - period1_average
    average_change_percent = (average_change / period1_average * 100) if period1_average > 0 else 0
    
    # Format numbers for display
    def format_number(num):
        return '{:,.2f}'.format(num).replace(',', ' ')
    
    # Prepare response data
    response_data = {
        'period1': {
            'count': period1_count,
            'total': format_number(period1_total),
            'average': format_number(period1_average)
        },
        'period2': {
            'count': period2_count,
            'total': format_number(period2_total),
            'average': format_number(period2_average)
        },
        'comparison': {
            'count_change': '+' + str(count_change) if count_change > 0 else str(count_change),
            'count_change_percent': '{:+.1f}'.format(count_change_percent),
            'total_change': format_number(total_change),
            'total_change_percent': '{:+.1f}'.format(total_change_percent),
            'average_change': format_number(average_change),
            'average_change_percent': '{:+.1f}'.format(average_change_percent)
        }
    }
    
    return JsonResponse(response_data)
