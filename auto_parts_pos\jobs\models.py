from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth.models import User
from customers.models import Customer
from inventory.models import Product

class JobStatus(models.TextChoices):
    PENDING = 'pending', _('قيد الانتظار')
    IN_PROGRESS = 'in_progress', _('قيد التنفيذ')
    COMPLETED = 'completed', _('مكتمل')
    CANCELLED = 'cancelled', _('ملغي')

class JobPriority(models.TextChoices):
    LOW = 'low', _('منخفضة')
    MEDIUM = 'medium', _('متوسطة')
    HIGH = 'high', _('عالية')
    URGENT = 'urgent', _('عاجلة')

class Job(models.Model):
    """نموذج لإدارة الأعمال والمهام"""

    job_number = models.CharField(_('رقم العمل'), max_length=20, unique=True)
    title = models.CharField(_('عنوان العمل'), max_length=200)
    description = models.TextField(_('وصف العمل'), blank=True, null=True)
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name='jobs', verbose_name=_('العميل'))

    # معلومات السيارة
    car_make = models.CharField(_('ماركة السيارة'), max_length=100)
    car_model = models.CharField(_('موديل السيارة'), max_length=100)
    car_year = models.PositiveIntegerField(_('سنة الصنع'), null=True, blank=True)
    car_vin = models.CharField(_('رقم الهيكل'), max_length=50, blank=True, null=True)
    car_plate = models.CharField(_('رقم اللوحة'), max_length=20, blank=True, null=True)
    car_color = models.CharField(_('لون السيارة'), max_length=50, blank=True, null=True)
    car_mileage = models.PositiveIntegerField(_('عداد الكيلومترات'), null=True, blank=True)

    # معلومات العمل
    status = models.CharField(_('حالة العمل'), max_length=20, choices=JobStatus.choices, default=JobStatus.PENDING)
    priority = models.CharField(_('أولوية العمل'), max_length=20, choices=JobPriority.choices, default=JobPriority.MEDIUM)

    # المسؤولين عن العمل
    assigned_to = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='assigned_jobs', verbose_name=_('المسؤول عن العمل'))
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_jobs', verbose_name=_('تم الإنشاء بواسطة'))

    # التواريخ
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)
    start_date = models.DateTimeField(_('تاريخ البدء'), null=True, blank=True)
    due_date = models.DateTimeField(_('تاريخ الاستحقاق'), null=True, blank=True)
    completed_date = models.DateTimeField(_('تاريخ الإكمال'), null=True, blank=True)

    # المالية
    estimated_cost = models.DecimalField(_('التكلفة التقديرية'), max_digits=10, decimal_places=2, default=0)
    actual_cost = models.DecimalField(_('التكلفة الفعلية'), max_digits=10, decimal_places=2, default=0)

    # ملاحظات إضافية
    notes = models.TextField(_('ملاحظات'), blank=True, null=True)

    class Meta:
        verbose_name = _('عمل')
        verbose_name_plural = _('الأعمال')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.job_number} - {self.title}"

    def save(self, *args, **kwargs):
        # إنشاء رقم العمل تلقائيًا إذا لم يكن موجودًا
        if not self.job_number:
            last_job = Job.objects.order_by('-id').first()
            if last_job:
                last_number = int(last_job.job_number.split('-')[1])
                self.job_number = f"JOB-{last_number + 1:06d}"
            else:
                self.job_number = "JOB-000001"
        super().save(*args, **kwargs)

class JobItem(models.Model):
    """نموذج لعناصر العمل (قطع الغيار والخدمات)"""

    job = models.ForeignKey(Job, on_delete=models.CASCADE, related_name='items', verbose_name=_('العمل'))
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='job_items', verbose_name=_('المنتج'))
    quantity = models.PositiveIntegerField(_('الكمية'), default=1)
    unit_price = models.DecimalField(_('سعر الوحدة'), max_digits=10, decimal_places=2)
    subtotal = models.DecimalField(_('المجموع الفرعي'), max_digits=10, decimal_places=2)
    notes = models.TextField(_('ملاحظات'), blank=True, null=True)

    class Meta:
        verbose_name = _('عنصر العمل')
        verbose_name_plural = _('عناصر العمل')

    def __str__(self):
        return f"{self.product.name} ({self.quantity})"

    def save(self, *args, **kwargs):
        # حساب المجموع الفرعي تلقائيًا
        self.subtotal = self.quantity * self.unit_price
        super().save(*args, **kwargs)

class JobService(models.Model):
    """نموذج للخدمات المقدمة في العمل"""

    job = models.ForeignKey(Job, on_delete=models.CASCADE, related_name='services', verbose_name=_('العمل'))
    name = models.CharField(_('اسم الخدمة'), max_length=200)
    description = models.TextField(_('وصف الخدمة'), blank=True, null=True)
    cost = models.DecimalField(_('تكلفة الخدمة'), max_digits=10, decimal_places=2)
    technician = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='job_services', verbose_name=_('الفني'))

    class Meta:
        verbose_name = _('خدمة العمل')
        verbose_name_plural = _('خدمات العمل')

    def __str__(self):
        return self.name

class JobAttachment(models.Model):
    """نموذج لمرفقات العمل (صور، ملفات، إلخ)"""

    job = models.ForeignKey(Job, on_delete=models.CASCADE, related_name='attachments', verbose_name=_('العمل'))
    file = models.FileField(_('الملف'), upload_to='job_attachments/')
    name = models.CharField(_('اسم الملف'), max_length=200)
    description = models.TextField(_('وصف الملف'), blank=True, null=True)
    uploaded_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='job_attachments', verbose_name=_('تم الرفع بواسطة'))
    uploaded_at = models.DateTimeField(_('تاريخ الرفع'), auto_now_add=True)

    class Meta:
        verbose_name = _('مرفق العمل')
        verbose_name_plural = _('مرفقات العمل')

    def __str__(self):
        return self.name

class JobComment(models.Model):
    """نموذج لتعليقات العمل"""

    job = models.ForeignKey(Job, on_delete=models.CASCADE, related_name='comments', verbose_name=_('العمل'))
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='job_comments', verbose_name=_('المستخدم'))
    comment = models.TextField(_('التعليق'))
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)

    class Meta:
        verbose_name = _('تعليق العمل')
        verbose_name_plural = _('تعليقات العمل')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.username} - {self.created_at}"

# نماذج فرق العمل

class Department(models.Model):
    """نموذج للأقسام في الشركة"""

    name = models.CharField(_('اسم القسم'), max_length=100)
    description = models.TextField(_('وصف القسم'), blank=True, null=True)
    manager = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='managed_departments', verbose_name=_('مدير القسم'))
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('قسم')
        verbose_name_plural = _('الأقسام')
        ordering = ['name']

    def __str__(self):
        return self.name

class Team(models.Model):
    """نموذج لفرق العمل"""

    name = models.CharField(_('اسم الفريق'), max_length=100)
    description = models.TextField(_('وصف الفريق'), blank=True, null=True)
    department = models.ForeignKey(Department, on_delete=models.CASCADE, related_name='teams', verbose_name=_('القسم'))
    leader = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='led_teams', verbose_name=_('قائد الفريق'))
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('فريق')
        verbose_name_plural = _('الفرق')
        ordering = ['name']

    def __str__(self):
        return self.name

class TeamMember(models.Model):
    """نموذج لأعضاء الفريق"""

    ROLE_CHOICES = (
        ('technician', _('فني')),
        ('assistant', _('مساعد')),
        ('supervisor', _('مشرف')),
        ('manager', _('مدير')),
        ('other', _('أخرى')),
    )

    team = models.ForeignKey(Team, on_delete=models.CASCADE, related_name='members', verbose_name=_('الفريق'))
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='team_memberships', verbose_name=_('المستخدم'))
    role = models.CharField(_('الدور'), max_length=20, choices=ROLE_CHOICES, default='technician')
    is_active = models.BooleanField(_('نشط'), default=True)
    joined_at = models.DateTimeField(_('تاريخ الانضمام'), auto_now_add=True)
    notes = models.TextField(_('ملاحظات'), blank=True, null=True)

    class Meta:
        verbose_name = _('عضو فريق')
        verbose_name_plural = _('أعضاء الفريق')
        unique_together = ('team', 'user')
        ordering = ['team', 'user__username']

    def __str__(self):
        return f"{self.user.get_full_name() or self.user.username} - {self.team.name}"

class Skill(models.Model):
    """نموذج للمهارات"""

    name = models.CharField(_('اسم المهارة'), max_length=100)
    description = models.TextField(_('وصف المهارة'), blank=True, null=True)

    class Meta:
        verbose_name = _('مهارة')
        verbose_name_plural = _('المهارات')
        ordering = ['name']

    def __str__(self):
        return self.name

class EmployeeSkill(models.Model):
    """نموذج لمهارات الموظف"""

    PROFICIENCY_CHOICES = (
        (1, _('مبتدئ')),
        (2, _('متوسط')),
        (3, _('جيد')),
        (4, _('متقدم')),
        (5, _('خبير')),
    )

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='skills', verbose_name=_('الموظف'))
    skill = models.ForeignKey(Skill, on_delete=models.CASCADE, related_name='employees', verbose_name=_('المهارة'))
    proficiency = models.PositiveSmallIntegerField(_('مستوى الإتقان'), choices=PROFICIENCY_CHOICES, default=3)
    years_experience = models.PositiveSmallIntegerField(_('سنوات الخبرة'), default=0)
    notes = models.TextField(_('ملاحظات'), blank=True, null=True)

    class Meta:
        verbose_name = _('مهارة الموظف')
        verbose_name_plural = _('مهارات الموظفين')
        unique_together = ('user', 'skill')
        ordering = ['user', '-proficiency']

    def __str__(self):
        return f"{self.user.get_full_name() or self.user.username} - {self.skill.name} ({self.get_proficiency_display()})"

class TeamAssignment(models.Model):
    """نموذج لتخصيص الفرق للأعمال"""

    job = models.ForeignKey(Job, on_delete=models.CASCADE, related_name='team_assignments', verbose_name=_('العمل'))
    team = models.ForeignKey(Team, on_delete=models.CASCADE, related_name='job_assignments', verbose_name=_('الفريق'))
    assigned_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='team_assignments_made', verbose_name=_('تم التخصيص بواسطة'))
    assigned_at = models.DateTimeField(_('تاريخ التخصيص'), auto_now_add=True)
    notes = models.TextField(_('ملاحظات'), blank=True, null=True)

    class Meta:
        verbose_name = _('تخصيص فريق')
        verbose_name_plural = _('تخصيصات الفرق')
        unique_together = ('job', 'team')
        ordering = ['-assigned_at']

    def __str__(self):
        return f"{self.team.name} - {self.job.job_number}"