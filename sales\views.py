from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.db import transaction
from django.http import HttpResponse, JsonResponse
from django.utils import timezone
from django.db.models import Sum, Count, Q, F, ExpressionWrapper, DecimalField, DurationField, fields
from django.core.mail import EmailMessage
from django.views.decorators.http import require_POST
from django.views.decorators.csrf import csrf_exempt
import datetime
import uuid
import json
import xlwt
from io import BytesIO
from reportlab.lib import colors
from reportlab.lib.pagesizes import letter, landscape, A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.lib.enums import TA_RIGHT

from .models import Sale, SaleItem, Payment
from customers.models import Customer
from inventory.models import Product, ProductMovement, Category
from settings_app.models import TaxSetting

@login_required
def export_sales_excel(request):
    """تصدير المبيعات إلى ملف Excel مع دعم الفلترة"""
    # إنشاء استجابة HTTP مع نوع محتوى مناسب
    response = HttpResponse(content_type='application/vnd.ms-excel; charset=utf-8')
    response['Content-Disposition'] = f'attachment; filename="sales_export_{datetime.datetime.now().strftime("%Y%m%d_%H%M%S")}.xls"'

    # إنشاء ملف Excel
    wb = xlwt.Workbook(encoding='utf-8')
    ws = wb.add_sheet('المبيعات')

    # أنماط الخلايا
    header_style = xlwt.XFStyle()
    header_style.font.bold = True
    header_style.alignment.wrap = 1
    header_style.alignment.horz = xlwt.Alignment.HORZ_CENTER
    header_style.pattern.pattern = xlwt.Pattern.SOLID_PATTERN
    header_style.pattern.pattern_fore_colour = xlwt.Style.colour_map['gray25']

    cell_style = xlwt.XFStyle()
    cell_style.alignment.horz = xlwt.Alignment.HORZ_CENTER

    date_style = xlwt.XFStyle()
    date_style.alignment.horz = xlwt.Alignment.HORZ_CENTER
    date_style.num_format_str = 'yyyy-mm-dd hh:mm'

    # كتابة رأس الجدول
    headers = [
        _('رقم الفاتورة'), _('العميل'), _('التاريخ'), _('المبلغ الإجمالي'),
        _('طريقة الدفع'), _('الحالة')
    ]

    for col_num, header in enumerate(headers):
        ws.write(0, col_num, str(header), header_style)
        ws.col(col_num).width = 256 * 20  # عرض العمود

    # تطبيق الفلاتر نفسها المستخدمة في صفحة المبيعات
    # Base queryset
    sales_queryset = Sale.objects.all()

    # Apply filters
    date_range = request.GET.get('date_range')
    customer_id = request.GET.get('customer')
    status = request.GET.get('status')
    payment_method = request.GET.get('payment_method')
    search = request.GET.get('search')

    # Date range filter
    if date_range:
        try:
            start_date, end_date = date_range.split(' - ')
            start_date = datetime.datetime.strptime(start_date, '%Y/%m/%d').date()
            end_date = datetime.datetime.strptime(end_date, '%Y/%m/%d').date() + timedelta(days=1)
            # Add one day to end_date to include the end date in the results
            end_date = end_date + datetime.timedelta(days=1)
            sales_queryset = sales_queryset.filter(date__gte=start_date, date__lt=end_date)
        except ValueError:
            pass

    # Customer filter
    if customer_id:
        sales_queryset = sales_queryset.filter(customer_id=customer_id)

    # Status filter
    if status:
        sales_queryset = sales_queryset.filter(status=status)

    # Payment method filter
    if payment_method:
        sales_queryset = sales_queryset.filter(payment_method=payment_method)

    # Search filter
    if search:
        sales_queryset = sales_queryset.filter(
            Q(invoice_number__icontains=search) |
            Q(customer__name__icontains=search)
        )

    # Get sales with applied filters
    sales = sales_queryset.order_by('-date')

    # كتابة بيانات المبيعات
    for row_num, sale in enumerate(sales, 1):
        ws.write(row_num, 0, sale.invoice_number, cell_style)
        ws.write(row_num, 1, sale.customer.name, cell_style)
        ws.write(row_num, 2, sale.date.strftime('%Y-%m-%d %H:%M'), date_style)
        ws.write(row_num, 3, float(sale.total_amount), cell_style)
        ws.write(row_num, 4, sale.get_payment_method_display(), cell_style)
        ws.write(row_num, 5, sale.get_status_display(), cell_style)

    wb.save(response)
    return response

@login_required
def export_sales_pdf(request):
    """تصدير المبيعات إلى ملف PDF مع دعم الفلترة"""
    # إنشاء استجابة HTTP مع نوع محتوى مناسب
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="sales_export_{datetime.datetime.now().strftime("%Y%m%d_%H%M%S")}.pdf"'

    # تسجيل الخط العربي
    try:
        import os
        font_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'static', 'fonts', 'NotoSansArabic-Regular.ttf')
        pdfmetrics.registerFont(TTFont('Arabic', font_path))
    except Exception as e:
        print(f"خطأ في تحميل الخط العربي: {e}")
        # إذا لم يتم العثور على الخط، استخدم الخط الافتراضي
        pass

    # إنشاء ملف PDF
    buffer = BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=A4, rightMargin=30, leftMargin=30, topMargin=30, bottomMargin=30)

    # قائمة العناصر التي سيتم إضافتها إلى المستند
    elements = []

    # إنشاء أنماط النصوص
    styles = getSampleStyleSheet()

    # إنشاء نمط للنصوص العربية
    arabic_style = ParagraphStyle(
        'ArabicStyle',
        parent=styles['Normal'],
        alignment=TA_RIGHT,
        fontName='Arabic' if 'Arabic' in pdfmetrics.getRegisteredFontNames() else 'Helvetica',
        fontSize=12,
        leading=14,
    )

    # إضافة عنوان للتقرير
    title = Paragraph(_("تقرير المبيعات"), arabic_style)
    elements.append(title)
    elements.append(Spacer(1, 12))
    
    # تطبيق الفلاتر نفسها المستخدمة في صفحة المبيعات
    # Base queryset
    sales_queryset = Sale.objects.all()

    # Apply filters
    date_range = request.GET.get('date_range')
    customer_id = request.GET.get('customer')
    status = request.GET.get('status')
    payment_method = request.GET.get('payment_method')
    search = request.GET.get('search')

    # Date range filter
    if date_range:
        try:
            start_date, end_date = date_range.split(' - ')
            start_date = datetime.datetime.strptime(start_date, '%Y/%m/%d').date()
            end_date = datetime.datetime.strptime(end_date, '%Y/%m/%d').date()
            # Add one day to end_date to include the end date in the results
            end_date = end_date + datetime.timedelta(days=1)
            sales_queryset = sales_queryset.filter(date__gte=start_date, date__lt=end_date)
        except ValueError:
            pass

    # Customer filter
    if customer_id:
        sales_queryset = sales_queryset.filter(customer_id=customer_id)

    # Status filter
    if status:
        sales_queryset = sales_queryset.filter(status=status)

    # Payment method filter
    if payment_method:
        sales_queryset = sales_queryset.filter(payment_method=payment_method)

    # Search filter
    if search:
        sales_queryset = sales_queryset.filter(
            Q(invoice_number__icontains=search) |
            Q(customer__name__icontains=search)
        )

    # Get sales with applied filters
    sales = sales_queryset.order_by('-date')

    # إضافة معلومات الفلترة إلى التقرير إذا تم تطبيق أي فلتر
    if date_range or customer_id or status or payment_method or search:
        filter_info = Paragraph(_("تم تطبيق الفلاتر على هذا التقرير"), arabic_style)
        elements.append(filter_info)
        elements.append(Spacer(1, 12))

    # إنشاء بيانات الجدول
    data = [
        [_('رقم الفاتورة'), _('العميل'), _('التاريخ'), _('المبلغ الإجمالي'), _('طريقة الدفع'), _('الحالة')]
    ]

    for sale in sales:
        data.append([
            sale.invoice_number,
            sale.customer.name,
            sale.date.strftime('%Y-%m-%d %H:%M'),
            f"{float(sale.total_amount)} د.م",
            sale.get_payment_method_display(),
            sale.get_status_display()
        ])

    # إنشاء الجدول
    table = Table(data, repeatRows=1)

    # تنسيق الجدول
    table_style = TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 12),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
    ])

    table.setStyle(table_style)
    elements.append(table)

    # بناء المستند
    doc.build(elements)

    # الحصول على قيمة البافر وكتابتها في الاستجابة
    pdf = buffer.getvalue()
    buffer.close()
    response.write(pdf)

    return response

@login_required
def index(request):
    # Base queryset
    sales_queryset = Sale.objects.all()

    # Apply filters
    date_range = request.GET.get('date_range')
    customer_id = request.GET.get('customer')
    status = request.GET.get('status')
    payment_method = request.GET.get('payment_method')
    search = request.GET.get('search')

    # Date range filter
    if date_range:
        try:
            start_date, end_date = date_range.split(' - ')
            start_date = datetime.datetime.strptime(start_date, '%Y/%m/%d').date()
            end_date = datetime.datetime.strptime(end_date, '%Y/%m/%d').date()
            # Add one day to end_date to include the end date in the results
            end_date = end_date + datetime.timedelta(days=1)
            sales_queryset = sales_queryset.filter(date__gte=start_date, date__lt=end_date)
        except ValueError:
            pass

    # Customer filter
    if customer_id:
        sales_queryset = sales_queryset.filter(customer_id=customer_id)

    # Status filter
    if status:
        sales_queryset = sales_queryset.filter(status=status)

    # Payment method filter
    if payment_method:
        sales_queryset = sales_queryset.filter(payment_method=payment_method)

    # Search filter
    if search:
        sales_queryset = sales_queryset.filter(
            Q(invoice_number__icontains=search) |
            Q(customer__name__icontains=search)
        )

    # Get sales with applied filters
    sales = sales_queryset.order_by('-date')

    # Get all customers for filter dropdown
    customers = Customer.objects.filter(is_active=True).order_by('name')

    # Get statistics for charts
    completed_count = sales_queryset.filter(status='completed').count()
    pending_count = sales_queryset.filter(status='pending').count()
    cancelled_count = sales_queryset.filter(status='cancelled').count()

    cash_count = sales_queryset.filter(payment_method='cash').count()
    card_count = sales_queryset.filter(payment_method='card').count()
    transfer_count = sales_queryset.filter(payment_method='transfer').count()
    check_count = sales_queryset.filter(payment_method='check').count()
    credit_count = sales_queryset.filter(payment_method='credit').count()

    context = {
        'sales': sales,
        'customers': customers,
        'completed_count': completed_count,
        'pending_count': pending_count,
        'cancelled_count': cancelled_count,
        'cash_count': cash_count,
        'card_count': card_count,
        'transfer_count': transfer_count,
        'check_count': check_count,
        'credit_count': credit_count,
    }
    return render(request, 'sales/index.html', context)

@login_required
def new_sale(request):
    customers = Customer.objects.filter(is_active=True).order_by('name')
    # تحميل جميع المنتجات النشطة بغض النظر عن الكمية
    products = Product.objects.filter(is_active=True).order_by('name')
    today = timezone.now().date()

    if request.method == 'POST':
        customer_id = request.POST.get('customer')
        date_str = request.POST.get('date')
        payment_method = request.POST.get('payment_method')
        status = request.POST.get('status')
        notes = request.POST.get('notes')
        discount = float(request.POST.get('discount') or 0)
        amount_paid = float(request.POST.get('amount_paid') or 0)
        confirm_sale = request.POST.get('confirm_sale') == 'true'

        # Get product data from form
        product_ids = request.POST.getlist('product_ids[]')
        quantities = request.POST.getlist('quantities[]')
        unit_prices = request.POST.getlist('unit_prices[]')

        # Validate form data
        if not customer_id or not date_str or not product_ids:
            messages.error(request, _('يرجى ملء جميع الحقول المطلوبة'))
            return redirect('sales:new_sale')

        # التحقق من تأكيد البيع (اختياري الآن - يتم التحقق فقط إذا تم النقر على زر تأكيد البيع)

        try:
            # Parse date
            date = datetime.datetime.strptime(date_str, '%Y-%m-%d').date()

            # Get customer
            customer = Customer.objects.get(id=customer_id)

            # Calculate subtotal
            subtotal = 0
            for i in range(len(product_ids)):
                subtotal += float(quantities[i]) * float(unit_prices[i])

            # Calculate tax
            tax_setting = TaxSetting.objects.filter(is_enabled=True).first()
            tax_rate = float(tax_setting.rate) if tax_setting else 15.0  # Use tax rate from settings or default to 15%
            tax_amount = subtotal * (tax_rate / 100)

            # Calculate total
            total_amount = subtotal + tax_amount - discount

            # Create sale with transaction to ensure atomicity
            with transaction.atomic():
                # Create sale
                sale = Sale.objects.create(
                    invoice_number=f"INV-{uuid.uuid4().hex[:8].upper()}",
                    customer=customer,
                    employee=request.user,
                    date=date,
                    subtotal=subtotal,
                    tax_rate=tax_rate,
                    tax_amount=tax_amount,
                    discount=discount,
                    total_amount=total_amount,
                    payment_method=payment_method,
                    status=status,
                    notes=notes
                )

                # Create sale items and update inventory
                for i in range(len(product_ids)):
                    product = Product.objects.get(id=product_ids[i])
                    quantity = int(quantities[i])
                    unit_price = float(unit_prices[i])

                    # Check if quantity is available
                    if product.quantity < quantity:
                        raise ValueError(f'الكمية المطلوبة من {product.name} غير متوفرة. المتاح: {product.quantity}')

                    # إنشاء عنصر البيع
                    # إذا كان تأكيد البيع مفعل، سيتم تحديث المخزون تلقائياً
                    # وإلا سيتم فقط حفظ العنصر بدون تحديث المخزون
                    SaleItem.objects.create(
                        sale=sale,
                        product=product,
                        quantity=quantity,
                        unit_price=unit_price,
                        subtotal=quantity * unit_price
                    )

                    # إذا كان تأكيد البيع مفعل، نقوم بتحديث المخزون يدوياً
                    if confirm_sale:
                        # تحديث المخزون يدوياً
                        product.quantity -= quantity
                        product.save()

                        # إنشاء سجل حركة المنتج
                        ProductMovement.objects.create(
                            product=product,
                            movement_type='out',
                            quantity=quantity,
                            reference=f"Sale #{sale.invoice_number}"
                        )

                # Create payment if amount_paid > 0
                if amount_paid > 0:
                    Payment.objects.create(
                        sale=sale,
                        amount=amount_paid,
                        payment_method=payment_method,
                        reference=f"Initial payment for {sale.invoice_number}"
                    )

            messages.success(request, _('تم إنشاء البيع بنجاح'))

            # Check if print after save
            if 'print_after_save' in request.POST:
                return redirect('sales:invoice', sale_id=sale.id)

            return redirect('sales:view_sale', sale_id=sale.id)

        except Customer.DoesNotExist:
            messages.error(request, _('العميل غير موجود'))
        except Product.DoesNotExist:
            messages.error(request, _('أحد المنتجات غير موجود'))
        except ValueError as e:
            messages.error(request, str(e))
        except Exception as e:
            messages.error(request, _('حدث خطأ أثناء إنشاء البيع: ') + str(e))

    context = {
        'customers': customers,
        'products': products,
        'today': today,
    }
    return render(request, 'sales/new_sale.html', context)

@login_required
def view_sale(request, sale_id):
    sale = get_object_or_404(Sale.objects.prefetch_related('items__product'), id=sale_id)
    items = sale.items.all()
    payments = sale.payments.all()
    today = timezone.now().date()

    # Calculate total paid and remaining amount
    total_paid = payments.aggregate(Sum('amount'))['amount__sum'] or 0
    remaining_amount = sale.total_amount - total_paid

    context = {
        'sale': sale,
        'items': items,
        'payments': payments,
        'total_paid': total_paid,
        'remaining_amount': remaining_amount,
        'today': today,
    }
    return render(request, 'sales/view_sale.html', context)

@login_required
def edit_sale(request, sale_id):
    sale = get_object_or_404(Sale, id=sale_id)

    # Check if sale is cancelled
    if sale.status == 'cancelled':
        messages.error(request, _('لا يمكن تعديل بيع ملغي'))
        return redirect('sales:view_sale', sale_id=sale.id)

    items = sale.items.all()
    customers = Customer.objects.filter(is_active=True).order_by('name')
    products = Product.objects.filter(is_active=True).order_by('name')
    payments = sale.payments.all()

    # Calculate total paid and remaining amount
    total_paid = payments.aggregate(Sum('amount'))['amount__sum'] or 0
    remaining_amount = sale.total_amount - total_paid

    if request.method == 'POST':
        customer_id = request.POST.get('customer')
        date_str = request.POST.get('date')
        payment_method = request.POST.get('payment_method')
        status = request.POST.get('status')
        notes = request.POST.get('notes')
        discount = float(request.POST.get('discount') or 0)
        amount_paid = float(request.POST.get('amount_paid') or 0)

        # Get product data from form
        product_ids = request.POST.getlist('product_ids[]')
        item_ids = request.POST.getlist('item_ids[]')
        quantities = request.POST.getlist('quantities[]')
        unit_prices = request.POST.getlist('unit_prices[]')

        # Validate form data
        if not customer_id or not date_str or not product_ids:
            messages.error(request, _('يرجى ملء جميع الحقول المطلوبة'))
            return redirect('sales:edit_sale', sale_id=sale.id)

        try:
            # Parse date
            date = datetime.datetime.strptime(date_str, '%Y-%m-%d').date()

            # Get customer
            customer = Customer.objects.get(id=customer_id)

            # Calculate subtotal
            subtotal = 0
            for i in range(len(product_ids)):
                subtotal += float(quantities[i]) * float(unit_prices[i])

            # Calculate tax
            tax_setting = TaxSetting.objects.filter(is_enabled=True).first()
            tax_rate = float(tax_setting.rate) if tax_setting else 15.0  # Use tax rate from settings or default to 15%
            tax_amount = subtotal * (tax_rate / 100)

            # Calculate total
            total_amount = subtotal + tax_amount - discount

            # Update sale with transaction to ensure atomicity
            with transaction.atomic():
                # Store original items for inventory adjustment
                original_items = {item.id: item for item in items}

                # Update sale
                sale.customer = customer
                sale.date = date
                sale.subtotal = subtotal
                sale.tax_rate = tax_rate
                sale.tax_amount = tax_amount
                sale.discount = discount
                sale.total_amount = total_amount
                sale.payment_method = payment_method
                sale.status = status
                sale.notes = notes
                sale.save()

                # Track items to keep
                kept_item_ids = []

                # Update or create sale items
                for i in range(len(product_ids)):
                    product = Product.objects.get(id=product_ids[i])
                    quantity = int(quantities[i])
                    unit_price = float(unit_prices[i])
                    item_id = item_ids[i] if item_ids[i] else None

                    if item_id and item_id.isdigit():
                        # Existing item - update
                        item_id = int(item_id)
                        kept_item_ids.append(item_id)
                        item = SaleItem.objects.get(id=item_id)

                        # Adjust inventory for quantity change
                        quantity_diff = quantity - item.quantity
                        if quantity_diff != 0:
                            # Check if increased quantity is available
                            if quantity_diff > 0 and product.quantity < quantity_diff:
                                raise ValueError(f'الكمية المطلوبة من {product.name} غير متوفرة. المتاح: {product.quantity}')

                            # Update product quantity
                            product.quantity -= quantity_diff
                            product.save()

                            # Create product movement record if quantity changed
                            if quantity_diff > 0:
                                # Additional items taken from inventory
                                ProductMovement.objects.create(
                                    product=product,
                                    movement_type='out',
                                    quantity=quantity_diff,
                                    reference=f"Sale #{sale.invoice_number} (Edit)"
                                )
                            elif quantity_diff < 0:
                                # Items returned to inventory
                                ProductMovement.objects.create(
                                    product=product,
                                    movement_type='in',
                                    quantity=abs(quantity_diff),
                                    reference=f"Sale #{sale.invoice_number} (Edit Return)"
                                )

                        # Update item
                        item.quantity = quantity
                        item.unit_price = unit_price
                        item.subtotal = quantity * unit_price
                        item.save()
                    else:
                        # New item - create
                        # Check if quantity is available
                        if product.quantity < quantity:
                            raise ValueError(f'الكمية المطلوبة من {product.name} غير متوفرة. المتاح: {product.quantity}')

                        # Create sale item
                        # ملاحظة: سيتم تحديث كمية المنتج تلقائياً في طريقة save لنموذج SaleItem
                        new_item = SaleItem.objects.create(
                            sale=sale,
                            product=product,
                            quantity=quantity,
                            unit_price=unit_price,
                            subtotal=quantity * unit_price
                        )
                        kept_item_ids.append(new_item.id)

                # Delete removed items and return quantities to inventory
                for item in items:
                    if item.id not in kept_item_ids:
                        # Return quantity to inventory
                        product = item.product
                        product.quantity += item.quantity
                        product.save()

                        # Create product movement record
                        ProductMovement.objects.create(
                            product=product,
                            movement_type='in',
                            quantity=item.quantity,
                            reference=f"Sale #{sale.invoice_number} (Edit Delete)"
                        )

                        # Delete item
                        item.delete()

                # Update payment if amount changed
                if amount_paid != total_paid:
                    # If payment exists, update it
                    if payments.exists():
                        payment = payments.first()
                        payment.amount = amount_paid
                        payment.payment_method = payment_method
                        payment.save()
                    # If no payment exists but amount_paid > 0, create new payment
                    elif amount_paid > 0:
                        Payment.objects.create(
                            sale=sale,
                            amount=amount_paid,
                            payment_method=payment_method,
                            reference=f"Payment update for {sale.invoice_number}"
                        )

            messages.success(request, _('تم تحديث البيع بنجاح'))
            return redirect('sales:view_sale', sale_id=sale.id)

        except Customer.DoesNotExist:
            messages.error(request, _('العميل غير موجود'))
        except Product.DoesNotExist:
            messages.error(request, _('أحد المنتجات غير موجود'))
        except SaleItem.DoesNotExist:
            messages.error(request, _('أحد عناصر البيع غير موجود'))
        except ValueError as e:
            messages.error(request, str(e))
        except Exception as e:
            messages.error(request, _('حدث خطأ أثناء تحديث البيع: ') + str(e))

    context = {
        'sale': sale,
        'items': items,
        'customers': customers,
        'products': products,
        'total_paid': total_paid,
        'remaining_amount': remaining_amount,
    }
    return render(request, 'sales/edit_sale.html', context)

@login_required
def delete_sale(request, sale_id):
    sale = get_object_or_404(Sale, id=sale_id)

    if request.method == 'POST':
        with transaction.atomic():
            # Restore product quantities
            for item in sale.items.all():
                item.product.quantity += item.quantity
                item.product.save()

                # Create product movement record for restored items
                from inventory.models import ProductMovement
                ProductMovement.objects.create(
                    product=item.product,
                    movement_type='in',
                    quantity=item.quantity,
                    reference=f"Deleted Sale #{sale.invoice_number}",
                    notes=f"استعادة المنتج من البيع المحذوف رقم {sale.invoice_number}"
                )

            # Delete the sale
            sale.delete()

        messages.success(request, _('تم حذف المبيعات بنجاح'))
        return redirect('sales:index')

    context = {
        'sale': sale,
    }
    return render(request, 'sales/delete_sale.html', context)

@login_required
def generate_invoice_pdf(request, sale_id):
    """Generate and return invoice PDF"""
    sale = get_object_or_404(Sale, id=sale_id)
    items = sale.items.all()
    
    # Get invoice settings
    from settings_app.models import InvoiceSetting, TaxSetting, CompanyInfo
    invoice_settings = InvoiceSetting.objects.first()
    tax = TaxSetting.objects.filter(is_enabled=True).first()
    company_info = CompanyInfo.objects.first()
    
    # Render HTML template
    html_string = render_to_string('sales/invoice_pdf.html', {
        'sale': sale,
        'items': items,
        'invoice_settings': invoice_settings,
        'tax': tax,
        'company_info': company_info,
    })
    
    # Create PDF
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="invoice_{sale.invoice_number}.pdf"'
    
    # Generate PDF
    pisa_status = pisa.CreatePDF(html_string, dest=response)
    if pisa_status.err:
        return HttpResponse('حدث خطأ أثناء إنشاء ملف PDF')
    return response

from django.urls import reverse

def invoice(request, sale_id):
    sale = get_object_or_404(Sale.objects.prefetch_related('payments'), id=sale_id)
    items = sale.items.all()
    payments = sale.payments.all()
    total_paid = payments.aggregate(Sum('amount'))['amount__sum'] or 0
    remaining_amount = sale.total_amount - total_paid

    # Get invoice settings
    from settings_app.models import InvoiceSetting, TaxSetting, CompanyInfo
    invoice_settings = InvoiceSetting.objects.first()
    tax = TaxSetting.objects.filter(is_enabled=True).first()
    company_info = CompanyInfo.objects.first()

    # إذا لم تكن إعدادات الفاتورة موجودة، قم بإنشائها
    if not invoice_settings:
        invoice_settings = InvoiceSetting.objects.create()

    # Generate WhatsApp message text
    pdf_url = request.build_absolute_uri(reverse('sales:invoice_pdf', args=[sale.id]))
    whatsapp_text = (
        f"فاتورة مبيعات رقم: {sale.invoice_number}\n"
        f"العميل: {sale.customer.name}\n"
        f"التاريخ: {sale.date.strftime('%Y-%m-%d')}\n"
        f"المبلغ الإجمالي: {sale.total_amount} درهم\n"
        f"المبلغ المدفوع: {total_paid} درهم\n"
        f"المبلغ المتبقي: {remaining_amount} درهم\n\n"
        f"رابط تحميل الفاتورة: {pdf_url}"
    )

    context = {
        'sale': sale,
        'items': items,
        'payments': payments,
        'total_paid': total_paid,
        'remaining_amount': remaining_amount,
        'invoice_settings': invoice_settings,
        'tax': tax,
        'company_info': company_info,
        'show_settings_link': True,
        'whatsapp_text': whatsapp_text,
        'pdf_url': pdf_url,
    }
    return render(request, 'sales/invoice.html', context)

@login_required
def email_invoice(request, sale_id):
    sale = get_object_or_404(Sale, id=sale_id)

    if request.method == 'POST':
        email_to = request.POST.get('email_to')
        email_subject = request.POST.get('email_subject')
        email_message = request.POST.get('email_message')

        if not email_to:
            messages.error(request, _('يرجى تحديد عنوان البريد الإلكتروني'))
            return redirect('sales:view_sale', sale_id=sale.id)

        try:
            # Generate PDF invoice (this would be implemented with a PDF library)
            # For now, we'll just send a message without attachment
            email = EmailMessage(
                subject=email_subject,
                body=email_message,
                from_email='<EMAIL>',
                to=[email_to],
            )
            email.send(fail_silently=False)

            messages.success(request, _('تم إرسال الفاتورة بنجاح'))
        except Exception as e:
            messages.error(request, _('حدث خطأ أثناء إرسال الفاتورة: ') + str(e))

@login_required
def add_payment(request, sale_id):
    """إضافة دفعة جديدة لبيع موجود"""
    try:
        sale = Sale.objects.get(id=sale_id)

        if request.method == 'POST':
            amount = float(request.POST.get('amount') or 0)

            if amount <= 0:
                messages.error(request, _('يجب أن يكون المبلغ أكبر من صفر'))
                return redirect('sales:view_sale', sale_id=sale_id)

            # إنشاء دفعة جديدة
            Payment.objects.create(
                sale=sale,
                amount=amount
            )

            messages.success(request, _('تمت إضافة الدفعة بنجاح'))
            return redirect('sales:view_sale', sale_id=sale_id)
        else:
            # إذا كانت الطلب GET، نعرض صفحة إضافة دفعة
            return render(request, 'sales/add_payment.html', {'sale': sale})

    except Sale.DoesNotExist:
        messages.error(request, _('البيع غير موجود'))
        return redirect('sales:index')
    except Exception as e:
        messages.error(request, _('حدث خطأ أثناء إضافة الدفعة: ') + str(e))
        return redirect('sales:view_sale', sale_id=sale_id)

@login_required
@require_POST
@csrf_exempt
def check_stock(request):
    """التحقق من توفر المخزون للمنتجات باستخدام AJAX"""
    try:
        data = json.loads(request.body)
        product_id = data.get('product_id')
        quantity = int(data.get('quantity', 1))

        if not product_id:
            return JsonResponse({'success': False, 'error': 'معرف المنتج غير محدد'}, status=400)

        product = Product.objects.get(id=product_id)

        # التحقق من توفر المخزون
        if product.quantity < quantity:
            return JsonResponse({
                'success': False,
                'error': f'الكمية المطلوبة غير متوفرة. المتاح: {product.quantity}',
                'available': product.quantity
            })

        # التحقق من وجود عرض ترويجي
        price = product.current_price
        has_promotion = product.has_active_promotion

        return JsonResponse({
            'success': True,
            'product': {
                'id': product.id,
                'name': product.name,
                'code': product.code,
                'quantity': product.quantity,
                'price': float(price),
                'has_promotion': has_promotion,
                'regular_price': float(product.selling_price),
                'promotion_price': float(product.promotion_price) if has_promotion else None,
                'discount_percentage': round(product.discount_percentage) if has_promotion else 0
            }
        })

    except Product.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'المنتج غير موجود'}, status=404)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@login_required
def get_promotions(request):
    """الحصول على قائمة المنتجات التي لديها عروض ترويجية نشطة"""
    try:
        # الحصول على المنتجات التي لديها عروض نشطة
        products_with_promotions = []

        for product in Product.objects.filter(is_active=True, has_promotion=True):
            if product.has_active_promotion:
                products_with_promotions.append({
                    'id': product.id,
                    'name': product.name,
                    'code': product.code,
                    'regular_price': float(product.selling_price),
                    'promotion_price': float(product.promotion_price),
                    'discount_percentage': round(product.discount_percentage),
                    'quantity': product.quantity,
                    'image_url': product.image.url if product.image else None,
                    'category': product.category.name,
                })

        return JsonResponse({
            'success': True,
            'promotions_count': len(products_with_promotions),
            'products': products_with_promotions
        })

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@login_required
def alerts(request):
    today = timezone.now().date()

    # Get overdue sales (credit sales that are more than 30 days old)
    overdue_sales = []
    credit_sales = Sale.objects.filter(payment_method='credit', status='pending')

    for sale in credit_sales:
        # Calculate total paid amount
        total_paid = sale.payments.aggregate(Sum('amount'))['amount__sum'] or 0
        remaining_amount = sale.total_amount - total_paid

        # Skip if fully paid
        if remaining_amount <= 0:
            continue

        # Calculate days overdue
        days_overdue = (today - sale.date).days

        # Add to overdue if more than 30 days old
        if days_overdue > 30:
            sale.remaining_amount = remaining_amount
            sale.days_overdue = days_overdue
            overdue_sales.append(sale)

    # Get pending sales
    pending_sales = []
    pending_query = Sale.objects.filter(status='pending')

    for sale in pending_query:
        # Calculate total paid amount
        total_paid = sale.payments.aggregate(Sum('amount'))['amount__sum'] or 0
        remaining_amount = sale.total_amount - total_paid

        # Skip if fully paid
        if remaining_amount <= 0:
            continue

        sale.paid_amount = total_paid
        sale.remaining_amount = remaining_amount
        pending_sales.append(sale)

    # Get low stock products
    low_stock_products = Product.objects.filter(
        is_active=True,
        quantity__lte=F('min_quantity')
    ).order_by('quantity')

    # Calculate statistics
    overdue_count = len(overdue_sales)
    pending_count = len(pending_sales)
    low_stock_count = low_stock_products.count()

    # Calculate total pending amount
    pending_amount = sum(sale.remaining_amount for sale in pending_sales)

    context = {
        'overdue_sales': overdue_sales,
        'pending_sales': pending_sales,
        'low_stock_products': low_stock_products,
        'overdue_count': overdue_count,
        'pending_count': pending_count,
        'low_stock_count': low_stock_count,
        'pending_amount': pending_amount,
        'today': today,
    }
    return render(request, 'sales/alerts.html', context)

@login_required
@require_POST
def mark_as_paid(request, sale_id):
    """تسجيل الفاتورة كمدفوعة"""
    try:
        sale = get_object_or_404(Sale, id=sale_id)

        if sale.is_paid:
            return JsonResponse({
                'success': False,
                'message': 'هذه الفاتورة مدفوعة بالفعل'
            })

        # حساب المبلغ المتبقي للدفع
        remaining_amount = sale.remaining_amount

        if remaining_amount <= 0:
            return JsonResponse({
                'success': False,
                'message': 'هذه الفاتورة مدفوعة بالفعل'
            })

        # إنشاء سجل دفع للمبلغ المتبقي
        Payment.objects.create(
            sale=sale,
            amount=remaining_amount,
            payment_method=sale.payment_method or 'cash',
            payment_date=timezone.now(),
            notes='تم تسجيل الدفع من تقرير المبيعات'
        )

        return JsonResponse({
            'success': True,
            'message': 'تم تسجيل الفاتورة كمدفوعة بنجاح'
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'حدث خطأ: {str(e)}'
        })

@login_required
def sale_detail(request, sale_id):
    """عرض تفاصيل البيع"""
    sale = get_object_or_404(Sale, id=sale_id)
    return render(request, 'sales/sale_detail.html', {'sale': sale})

@login_required
def print_invoice(request, sale_id):
    """طباعة الفاتورة"""
    sale = get_object_or_404(Sale, id=sale_id)
    return render(request, 'sales/print_invoice.html', {'sale': sale})