{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "تفاصيل العميل" %} | {{ customer.name }} | {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .customer-card {
        border-radius: 10px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    }

    .customer-card .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #e3e6f0;
    }

    .customer-info {
        background-color: #f8f9fa;
        border-radius: 5px;
        padding: 15px;
        margin-bottom: 20px;
    }

    .customer-info .label {
        font-weight: bold;
        color: #4e73df;
    }

    .info-box {
        background-color: #fff;
        border-radius: 5px;
        padding: 15px;
        margin-bottom: 15px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
    }

    .info-box .title {
        font-weight: bold;
        color: #4e73df;
        margin-bottom: 10px;
        border-bottom: 1px solid #e3e6f0;
        padding-bottom: 5px;
    }

    .badge-status {
        font-size: 0.8rem;
    }

    .tab-content {
        padding: 20px;
        background-color: #fff;
        border: 1px solid #dee2e6;
        border-top: none;
        border-radius: 0 0 5px 5px;
    }

    .nav-tabs .nav-link {
        font-weight: 500;
    }

    .nav-tabs .nav-link.active {
        background-color: #fff;
        border-bottom-color: #fff;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0 text-gray-800">{% trans "تفاصيل العميل" %}</h1>
    <div>
        <a href="{% url 'customers:edit_customer' customer_id=customer.id %}" class="btn btn-primary me-2">
            <i class="fas fa-edit me-1"></i> {% trans "تعديل" %}
        </a>
        <button id="printCustomerCard" class="btn btn-success me-2">
            <i class="fas fa-print me-1"></i> {% trans "طباعة البطاقة" %}
        </button>
        <a href="{% url 'customers:index' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i> {% trans "العودة إلى قائمة العملاء" %}
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-4">
        <!-- بطاقة معلومات العميل -->
        <div class="card shadow customer-card mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">{% trans "معلومات العميل" %}</h6>
            </div>
            <div class="card-body">
                <div class="text-center mb-4">
                    <div class="avatar-circle mb-3">
                        <span class="avatar-text">{{ customer.name|slice:":1" }}</span>
                    </div>
                    <h4>{{ customer.name }}</h4>
                    <p class="text-muted">
                        {% if customer.category %}
                            <span class="badge bg-info">{{ customer.category.name }}</span>
                        {% endif %}
                        {% if customer.is_active %}
                            <span class="badge bg-success">{% trans "نشط" %}</span>
                        {% else %}
                            <span class="badge bg-danger">{% trans "غير نشط" %}</span>
                        {% endif %}
                    </p>
                </div>

                <div class="info-box">
                    <div class="title d-flex justify-content-between align-items-center">
                        <span>{% trans "معلومات الاتصال" %}</span>
                        <div>
                            <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#sendSmsModal">
                                <i class="fas fa-sms"></i>
                            </button>
                            {% if customer.email %}
                            <button type="button" class="btn btn-sm btn-outline-info ms-1" data-bs-toggle="modal" data-bs-target="#sendEmailModal">
                                <i class="fas fa-envelope"></i>
                            </button>
                            {% endif %}
                        </div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-4">
                            <i class="fas fa-phone me-1"></i> {% trans "الهاتف" %}:
                        </div>
                        <div class="col-8">
                            <a href="tel:{{ customer.phone }}">{{ customer.phone }}</a>
                        </div>
                    </div>
                    {% if customer.email %}
                    <div class="row mb-2">
                        <div class="col-4">
                            <i class="fas fa-envelope me-1"></i> {% trans "البريد" %}:
                        </div>
                        <div class="col-8">
                            <a href="mailto:{{ customer.email }}">{{ customer.email }}</a>
                        </div>
                    </div>
                    {% endif %}
                    {% if customer.address %}
                    <div class="row mb-2">
                        <div class="col-4">
                            <i class="fas fa-map-marker-alt me-1"></i> {% trans "العنوان" %}:
                        </div>
                        <div class="col-8">
                            {{ customer.address }}
                        </div>
                    </div>
                    {% endif %}
                    {% if customer.city %}
                    <div class="row">
                        <div class="col-4">
                            <i class="fas fa-city me-1"></i> {% trans "المدينة" %}:
                        </div>
                        <div class="col-8">
                            {{ customer.city }}
                        </div>
                    </div>
                    {% endif %}
                </div>

                <div class="info-box">
                    <div class="title">{% trans "معلومات الحساب" %}</div>
                    <div class="row mb-2">
                        <div class="col-6">
                            <i class="fas fa-calendar-alt me-1"></i> {% trans "تاريخ التسجيل" %}:
                        </div>
                        <div class="col-6">
                            {{ customer.created_at|date:"Y-m-d" }}
                        </div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-6">
                            <i class="fas fa-shopping-cart me-1"></i> {% trans "عدد المشتريات" %}:
                        </div>
                        <div class="col-6">
                            {{ total_sales }}
                        </div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-6">
                            <i class="fas fa-money-bill-wave me-1"></i> {% trans "إجمالي المشتريات" %}:
                        </div>
                        <div class="col-6">
                            {{ total_amount|floatformat:2 }} {% trans "د.م" %}
                        </div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-6">
                            <i class="fas fa-credit-card me-1"></i> {% trans "حد الائتمان" %}:
                        </div>
                        <div class="col-6">
                            {{ customer.credit_limit|floatformat:2 }} {% trans "د.م" %}
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <i class="fas fa-balance-scale me-1"></i> {% trans "الرصيد الحالي" %}:
                        </div>
                        <div class="col-6 {% if customer.balance > 0 %}text-danger{% endif %}">
                            {{ customer.balance|floatformat:2 }} {% trans "د.م" %}
                        </div>
                    </div>
                </div>

                {% if customer.notes %}
                <div class="info-box">
                    <div class="title">{% trans "ملاحظات" %}</div>
                    <p>{{ customer.notes|linebreaks }}</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-lg-8">
        <!-- علامات التبويب -->
        <ul class="nav nav-tabs" id="customerTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="sales-tab" data-bs-toggle="tab" data-bs-target="#sales" type="button" role="tab" aria-controls="sales" aria-selected="true">
                    <i class="fas fa-shopping-cart me-1"></i> {% trans "المبيعات" %}
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="vehicles-tab" data-bs-toggle="tab" data-bs-target="#vehicles" type="button" role="tab" aria-controls="vehicles" aria-selected="false">
                    <i class="fas fa-car me-1"></i> {% trans "المركبات" %}
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="interactions-tab" data-bs-toggle="tab" data-bs-target="#interactions" type="button" role="tab" aria-controls="interactions" aria-selected="false">
                    <i class="fas fa-comments me-1"></i> {% trans "التفاعلات" %}
                </button>
            </li>
        </ul>

        <div class="tab-content" id="customerTabsContent">
            <!-- تبويب المبيعات -->
            <div class="tab-pane fade show active" id="sales" role="tabpanel" aria-labelledby="sales-tab">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5>{% trans "سجل المبيعات" %}</h5>
                    <a href="{% url 'sales:new_sale' %}?customer_id={{ customer.id }}" class="btn btn-sm btn-primary">
                        <i class="fas fa-plus me-1"></i> {% trans "إضافة مبيعات جديدة" %}
                    </a>
                </div>

                {% if sales %}
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>{% trans "رقم الفاتورة" %}</th>
                                    <th>{% trans "التاريخ" %}</th>
                                    <th>{% trans "المبلغ" %}</th>
                                    <th>{% trans "المدفوع" %}</th>
                                    <th>{% trans "المتبقي" %}</th>
                                    <th>{% trans "الحالة" %}</th>
                                    <th>{% trans "الإجراءات" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for sale in sales %}
                                <tr>
                                    <td>#{{ sale.id }}</td>
                                    <td>{{ sale.date|date:"Y-m-d" }}</td>
                                    <td>{{ sale.total_amount|floatformat:2 }} {% trans "د.م" %}</td>
                                    <td>{{ sale.paid_amount|floatformat:2 }} {% trans "د.م" %}</td>
                                    <td>{{ sale.remaining_amount|floatformat:2 }} {% trans "د.م" %}</td>
                                    <td>
                                        {% if sale.status == 'completed' %}
                                            <span class="badge bg-success">{% trans "مكتمل" %}</span>
                                        {% elif sale.status == 'pending' %}
                                            <span class="badge bg-warning">{% trans "معلق" %}</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ sale.get_status_display }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{% url 'sales:view_sale' sale_id=sale.id %}" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if sales.has_other_pages %}
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            {% if sales.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1" aria-label="First">
                                        <span aria-hidden="true">&laquo;&laquo;</span>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ sales.previous_page_number }}" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                            {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="First">
                                        <span aria-hidden="true">&laquo;&laquo;</span>
                                    </a>
                                </li>
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                            {% endif %}

                            {% for i in sales.paginator.page_range %}
                                {% if sales.number == i %}
                                    <li class="page-item active"><a class="page-link" href="#">{{ i }}</a></li>
                                {% elif i > sales.number|add:'-3' and i < sales.number|add:'3' %}
                                    <li class="page-item"><a class="page-link" href="?page={{ i }}">{{ i }}</a></li>
                                {% endif %}
                            {% endfor %}

                            {% if sales.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ sales.next_page_number }}" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ sales.paginator.num_pages }}" aria-label="Last">
                                        <span aria-hidden="true">&raquo;&raquo;</span>
                                    </a>
                                </li>
                            {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Last">
                                        <span aria-hidden="true">&raquo;&raquo;</span>
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                {% else %}
                    <div class="text-center text-muted py-5">
                        <i class="fas fa-shopping-cart fa-4x mb-3"></i>
                        <p>{% trans "لا توجد مبيعات لهذا العميل بعد" %}</p>
                        <a href="{% url 'sales:new_sale' %}?customer_id={{ customer.id }}" class="btn btn-primary mt-2">
                            <i class="fas fa-plus me-1"></i> {% trans "إضافة مبيعات جديدة" %}
                        </a>
                    </div>
                {% endif %}
            </div>

            <!-- تبويب المركبات -->
            <div class="tab-pane fade" id="vehicles" role="tabpanel" aria-labelledby="vehicles-tab">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5>{% trans "مركبات العميل" %}</h5>
                    <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addVehicleModal">
                        <i class="fas fa-plus me-1"></i> {% trans "إضافة مركبة" %}
                    </button>
                </div>

                {% if vehicles %}
                    <div class="row">
                        {% for vehicle in vehicles %}
                            <div class="col-md-6 mb-3">
                                <div class="card">
                                    <div class="card-body">
                                        <h5 class="card-title">{{ vehicle.make }} {{ vehicle.model }}</h5>
                                        <h6 class="card-subtitle mb-2 text-muted">{{ vehicle.year }}</h6>
                                        <ul class="list-group list-group-flush mb-3">
                                            {% if vehicle.license_plate %}
                                                <li class="list-group-item">{% trans "رقم اللوحة" %}: {{ vehicle.license_plate }}</li>
                                            {% endif %}
                                            {% if vehicle.vin %}
                                                <li class="list-group-item">VIN: {{ vehicle.vin }}</li>
                                            {% endif %}
                                            {% if vehicle.color %}
                                                <li class="list-group-item">{% trans "اللون" %}: {{ vehicle.color }}</li>
                                            {% endif %}
                                        </ul>
                                        <div class="d-flex justify-content-end">
                                            <button type="button" class="btn btn-sm btn-danger delete-vehicle" data-id="{{ vehicle.id }}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center text-muted py-5">
                        <i class="fas fa-car fa-4x mb-3"></i>
                        <p>{% trans "لا توجد مركبات مسجلة لهذا العميل" %}</p>
                        <button type="button" class="btn btn-primary mt-2" data-bs-toggle="modal" data-bs-target="#addVehicleModal">
                            <i class="fas fa-plus me-1"></i> {% trans "إضافة مركبة" %}
                        </button>
                    </div>
                {% endif %}
            </div>

            <!-- تبويب التفاعلات -->
            <div class="tab-pane fade" id="interactions" role="tabpanel" aria-labelledby="interactions-tab">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5>{% trans "سجل التفاعلات" %}</h5>
                    <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addInteractionModal">
                        <i class="fas fa-plus me-1"></i> {% trans "إضافة تفاعل" %}
                    </button>
                </div>

                {% if interactions %}
                    <div class="timeline">
                        {% for interaction in interactions %}
                            <div class="timeline-item">
                                <div class="timeline-date">
                                    {{ interaction.date|date:"Y-m-d" }}
                                </div>
                                <div class="timeline-content">
                                    <h6>{{ interaction.get_interaction_type_display }}</h6>
                                    <p>{{ interaction.notes }}</p>
                                    <div class="text-muted small">
                                        {% if interaction.employee %}
                                            <i class="fas fa-user me-1"></i> {{ interaction.employee.name }}
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center text-muted py-5">
                        <i class="fas fa-comments fa-4x mb-3"></i>
                        <p>{% trans "لا توجد تفاعلات مسجلة مع هذا العميل" %}</p>
                        <button type="button" class="btn btn-primary mt-2" data-bs-toggle="modal" data-bs-target="#addInteractionModal">
                            <i class="fas fa-plus me-1"></i> {% trans "إضافة تفاعل" %}
                        </button>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Modal إضافة مركبة -->
<div class="modal fade" id="addVehicleModal" tabindex="-1" aria-labelledby="addVehicleModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addVehicleModalLabel">{% trans "إضافة مركبة جديدة" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="vehicleForm" method="post" action="{% url 'customers:add_vehicle' customer_id=customer.id %}">
                    {% csrf_token %}
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="id_make" class="form-label">{% trans "الشركة المصنعة" %}</label>
                            {{ vehicle_form.make }}
                        </div>
                        <div class="col-md-6">
                            <label for="id_model" class="form-label">{% trans "الموديل" %}</label>
                            {{ vehicle_form.model }}
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="id_year" class="form-label">{% trans "سنة الصنع" %}</label>
                            {{ vehicle_form.year }}
                        </div>
                        <div class="col-md-6">
                            <label for="id_color" class="form-label">{% trans "اللون" %}</label>
                            {{ vehicle_form.color }}
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="id_license_plate" class="form-label">{% trans "رقم اللوحة" %}</label>
                        {{ vehicle_form.license_plate }}
                    </div>
                    <div class="mb-3">
                        <label for="id_vin" class="form-label">{% trans "رقم الهيكل (VIN)" %}</label>
                        {{ vehicle_form.vin }}
                    </div>
                    <div class="mb-3">
                        <label for="id_notes" class="form-label">{% trans "ملاحظات" %}</label>
                        {{ vehicle_form.notes }}
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                <button type="button" class="btn btn-primary" id="saveVehicleBtn">{% trans "حفظ" %}</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal إضافة تفاعل -->
<div class="modal fade" id="addInteractionModal" tabindex="-1" aria-labelledby="addInteractionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addInteractionModalLabel">{% trans "إضافة تفاعل جديد" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="interactionForm" method="post" action="{% url 'customers:add_interaction' customer_id=customer.id %}">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="id_interaction_type" class="form-label">{% trans "نوع التفاعل" %}</label>
                        {{ interaction_form.interaction_type }}
                    </div>
                    <div class="mb-3">
                        <label for="id_date" class="form-label">{% trans "التاريخ" %}</label>
                        {{ interaction_form.date }}
                    </div>
                    <div class="mb-3">
                        <label for="id_notes" class="form-label">{% trans "ملاحظات" %}</label>
                        {{ interaction_form.notes }}
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                <button type="button" class="btn btn-primary" id="saveInteractionBtn">{% trans "حفظ" %}</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal إرسال رسالة نصية -->
<div class="modal fade" id="sendSmsModal" tabindex="-1" aria-labelledby="sendSmsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="sendSmsModalLabel">{% trans "إرسال رسالة نصية" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="smsForm">
                    <div class="mb-3">
                        <label for="smsPhone" class="form-label">{% trans "رقم الهاتف" %}</label>
                        <input type="text" class="form-control" id="smsPhone" value="{{ customer.phone }}" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="smsTemplate" class="form-label">{% trans "قالب الرسالة" %}</label>
                        <select class="form-select" id="smsTemplate">
                            <option value="">{% trans "اختر قالباً" %}</option>
                            <option value="welcome">{% trans "رسالة ترحيب" %}</option>
                            <option value="reminder">{% trans "تذكير بالدفع" %}</option>
                            <option value="promotion">{% trans "عرض ترويجي" %}</option>
                            <option value="custom">{% trans "رسالة مخصصة" %}</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="smsMessage" class="form-label">{% trans "نص الرسالة" %}</label>
                        <textarea class="form-control" id="smsMessage" rows="4" placeholder="{% trans "اكتب نص الرسالة هنا..." %}"></textarea>
                        <div class="form-text">
                            <span id="smsCharCount">0</span>/160 {% trans "حرف" %}
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                <button type="button" class="btn btn-primary" id="sendSmsBtn">{% trans "إرسال" %}</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal إرسال بريد إلكتروني -->
<div class="modal fade" id="sendEmailModal" tabindex="-1" aria-labelledby="sendEmailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="sendEmailModalLabel">{% trans "إرسال بريد إلكتروني" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="emailForm">
                    <div class="mb-3">
                        <label for="emailTo" class="form-label">{% trans "البريد الإلكتروني" %}</label>
                        <input type="email" class="form-control" id="emailTo" value="{{ customer.email }}" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="emailTemplate" class="form-label">{% trans "قالب البريد" %}</label>
                        <select class="form-select" id="emailTemplate">
                            <option value="">{% trans "اختر قالباً" %}</option>
                            <option value="welcome">{% trans "رسالة ترحيب" %}</option>
                            <option value="invoice">{% trans "فاتورة" %}</option>
                            <option value="reminder">{% trans "تذكير بالدفع" %}</option>
                            <option value="promotion">{% trans "عرض ترويجي" %}</option>
                            <option value="custom">{% trans "رسالة مخصصة" %}</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="emailSubject" class="form-label">{% trans "الموضوع" %}</label>
                        <input type="text" class="form-control" id="emailSubject" placeholder="{% trans "موضوع البريد الإلكتروني" %}">
                    </div>
                    <div class="mb-3">
                        <label for="emailMessage" class="form-label">{% trans "نص الرسالة" %}</label>
                        <textarea class="form-control" id="emailMessage" rows="6" placeholder="{% trans "اكتب نص الرسالة هنا..." %}"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                <button type="button" class="btn btn-primary" id="sendEmailBtn">{% trans "إرسال" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // حفظ المركبة
        $('#saveVehicleBtn').click(function() {
            $('#vehicleForm').submit();
        });

        // حفظ التفاعل
        $('#saveInteractionBtn').click(function() {
            $('#interactionForm').submit();
        });

        // حذف المركبة
        $('.delete-vehicle').click(function() {
            if (confirm('{% trans "هل أنت متأكد من رغبتك في حذف هذه المركبة؟" %}')) {
                var vehicleId = $(this).data('id');
                $.ajax({
                    url: '{% url "customers:delete_vehicle" vehicle_id=0 %}'.replace('0', vehicleId),
                    type: 'POST',
                    data: {
                        'csrfmiddlewaretoken': '{{ csrf_token }}'
                    },
                    success: function(response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert(response.message);
                        }
                    },
                    error: function() {
                        alert('{% trans "حدث خطأ أثناء حذف المركبة" %}');
                    }
                });
            }
        });

        // تنسيق التاريخ
        if ($('#id_date').length) {
            $('#id_date').attr('type', 'date');
        }

        // تفعيل علامات التبويب
        var hash = window.location.hash;
        if (hash) {
            $('#customerTabs a[href="' + hash + '"]').tab('show');
        }

        // تحديث الرابط عند تغيير التبويب
        $('#customerTabs a').on('shown.bs.tab', function(e) {
            window.location.hash = e.target.getAttribute('data-bs-target');
        });

        // التعامل مع قوالب الرسائل النصية
        $('#smsTemplate').change(function() {
            var template = $(this).val();
            var message = '';

            switch(template) {
                case 'welcome':
                    message = '{% trans "مرحباً بك " %}{{ customer.name }}{% trans ", نشكرك على تعاملك معنا. نتطلع لخدمتك دائماً." %}';
                    break;
                case 'reminder':
                    message = '{% trans "تذكير للعميل الكريم " %}{{ customer.name }}{% trans ", لديك رصيد مستحق بقيمة " %}{{ customer.balance|floatformat:2 }}{% trans " د.م. نرجو سداد المبلغ في أقرب وقت." %}';
                    break;
                case 'promotion':
                    message = '{% trans "عميلنا المميز " %}{{ customer.name }}{% trans ", نقدم لك خصم 10% على جميع مشترياتك خلال هذا الأسبوع. العرض ساري حتى نهاية الشهر." %}';
                    break;
                case 'custom':
                    message = '';
                    break;
            }

            $('#smsMessage').val(message);
            updateSmsCharCount();
        });

        // عد حروف الرسالة النصية
        function updateSmsCharCount() {
            var count = $('#smsMessage').val().length;
            $('#smsCharCount').text(count);

            // تغيير لون العداد إذا تجاوز 160 حرف
            if (count > 160) {
                $('#smsCharCount').addClass('text-danger');
            } else {
                $('#smsCharCount').removeClass('text-danger');
            }
        }

        $('#smsMessage').on('input', updateSmsCharCount);

        // إرسال الرسالة النصية
        $('#sendSmsBtn').click(function() {
            var phone = $('#smsPhone').val();
            var message = $('#smsMessage').val();

            if (!message) {
                alert('{% trans "الرجاء كتابة نص الرسالة" %}');
                return;
            }

            // هنا يمكن إضافة كود لإرسال الرسالة النصية باستخدام API
            // للتبسيط، سنقوم بإظهار رسالة نجاح فقط

            // إنشاء سجل تفاعل جديد
            var interactionData = {
                customer_id: {{ customer.id }},
                interaction_type: 'other',
                notes: '{% trans "تم إرسال رسالة نصية: " %}' + message,
                csrfmiddlewaretoken: '{{ csrf_token }}'
            };

            $.ajax({
                url: '{% url "customers:add_interaction" customer_id=customer.id %}',
                type: 'POST',
                data: interactionData,
                success: function() {
                    alert('{% trans "تم إرسال الرسالة النصية بنجاح" %}');
                    $('#sendSmsModal').modal('hide');
                    // إعادة تحميل الصفحة لتحديث سجل التفاعلات
                    location.reload();
                },
                error: function() {
                    alert('{% trans "حدث خطأ أثناء إرسال الرسالة النصية" %}');
                }
            });
        });

        // التعامل مع قوالب البريد الإلكتروني
        $('#emailTemplate').change(function() {
            var template = $(this).val();
            var subject = '';
            var message = '';

            switch(template) {
                case 'welcome':
                    subject = '{% trans "مرحباً بك في متجرنا" %}';
                    message = '{% trans "عزيزي " %}{{ customer.name }},\n\n{% trans "نرحب بك في متجرنا ونشكرك على تسجيلك معنا. نحن سعداء بانضمامك إلى قائمة عملائنا المميزين.\n\nيمكنك التواصل معنا في أي وقت للاستفسار عن منتجاتنا أو خدماتنا.\n\nمع خالص التقدير,\nفريق خدمة العملاء" %}';
                    break;
                case 'invoice':
                    subject = '{% trans "فاتورة مشترياتك الأخيرة" %}';
                    message = '{% trans "عزيزي " %}{{ customer.name }},\n\n{% trans "نرفق لكم فاتورة مشترياتكم الأخيرة. يرجى الاطلاع عليها والتواصل معنا في حال وجود أي استفسار.\n\nمع خالص التقدير,\nفريق خدمة العملاء" %}';
                    break;
                case 'reminder':
                    subject = '{% trans "تذكير بالمبلغ المستحق" %}';
                    message = '{% trans "عزيزي " %}{{ customer.name }},\n\n{% trans "نود تذكيركم بأن لديكم رصيد مستحق بقيمة " %}{{ customer.balance|floatformat:2 }}{% trans " د.م. نرجو سداد المبلغ في أقرب وقت ممكن.\n\nإذا كنتم قد قمتم بالسداد بالفعل، يرجى تجاهل هذا البريد الإلكتروني.\n\nمع خالص التقدير,\nفريق خدمة العملاء" %}';
                    break;
                case 'promotion':
                    subject = '{% trans "عرض خاص لعملائنا المميزين" %}';
                    message = '{% trans "عزيزي " %}{{ customer.name }},\n\n{% trans "يسرنا أن نقدم لكم عرضاً خاصاً بخصم 10% على جميع مشترياتكم خلال هذا الأسبوع.\n\nالعرض ساري حتى نهاية الشهر الحالي. لا تفوتوا هذه الفرصة!\n\nمع خالص التقدير,\nفريق خدمة العملاء" %}';
                    break;
                case 'custom':
                    subject = '';
                    message = '';
                    break;
            }

            $('#emailSubject').val(subject);
            $('#emailMessage').val(message);
        });

        // إرسال البريد الإلكتروني
        $('#sendEmailBtn').click(function() {
            var email = $('#emailTo').val();
            var subject = $('#emailSubject').val();
            var message = $('#emailMessage').val();

            if (!subject || !message) {
                alert('{% trans "الرجاء ملء جميع الحقول المطلوبة" %}');
                return;
            }

            // هنا يمكن إضافة كود لإرسال البريد الإلكتروني باستخدام API
            // للتبسيط، سنقوم بإظهار رسالة نجاح فقط

            // إنشاء سجل تفاعل جديد
            var interactionData = {
                customer_id: {{ customer.id }},
                interaction_type: 'email',
                notes: '{% trans "تم إرسال بريد إلكتروني: " %}' + subject,
                csrfmiddlewaretoken: '{{ csrf_token }}'
            };

            $.ajax({
                url: '{% url "customers:add_interaction" customer_id=customer.id %}',
                type: 'POST',
                data: interactionData,
                success: function() {
                    alert('{% trans "تم إرسال البريد الإلكتروني بنجاح" %}');
                    $('#sendEmailModal').modal('hide');
                    // إعادة تحميل الصفحة لتحديث سجل التفاعلات
                    location.reload();
                },
                error: function() {
                    alert('{% trans "حدث خطأ أثناء إرسال البريد الإلكتروني" %}');
                }
            });
        });

        // طباعة بطاقة العميل
        $('#printCustomerCard').click(function() {
            // إنشاء نافذة طباعة جديدة
            var printWindow = window.open('', '_blank', 'width=800,height=600');

            // إنشاء محتوى البطاقة
            var cardContent = `
                <!DOCTYPE html>
                <html dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>{% trans "بطاقة العميل" %} - {{ customer.name }}</title>
                    <style>
                        body {
                            font-family: Arial, sans-serif;
                            margin: 0;
                            padding: 20px;
                            direction: rtl;
                        }
                        .card {
                            border: 1px solid #ccc;
                            border-radius: 10px;
                            padding: 20px;
                            max-width: 500px;
                            margin: 0 auto;
                            box-shadow: 0 0 10px rgba(0,0,0,0.1);
                        }
                        .header {
                            text-align: center;
                            margin-bottom: 20px;
                            border-bottom: 2px solid #4e73df;
                            padding-bottom: 10px;
                        }
                        .avatar {
                            width: 100px;
                            height: 100px;
                            background-color: #4e73df;
                            border-radius: 50%;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            margin: 0 auto 15px;
                            color: white;
                            font-size: 48px;
                            font-weight: bold;
                        }
                        .customer-name {
                            font-size: 24px;
                            font-weight: bold;
                            margin: 10px 0;
                        }
                        .customer-id {
                            font-size: 14px;
                            color: #666;
                            margin-bottom: 15px;
                        }
                        .info-section {
                            margin-bottom: 15px;
                        }
                        .info-title {
                            font-weight: bold;
                            margin-bottom: 5px;
                            color: #4e73df;
                        }
                        .info-row {
                            display: flex;
                            margin-bottom: 5px;
                        }
                        .info-label {
                            width: 120px;
                            font-weight: bold;
                        }
                        .info-value {
                            flex: 1;
                        }
                        .footer {
                            text-align: center;
                            margin-top: 20px;
                            font-size: 12px;
                            color: #666;
                        }
                        @media print {
                            body {
                                padding: 0;
                            }
                            .card {
                                border: none;
                                box-shadow: none;
                            }
                            .print-btn {
                                display: none;
                            }
                        }
                    </style>
                </head>
                <body>
                    <div class="card">
                        <div class="header">
                            <div class="avatar">{{ customer.name|slice:":1" }}</div>
                            <div class="customer-name">{{ customer.name }}</div>
                            <div class="customer-id">{% trans "رقم العميل" %}: {{ customer.id }}</div>
                            {% if customer.category %}
                                <div>{{ customer.category.name }}</div>
                            {% endif %}
                        </div>

                        <div class="info-section">
                            <div class="info-title">{% trans "معلومات الاتصال" %}</div>
                            <div class="info-row">
                                <div class="info-label">{% trans "رقم الهاتف" %}:</div>
                                <div class="info-value">{{ customer.phone }}</div>
                            </div>
                            {% if customer.email %}
                            <div class="info-row">
                                <div class="info-label">{% trans "البريد الإلكتروني" %}:</div>
                                <div class="info-value">{{ customer.email }}</div>
                            </div>
                            {% endif %}
                            {% if customer.address %}
                            <div class="info-row">
                                <div class="info-label">{% trans "العنوان" %}:</div>
                                <div class="info-value">{{ customer.address }}</div>
                            </div>
                            {% endif %}
                            {% if customer.city %}
                            <div class="info-row">
                                <div class="info-label">{% trans "المدينة" %}:</div>
                                <div class="info-value">{{ customer.city }}</div>
                            </div>
                            {% endif %}
                        </div>

                        <div class="info-section">
                            <div class="info-title">{% trans "معلومات الحساب" %}</div>
                            <div class="info-row">
                                <div class="info-label">{% trans "تاريخ التسجيل" %}:</div>
                                <div class="info-value">{{ customer.created_at|date:"Y-m-d" }}</div>
                            </div>
                            <div class="info-row">
                                <div class="info-label">{% trans "عدد المشتريات" %}:</div>
                                <div class="info-value">{{ total_sales }}</div>
                            </div>
                            <div class="info-row">
                                <div class="info-label">{% trans "حد الائتمان" %}:</div>
                                <div class="info-value">{{ customer.credit_limit|floatformat:2 }} {% trans "د.م" %}</div>
                            </div>
                            <div class="info-row">
                                <div class="info-label">{% trans "الرصيد الحالي" %}:</div>
                                <div class="info-value">{{ customer.balance|floatformat:2 }} {% trans "د.م" %}</div>
                            </div>
                        </div>

                        <div class="footer">
                            <p>{% trans "تم إنشاء هذه البطاقة بتاريخ" %}: {{ today|date:"Y-m-d" }}</p>
                        </div>

                        <div class="print-btn" style="text-align: center; margin-top: 20px;">
                            <button onclick="window.print()" style="padding: 10px 20px; background-color: #4e73df; color: white; border: none; border-radius: 5px; cursor: pointer;">
                                {% trans "طباعة" %}
                            </button>
                        </div>
                    </div>
                </body>
                </html>
            `;

            // كتابة المحتوى في النافذة الجديدة
            printWindow.document.open();
            printWindow.document.write(cardContent);
            printWindow.document.close();

            // الانتظار حتى يتم تحميل الصفحة ثم طباعتها
            printWindow.onload = function() {
                setTimeout(function() {
                    printWindow.focus();
                }, 100);
            };
        });
    });
</script>

<style>
    /* تنسيق دائرة الصورة الرمزية */
    .avatar-circle {
        width: 100px;
        height: 100px;
        background-color: #4e73df;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 0 auto;
    }

    .avatar-text {
        font-size: 48px;
        color: white;
        font-weight: bold;
        text-transform: uppercase;
    }

    /* تنسيق الجدول الزمني للتفاعلات */
    .timeline {
        position: relative;
        padding: 20px 0;
    }

    .timeline:before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #e3e6f0;
        left: 20px;
    }

    .timeline-item {
        position: relative;
        margin-bottom: 30px;
        padding-left: 60px;
    }

    .timeline-date {
        position: absolute;
        left: 0;
        top: 0;
        width: 40px;
        height: 40px;
        background: #4e73df;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        color: white;
        font-size: 0.7rem;
        font-weight: bold;
        text-align: center;
        line-height: 1;
    }

    .timeline-content {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        box-shadow: 0 0 5px rgba(0, 0, 0, 0.05);
    }
</style>
{% endblock %}
