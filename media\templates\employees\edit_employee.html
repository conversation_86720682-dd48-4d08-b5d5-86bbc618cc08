{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "تعديل بيانات الموظف" %} - {{ employee.full_name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{% trans "تعديل بيانات الموظف" %}: {{ employee.full_name }}</h1>
        <div>
            <a href="{% url 'employees:view_employee' employee.id %}" class="btn btn-info">
                <i class="fas fa-eye"></i> {% trans "عرض" %}
            </a>
            <a href="{% url 'employees:index' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> {% trans "العودة إلى قائمة الموظفين" %}
            </a>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">{% trans "معلومات الموظف" %}</h6>
        </div>
        <div class="card-body">
            <form method="post" enctype="multipart/form-data">
                {% csrf_token %}

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="first_name">{% trans "الاسم الأول" %} *</label>
                            <input type="text" class="form-control" id="first_name" name="first_name" value="{{ employee.user.first_name }}" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="last_name">{% trans "الاسم الأخير" %} *</label>
                            <input type="text" class="form-control" id="last_name" name="last_name" value="{{ employee.user.last_name }}" required>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="email">{% trans "البريد الإلكتروني" %} *</label>
                            <input type="email" class="form-control" id="email" name="email" value="{{ employee.user.email }}" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="employee_id">{% trans "رقم الموظف" %} *</label>
                            <input type="text" class="form-control" id="employee_id" name="employee_id" value="{{ employee.employee_id }}" required readonly>
                            <small class="form-text text-muted">{% trans "لا يمكن تغيير رقم الموظف" %}</small>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="phone">{% trans "رقم الهاتف" %} *</label>
                            <input type="text" class="form-control" id="phone" name="phone" value="{{ employee.phone }}" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="department">{% trans "القسم" %} *</label>
                            <select class="form-control" id="department" name="department" required>
                                <option value="">{% trans "اختر القسم" %}</option>
                                {% for department in departments %}
                                <option value="{{ department.id }}" {% if employee.department.id == department.id %}selected{% endif %}>{{ department.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="position">{% trans "المنصب" %} *</label>
                            <select class="form-control" id="position" name="position" required>
                                <option value="">{% trans "اختر المنصب" %}</option>
                                {% for position in positions %}
                                <option value="{{ position.id }}" data-department="{{ position.department.id }}" {% if employee.position.id == position.id %}selected{% endif %}>{{ position.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="role">{% trans "الدور الوظيفي" %}</label>
                            <select class="form-control" id="role" name="role">
                                <option value="">{% trans "اختر الدور الوظيفي" %}</option>
                                {% for role in roles %}
                                <option value="{{ role.id }}" {% if employee.role.id == role.id %}selected{% endif %}>{{ role.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="gender">{% trans "الجنس" %} *</label>
                            <select class="form-control" id="gender" name="gender" required>
                                <option value="male" {% if employee.gender == 'male' %}selected{% endif %}>{% trans "ذكر" %}</option>
                                <option value="female" {% if employee.gender == 'female' %}selected{% endif %}>{% trans "أنثى" %}</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="date_of_birth">{% trans "تاريخ الميلاد" %}</label>
                            <input type="date" class="form-control" id="date_of_birth" name="date_of_birth" value="{{ employee.date_of_birth|date:'Y-m-d' }}">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="hire_date">{% trans "تاريخ التوظيف" %} *</label>
                            <input type="date" class="form-control" id="hire_date" name="hire_date" value="{{ employee.hire_date|date:'Y-m-d' }}" required>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="salary">{% trans "الراتب" %} *</label>
                            <input type="number" step="0.01" class="form-control" id="salary" name="salary" value="{{ employee.salary }}" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="national_id">{% trans "رقم الهوية" %}</label>
                            <input type="text" class="form-control" id="national_id" name="national_id" value="{{ employee.national_id }}">
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="is_active">{% trans "الحالة" %} *</label>
                            <select class="form-control" id="is_active" name="is_active" required>
                                <option value="True" {% if employee.is_active %}selected{% endif %}>{% trans "نشط" %}</option>
                                <option value="False" {% if not employee.is_active %}selected{% endif %}>{% trans "غير نشط" %}</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="image">{% trans "الصورة الشخصية" %}</label>
                            <input type="file" class="form-control-file" id="image" name="image">
                            {% if employee.image %}
                            <div class="mt-2">
                                <img src="{{ employee.image.url }}" alt="{{ employee.full_name }}" class="img-thumbnail" style="max-height: 100px;">
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="address">{% trans "العنوان" %}</label>
                    <textarea class="form-control" id="address" name="address" rows="3">{{ employee.address }}</textarea>
                </div>

                <div class="form-group">
                    <label for="notes">{% trans "ملاحظات" %}</label>
                    <textarea class="form-control" id="notes" name="notes" rows="3">{{ employee.notes }}</textarea>
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> {% trans "حفظ التغييرات" %}
                    </button>
                    <a href="{% url 'employees:view_employee' employee.id %}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> {% trans "إلغاء" %}
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // تصفية المناصب بناءً على القسم المحدد
        $('#department').on('change', function() {
            var departmentId = $(this).val();
            console.log('تم تغيير القسم إلى: ' + departmentId);

            // إخفاء جميع المناصب
            $('#position option').each(function() {
                if ($(this).val() === '') {
                    $(this).show(); // إظهار خيار "اختر المنصب"
                } else {
                    var positionDept = $(this).data('department');
                    console.log('منصب: ' + $(this).text() + ', قسم: ' + positionDept);

                    if (positionDept == departmentId) {
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                }
            });

            // إذا كان المنصب الحالي ليس في القسم المحدد، قم بإعادة تعيينه
            var currentPosition = $('#position').val();
            var currentPositionDept = $('#position option[value="' + currentPosition + '"]').data('department');
            if (currentPositionDept != departmentId) {
                $('#position').val('');
            }
        });

        // تشغيل تغيير القسم عند تحميل الصفحة
        $('#department').trigger('change');
    });
</script>
{% endblock %}
