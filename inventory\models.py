from django.db import models
from django.utils.translation import gettext_lazy as _
from django.conf import settings

class Category(models.Model):
    name = models.CharField(_('الاسم'), max_length=100)
    description = models.TextField(_('الوصف'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('فئة')
        verbose_name_plural = _('الفئات')
        ordering = ['name']

    def __str__(self):
        return self.name

class StorageLocation(models.Model):
    name = models.CharField(_('الاسم'), max_length=100)
    description = models.TextField(_('الوصف'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('مكان التخزين')
        verbose_name_plural = _('أماكن التخزين')
        ordering = ['name']

    def __str__(self):
        return self.name

class Product(models.Model):
    # خيارات الوحدات
    UNIT_CHOICES = [
        ('piece', _('قطعة')),
        ('kg', _('كيلوغرام')),
        ('gram', _('غرام')),
        ('liter', _('لتر')),
        ('ml', _('مليلتر')),
        ('meter', _('متر')),
        ('cm', _('سنتيمتر')),
        ('mm', _('مليمتر')),
        ('m2', _('متر مربع')),
        ('cm2', _('سنتيمتر مربع')),
        ('m3', _('متر مكعب')),
        ('cm3', _('سنتيمتر مكعب')),
        ('box', _('صندوق')),
        ('pack', _('علبة')),
        ('bottle', _('زجاجة')),
        ('bag', _('كيس')),
        ('roll', _('لفة')),
        ('sheet', _('ورقة')),
        ('pair', _('زوج')),
        ('set', _('طقم')),
        ('dozen', _('دزينة')),
        ('carton', _('كرتونة')),
        ('pallet', _('منصة نقالة')),
        ('ton', _('طن')),
        ('inch', _('بوصة')),
        ('foot', _('قدم')),
        ('yard', _('ياردة')),
        ('gallon', _('غالون')),
        ('other', _('أخرى')),
    ]

    code = models.CharField(_('الكود'), max_length=50, unique=True)
    barcode = models.CharField(_('الباركود'), max_length=100, blank=True, null=True)
    name = models.CharField(_('الاسم'), max_length=200)
    description = models.TextField(_('الوصف'), blank=True, null=True)
    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='products', verbose_name=_('الفئة'))
    supplier = models.ForeignKey('purchases.Supplier', on_delete=models.SET_NULL, related_name='products', verbose_name=_('المورد'), blank=True, null=True)
    storage_location = models.ForeignKey(StorageLocation, on_delete=models.SET_NULL, related_name='products', verbose_name=_('مكان التخزين'), blank=True, null=True)
    unit = models.CharField(_('الوحدة'), max_length=20, choices=UNIT_CHOICES, default='piece')
    custom_unit = models.CharField(_('وحدة مخصصة'), max_length=50, blank=True, null=True, help_text=_('استخدم هذا الحقل إذا اخترت "أخرى" في الوحدة'))
    purchase_price = models.DecimalField(_('سعر الشراء'), max_digits=10, decimal_places=2)
    selling_price = models.DecimalField(_('سعر البيع'), max_digits=10, decimal_places=2)
    quantity = models.PositiveIntegerField(_('الكمية'))
    min_quantity = models.PositiveIntegerField(_('الحد الأدنى للكمية'), default=1)
    image = models.ImageField(_('الصورة الرئيسية'), upload_to='products/', blank=True, null=True)
    is_active = models.BooleanField(_('نشط'), default=True)
    has_promotion = models.BooleanField(_('يوجد عرض خاص'), default=False)
    promotion_price = models.DecimalField(_('سعر العرض'), max_digits=10, decimal_places=2, blank=True, null=True)
    promotion_start_date = models.DateField(_('تاريخ بداية العرض'), blank=True, null=True)
    promotion_end_date = models.DateField(_('تاريخ نهاية العرض'), blank=True, null=True)
    promotion_description = models.TextField(_('وصف العرض'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('منتج')
        verbose_name_plural = _('المنتجات')
        ordering = ['name']

    def __str__(self):
        return f"{self.code} - {self.name}"

    @property
    def display_unit(self):
        """عرض الوحدة - إما الوحدة المحددة أو الوحدة المخصصة"""
        if self.unit == 'other' and self.custom_unit:
            return self.custom_unit
        return self.get_unit_display()

    @property
    def is_low_stock(self):
        return self.quantity <= self.min_quantity

    @property
    def profit_margin(self):
        if self.purchase_price > 0:
            return ((self.selling_price - self.purchase_price) / self.purchase_price) * 100
        return 0

    @property
    def has_active_promotion(self):
        from django.utils import timezone
        today = timezone.now().date()
        if not self.has_promotion or not self.promotion_price:
            return False
        if self.promotion_start_date and self.promotion_start_date > today:
            return False
        if self.promotion_end_date and self.promotion_end_date < today:
            return False
        return True

    @property
    def current_price(self):
        if self.has_active_promotion:
            return self.promotion_price
        return self.selling_price

    @property
    def discount_percentage(self):
        if not self.has_active_promotion or not self.promotion_price or self.selling_price <= 0:
            return 0
        return ((self.selling_price - self.promotion_price) / self.selling_price) * 100

class ProductMovement(models.Model):
    MOVEMENT_TYPE_CHOICES = (
        ('in', _('وارد')),
        ('out', _('صادر')),
        ('adjustment', _('تعديل')),
    )

    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='movements', verbose_name=_('المنتج'))
    movement_type = models.CharField(_('نوع الحركة'), max_length=20, choices=MOVEMENT_TYPE_CHOICES)
    quantity = models.PositiveIntegerField(_('الكمية'))
    reference = models.CharField(_('المرجع'), max_length=100, blank=True, null=True)
    notes = models.TextField(_('ملاحظات'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)

    class Meta:
        verbose_name = _('حركة المنتج')
        verbose_name_plural = _('حركات المنتجات')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.product.name} - {self.get_movement_type_display()} - {self.quantity}"
