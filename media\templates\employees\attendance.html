{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "إدارة الحضور" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{% trans "إدارة الحضور" %}</h1>
        <div>
            <button class="btn btn-primary" data-toggle="modal" data-target="#addAttendanceModal">
                <i class="fas fa-plus"></i> {% trans "تسجيل حضور" %}
            </button>
            <a href="{% url 'employees:index' %}" class="btn btn-secondary">
                <i class="fas fa-users"></i> {% trans "الموظفين" %}
            </a>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">{% trans "سجل الحضور" %}</h6>
            <div class="form-inline">
                <input type="date" class="form-control mr-2" id="attendanceDate" value="{{ today|date:'Y-m-d' }}">
                <button class="btn btn-sm btn-primary" id="filterByDate">
                    <i class="fas fa-filter"></i> {% trans "تصفية" %}
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="attendanceTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>{% trans "الموظف" %}</th>
                            <th>{% trans "القسم" %}</th>
                            <th>{% trans "الحالة" %}</th>
                            <th>{% trans "وقت الحضور" %}</th>
                            <th>{% trans "وقت الانصراف" %}</th>
                            <th>{% trans "ملاحظات" %}</th>
                            <th>{% trans "الإجراءات" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- سيتم ملء هذا الجدول بواسطة AJAX -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modal for Adding Attendance -->
<div class="modal fade" id="addAttendanceModal" tabindex="-1" role="dialog" aria-labelledby="addAttendanceModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addAttendanceModalLabel">{% trans "تسجيل حضور" %}</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="attendanceForm">
                    {% csrf_token %}
                    <div class="form-group">
                        <label for="attendance_date">{% trans "التاريخ" %} *</label>
                        <input type="date" class="form-control" id="attendance_date" name="date" value="{{ today|date:'Y-m-d' }}" required>
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-bordered" id="employeesAttendanceTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>{% trans "الموظف" %}</th>
                                    <th>{% trans "القسم" %}</th>
                                    <th>{% trans "الحالة" %}</th>
                                    <th>{% trans "وقت الحضور" %}</th>
                                    <th>{% trans "وقت الانصراف" %}</th>
                                    <th>{% trans "ملاحظات" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for employee in employees %}
                                <tr>
                                    <td>
                                        <input type="hidden" name="employee_id[]" value="{{ employee.id }}">
                                        {{ employee.full_name }}
                                    </td>
                                    <td>{{ employee.department.name|default:"-" }}</td>
                                    <td>
                                        <select class="form-control" name="status[]" required>
                                            <option value="present">{% trans "حاضر" %}</option>
                                            <option value="absent">{% trans "غائب" %}</option>
                                            <option value="late">{% trans "متأخر" %}</option>
                                            <option value="half_day">{% trans "نصف يوم" %}</option>
                                            <option value="leave">{% trans "إجازة" %}</option>
                                        </select>
                                    </td>
                                    <td>
                                        <input type="time" class="form-control" name="check_in[]">
                                    </td>
                                    <td>
                                        <input type="time" class="form-control" name="check_out[]">
                                    </td>
                                    <td>
                                        <input type="text" class="form-control" name="notes[]" placeholder="{% trans 'ملاحظات' %}">
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">{% trans "إلغاء" %}</button>
                <button type="button" class="btn btn-primary" id="saveAttendance">{% trans "حفظ" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Initialize DataTable
        var attendanceTable = $('#attendanceTable').DataTable({
            "language": {
                "url": "{% static 'js/dataTables.arabic.json' %}"
            },
            "order": [[0, "asc"]]
        });
        
        // Initialize DataTable for employees in modal
        var employeesTable = $('#employeesAttendanceTable').DataTable({
            "language": {
                "url": "{% static 'js/dataTables.arabic.json' %}"
            },
            "order": [[0, "asc"]],
            "paging": false,
            "searching": true
        });
        
        // Filter attendance by date
        $('#filterByDate').click(function() {
            var date = $('#attendanceDate').val();
            // Here you would make an AJAX call to get attendance for the selected date
            // For now, we'll just show a message
            alert('سيتم تنفيذ التصفية حسب التاريخ: ' + date);
        });
        
        // Save attendance
        $('#saveAttendance').click(function() {
            // Here you would submit the form via AJAX
            // For now, we'll just show a message
            alert('سيتم حفظ بيانات الحضور');
            $('#addAttendanceModal').modal('hide');
        });
    });
</script>
{% endblock %}
