{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "تغيير كلمة المرور" %} | {{ block.super }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "تغيير كلمة المرور" %}</h6>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        {% if form.errors %}
                        <div class="alert alert-danger">
                            {% for field in form %}
                                {% for error in field.errors %}
                                    <p>{{ field.label }}: {{ error }}</p>
                                {% endfor %}
                            {% endfor %}
                        </div>
                        {% endif %}
                        
                        <div class="mb-3">
                            <label for="id_old_password" class="form-label">{% trans "كلمة المرور الحالية" %}</label>
                            <input type="password" name="old_password" id="id_old_password" class="form-control" required>
                            {% if form.old_password.help_text %}
                            <small class="form-text text-muted">{{ form.old_password.help_text }}</small>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="id_new_password1" class="form-label">{% trans "كلمة المرور الجديدة" %}</label>
                            <input type="password" name="new_password1" id="id_new_password1" class="form-control" required>
                            {% if form.new_password1.help_text %}
                            <small class="form-text text-muted">{{ form.new_password1.help_text }}</small>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="id_new_password2" class="form-label">{% trans "تأكيد كلمة المرور الجديدة" %}</label>
                            <input type="password" name="new_password2" id="id_new_password2" class="form-control" required>
                            {% if form.new_password2.help_text %}
                            <small class="form-text text-muted">{{ form.new_password2.help_text }}</small>
                            {% endif %}
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-key me-2"></i> {% trans "تغيير كلمة المرور" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
