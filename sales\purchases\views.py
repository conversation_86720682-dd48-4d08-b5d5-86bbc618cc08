from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.http import JsonResponse, HttpResponse
from django.db.models import Sum, Count, Q, F
from django.views.decorators.csrf import csrf_exempt
from datetime import datetime, date, timedelta
import json
import csv
import xlwt

from .models import Purchase, PurchaseItem, Supplier, SupplierPayment, SupplierCategory, PurchaseInvoice
from inventory.models import Product, ProductMovement

@login_required
def index(request):
    # Get recent purchases
    recent_purchases = Purchase.objects.all().order_by('-date')[:5]

    # Get pending purchases
    pending_purchases = Purchase.objects.filter(status='pending').order_by('-date')

    # Get purchases with upcoming delivery dates
    from datetime import date as date_class
    today = date_class.today()
    upcoming_deliveries = Purchase.objects.filter(
        status='pending',
        expected_delivery_date__gte=today,
        expected_delivery_date__lte=today + timedelta(days=7)
    ).order_by('expected_delivery_date')

    # Get unpaid invoices
    unpaid_invoices = PurchaseInvoice.objects.filter(
        status__in=['pending', 'verified']
    ).order_by('due_date')

    # Get purchase statistics
    total_purchases = Purchase.objects.count()
    pending_count = Purchase.objects.filter(status='pending').count()
    received_count = Purchase.objects.filter(status='received').count()
    cancelled_count = Purchase.objects.filter(status='cancelled').count()

    # Get payment statistics
    total_amount = Purchase.objects.aggregate(total=Sum('total_amount'))['total'] or 0
    paid_amount = Purchase.objects.filter(payment_status='paid').aggregate(total=Sum('total_amount'))['total'] or 0
    unpaid_amount = Purchase.objects.filter(payment_status='unpaid').aggregate(total=Sum('total_amount'))['total'] or 0
    partial_amount = Purchase.objects.filter(payment_status='partial').aggregate(total=Sum('total_amount'))['total'] or 0

    # Get top suppliers
    top_suppliers = Supplier.objects.annotate(
        purchase_count=Count('purchases'),
        purchase_total=Sum('purchases__total_amount')
    ).order_by('-purchase_total')[:5]

    context = {
        'recent_purchases': recent_purchases,
        'pending_purchases': pending_purchases,
        'upcoming_deliveries': upcoming_deliveries,
        'unpaid_invoices': unpaid_invoices,
        'total_purchases': total_purchases,
        'pending_count': pending_count,
        'received_count': received_count,
        'cancelled_count': cancelled_count,
        'total_amount': total_amount,
        'paid_amount': paid_amount,
        'unpaid_amount': unpaid_amount,
        'partial_amount': partial_amount,
        'top_suppliers': top_suppliers,
    }
    return render(request, 'purchases/index.html', context)

@login_required
def purchase_orders(request):
    status_filter = request.GET.get('status', '')
    supplier_filter = request.GET.get('supplier', '')
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')

    purchases = Purchase.objects.all()

    # Apply filters
    if status_filter:
        purchases = purchases.filter(status=status_filter)

    if supplier_filter:
        purchases = purchases.filter(supplier_id=supplier_filter)

    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
            purchases = purchases.filter(date__gte=date_from_obj)
        except ValueError:
            pass

    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
            purchases = purchases.filter(date__lte=date_to_obj)
        except ValueError:
            pass

    # Get all suppliers for filter dropdown
    suppliers = Supplier.objects.filter(is_active=True).order_by('name')

    context = {
        'purchases': purchases.order_by('-date'),
        'suppliers': suppliers,
        'status_filter': status_filter,
        'supplier_filter': supplier_filter,
        'date_from': date_from,
        'date_to': date_to,
        'status_choices': Purchase.STATUS_CHOICES,
    }
    return render(request, 'purchases/purchase_orders.html', context)

@login_required
def filter_purchases(request):
    return purchase_orders(request)

@login_required
def new_purchase(request):
    suppliers = Supplier.objects.filter(is_active=True).order_by('name')
    products = Product.objects.filter(is_active=True).order_by('name')

    # Get product IDs and quantities from query parameters (if coming from inventory alerts)
    product_ids = request.GET.getlist('product_ids')
    quantities = request.GET.getlist('quantities')

    # Prepare initial items if product IDs and quantities are provided
    initial_items = []
    if product_ids and quantities and len(product_ids) == len(quantities):
        for i in range(len(product_ids)):
            try:
                product = Product.objects.get(id=product_ids[i])
                quantity = int(quantities[i])
                initial_items.append({
                    'product': product,
                    'quantity': quantity,
                    'unit_price': product.purchase_price
                })
            except (Product.DoesNotExist, ValueError):
                pass

    if request.method == 'POST':
        supplier_id = request.POST.get('supplier')
        date = request.POST.get('date')
        expected_delivery_date = request.POST.get('expected_delivery_date')
        shipping_cost = request.POST.get('shipping_cost', 0)
        notes = request.POST.get('notes')
        product_ids = request.POST.getlist('product_ids[]')
        quantities = request.POST.getlist('quantities[]')
        unit_prices = request.POST.getlist('unit_prices[]')

        if not supplier_id or not date or not product_ids or len(product_ids) != len(quantities) or len(product_ids) != len(unit_prices):
            messages.error(request, _('يرجى التحقق من جميع الحقول المطلوبة'))
            return redirect('purchases:new_purchase')

        try:
            # Create purchase
            supplier = Supplier.objects.get(id=supplier_id)

            # Calculate subtotal
            subtotal = 0
            for i in range(len(product_ids)):
                product = Product.objects.get(id=product_ids[i])
                quantity = int(quantities[i])
                unit_price = float(unit_prices[i])
                subtotal += quantity * unit_price

            # Create purchase
            purchase = Purchase.objects.create(
                supplier=supplier,
                employee=request.user,
                date=date,
                expected_delivery_date=expected_delivery_date,
                subtotal=subtotal,
                shipping_cost=shipping_cost,
                notes=notes,
                status='pending',
                tax_amount=0,  # Will be calculated in save method
                total_amount=0  # Will be calculated in save method
            )

            # Add items
            for i in range(len(product_ids)):
                product = Product.objects.get(id=product_ids[i])
                quantity = int(quantities[i])
                unit_price = float(unit_prices[i])

                PurchaseItem.objects.create(
                    purchase=purchase,
                    product=product,
                    quantity=quantity,
                    unit_price=unit_price,
                    subtotal=quantity * unit_price
                )

            messages.success(request, _('تم إنشاء طلب الشراء بنجاح'))
            return redirect('purchases:view_purchase', purchase_id=purchase.id)

        except Exception as e:
            messages.error(request, f'حدث خطأ: {str(e)}')

    from datetime import date as date_class
    context = {
        'suppliers': suppliers,
        'products': products,
        'initial_items': initial_items,
        'today': date_class.today(),
    }
    return render(request, 'purchases/new_purchase.html', context)

@login_required
def view_purchase(request, purchase_id):
    purchase = get_object_or_404(Purchase, id=purchase_id)
    items = purchase.items.all()
    payments = purchase.payments.all()
    invoices = purchase.invoices.all()

    context = {
        'purchase': purchase,
        'items': items,
        'payments': payments,
        'invoices': invoices,
    }
    return render(request, 'purchases/view_purchase.html', context)

@login_required
def receive_purchase(request, purchase_id):
    purchase = get_object_or_404(Purchase, id=purchase_id)
    items = purchase.items.all()

    if purchase.status == 'received':
        messages.error(request, _('تم استلام هذا الطلب بالفعل'))
        return redirect('purchases:view_purchase', purchase_id=purchase_id)

    if purchase.status == 'cancelled':
        messages.error(request, _('لا يمكن استلام طلب ملغي'))
        return redirect('purchases:view_purchase', purchase_id=purchase_id)

    if request.method == 'POST':
        actual_delivery_date = request.POST.get('actual_delivery_date')
        notes = request.POST.get('notes')
        received_quantities = {}

        # Get received quantities for each item
        for item in items:
            received_qty = request.POST.get(f'received_quantity_{item.id}', 0)
            try:
                received_qty = int(received_qty)
                if received_qty < 0:
                    received_qty = 0
                received_quantities[item.id] = received_qty
            except ValueError:
                received_quantities[item.id] = 0

        # Update purchase status and delivery date
        purchase.status = 'received'
        purchase.actual_delivery_date = actual_delivery_date
        if notes:
            purchase.notes = (purchase.notes or '') + '\n' + notes if purchase.notes else notes
        purchase.save()

        # Update items and inventory
        for item in items:
            received_qty = received_quantities.get(item.id, 0)

            # Store original received quantity to track changes
            original_received_qty = item.received_quantity

            # Update received quantity
            item.received_quantity = received_qty
            item._original_received_quantity = original_received_qty  # Set for the save method
            item.save()

            # Update inventory (handled in the item's save method)

        messages.success(request, _('تم تحديث حالة الطلب إلى "تم الاستلام"'))
        return redirect('purchases:view_purchase', purchase_id=purchase_id)

    from datetime import date as date_class
    context = {
        'purchase': purchase,
        'items': items,
        'today': date_class.today().strftime('%Y-%m-%d'),
    }
    return render(request, 'purchases/receive_purchase.html', context)

@login_required
def edit_purchase(request, purchase_id):
    purchase = get_object_or_404(Purchase, id=purchase_id)
    items = purchase.items.all()
    suppliers = Supplier.objects.filter(is_active=True).order_by('name')
    products = Product.objects.filter(is_active=True).order_by('name')

    if request.method == 'POST':
        # Process form data
        pass

    context = {
        'purchase': purchase,
        'items': items,
        'suppliers': suppliers,
        'products': products,
    }
    return render(request, 'purchases/edit_purchase.html', context)

@login_required
def delete_purchase(request, purchase_id):
    purchase = get_object_or_404(Purchase, id=purchase_id)

    if request.method == 'POST':
        purchase.delete()
        messages.success(request, _('تم حذف المشتريات بنجاح'))
        return redirect('purchases:index')

    context = {
        'purchase': purchase,
    }
    return render(request, 'purchases/delete_purchase.html', context)

@login_required
def suppliers(request):
    suppliers_list = Supplier.objects.all().order_by('name')
    categories = SupplierCategory.objects.all().order_by('name')

    context = {
        'suppliers': suppliers_list,
        'categories': categories,
    }
    return render(request, 'purchases/suppliers.html', context)

@login_required
def add_supplier(request):
    categories = SupplierCategory.objects.all().order_by('name')

    if request.method == 'POST':
        name = request.POST.get('name')
        category_id = request.POST.get('category')
        contact_person = request.POST.get('contact_person')
        phone = request.POST.get('phone')
        email = request.POST.get('email')
        address = request.POST.get('address')
        city = request.POST.get('city')
        country = request.POST.get('country')
        tax_number = request.POST.get('tax_number')
        website = request.POST.get('website')
        notes = request.POST.get('notes')
        is_active = request.POST.get('is_active') == 'on'

        if not name or not phone:
            messages.error(request, _('يرجى ملء جميع الحقول المطلوبة'))
            return redirect('purchases:add_supplier')

        # Check if supplier with same name already exists
        if Supplier.objects.filter(name=name).exists():
            messages.error(request, _('يوجد مورد بنفس الاسم بالفعل'))
            return redirect('purchases:add_supplier')

        # Create new supplier
        supplier = Supplier(
            name=name,
            contact_person=contact_person,
            phone=phone,
            email=email,
            address=address,
            city=city,
            country=country,
            tax_number=tax_number,
            website=website,
            notes=notes,
            is_active=is_active
        )

        # Add category if selected
        if category_id:
            try:
                category = SupplierCategory.objects.get(id=category_id)
                supplier.category = category
            except SupplierCategory.DoesNotExist:
                pass

        supplier.save()
        messages.success(request, _('تم إضافة المورد بنجاح'))
        return redirect('purchases:suppliers')

    context = {
        'categories': categories,
    }
    return render(request, 'purchases/add_supplier.html', context)

@login_required
def edit_supplier(request, supplier_id):
    supplier = get_object_or_404(Supplier, id=supplier_id)
    categories = SupplierCategory.objects.all().order_by('name')

    if request.method == 'POST':
        name = request.POST.get('name')
        category_id = request.POST.get('category')
        contact_person = request.POST.get('contact_person')
        phone = request.POST.get('phone')
        email = request.POST.get('email')
        address = request.POST.get('address')
        city = request.POST.get('city')
        country = request.POST.get('country')
        tax_number = request.POST.get('tax_number')
        website = request.POST.get('website')
        notes = request.POST.get('notes')
        is_active = request.POST.get('is_active') == 'on'

        if not name or not phone:
            messages.error(request, _('يرجى ملء جميع الحقول المطلوبة'))
            return redirect('purchases:edit_supplier', supplier_id=supplier_id)

        # Check if supplier with same name already exists (excluding current supplier)
        if Supplier.objects.filter(name=name).exclude(id=supplier_id).exists():
            messages.error(request, _('يوجد مورد بنفس الاسم بالفعل'))
            return redirect('purchases:edit_supplier', supplier_id=supplier_id)

        # Update supplier
        supplier.name = name
        supplier.contact_person = contact_person
        supplier.phone = phone
        supplier.email = email
        supplier.address = address
        supplier.city = city
        supplier.country = country
        supplier.tax_number = tax_number
        supplier.website = website
        supplier.notes = notes
        supplier.is_active = is_active

        # Update category
        if category_id:
            try:
                category = SupplierCategory.objects.get(id=category_id)
                supplier.category = category
            except SupplierCategory.DoesNotExist:
                supplier.category = None
        else:
            supplier.category = None

        supplier.save()
        messages.success(request, _('تم تحديث المورد بنجاح'))
        return redirect('purchases:suppliers')

    context = {
        'supplier': supplier,
        'categories': categories,
    }
    return render(request, 'purchases/edit_supplier.html', context)

@login_required
def view_supplier(request, supplier_id):
    supplier = get_object_or_404(Supplier, id=supplier_id)
    purchases = supplier.purchases.all().order_by('-date')

    # Calculate statistics
    total_purchases = purchases.count()
    total_amount = purchases.aggregate(total=Sum('total_amount'))['total'] or 0
    pending_purchases = purchases.filter(status='pending').count()
    received_purchases = purchases.filter(status='received').count()

    context = {
        'supplier': supplier,
        'purchases': purchases,
        'total_purchases': total_purchases,
        'total_amount': total_amount,
        'pending_purchases': pending_purchases,
        'received_purchases': received_purchases,
    }
    return render(request, 'purchases/view_supplier.html', context)

@login_required
def delete_supplier(request, supplier_id):
    supplier = get_object_or_404(Supplier, id=supplier_id)

    # Check if supplier has purchases
    if supplier.purchases.exists():
        messages.error(request, _('لا يمكن حذف المورد لأنه مرتبط بطلبات شراء'))
        return redirect('purchases:suppliers')

    if request.method == 'POST':
        supplier.delete()
        messages.success(request, _('تم حذف المورد بنجاح'))
        return redirect('purchases:suppliers')

    context = {
        'supplier': supplier,
    }
    return render(request, 'purchases/delete_supplier.html', context)

@login_required
def search_suppliers(request):
    query = request.GET.get('q', '')

    if query:
        suppliers = Supplier.objects.filter(
            Q(name__icontains=query) |
            Q(contact_person__icontains=query) |
            Q(phone__icontains=query) |
            Q(email__icontains=query)
        ).order_by('name')
    else:
        suppliers = Supplier.objects.all().order_by('name')

    context = {
        'suppliers': suppliers,
        'query': query,
    }
    return render(request, 'purchases/suppliers.html', context)

@login_required
def supplier_categories(request):
    categories = SupplierCategory.objects.all().order_by('name')

    context = {
        'categories': categories,
    }
    return render(request, 'purchases/supplier_categories.html', context)

@login_required
def add_supplier_category(request):
    if request.method == 'POST':
        name = request.POST.get('name')
        description = request.POST.get('description')

        if not name:
            messages.error(request, _('يرجى إدخال اسم الفئة'))
            return redirect('purchases:supplier_categories')

        # Check if category with same name already exists
        if SupplierCategory.objects.filter(name=name).exists():
            messages.error(request, _('يوجد فئة بنفس الاسم بالفعل'))
            return redirect('purchases:supplier_categories')

        # Create new category
        SupplierCategory.objects.create(name=name, description=description)
        messages.success(request, _('تم إضافة فئة الموردين بنجاح'))

    return redirect('purchases:supplier_categories')

@login_required
def edit_supplier_category(request, category_id):
    category = get_object_or_404(SupplierCategory, id=category_id)

    if request.method == 'POST':
        name = request.POST.get('name')
        description = request.POST.get('description')

        if not name:
            messages.error(request, _('يرجى إدخال اسم الفئة'))
            return redirect('purchases:supplier_categories')

        # Check if category with same name already exists (excluding current category)
        if SupplierCategory.objects.filter(name=name).exclude(id=category_id).exists():
            messages.error(request, _('يوجد فئة بنفس الاسم بالفعل'))
            return redirect('purchases:supplier_categories')

        # Update category
        category.name = name
        category.description = description
        category.save()
        messages.success(request, _('تم تحديث فئة الموردين بنجاح'))

    return redirect('purchases:supplier_categories')

@login_required
def delete_supplier_category(request, category_id):
    category = get_object_or_404(SupplierCategory, id=category_id)

    # Check if category has suppliers
    if category.suppliers.exists():
        messages.error(request, _('لا يمكن حذف الفئة لأنها مرتبطة بموردين'))
        return redirect('purchases:supplier_categories')

    if request.method == 'POST':
        category.delete()
        messages.success(request, _('تم حذف فئة الموردين بنجاح'))

    return redirect('purchases:supplier_categories')

# Invoice Management
@login_required
def invoices(request):
    status_filter = request.GET.get('status', '')
    supplier_filter = request.GET.get('supplier', '')
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')

    invoices_list = PurchaseInvoice.objects.all()

    # Apply filters
    if status_filter:
        invoices_list = invoices_list.filter(status=status_filter)

    if supplier_filter:
        invoices_list = invoices_list.filter(purchase__supplier_id=supplier_filter)

    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
            invoices_list = invoices_list.filter(invoice_date__gte=date_from_obj)
        except ValueError:
            pass

    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
            invoices_list = invoices_list.filter(invoice_date__lte=date_to_obj)
        except ValueError:
            pass

    # Get all suppliers for filter dropdown
    suppliers = Supplier.objects.filter(is_active=True).order_by('name')

    context = {
        'invoices': invoices_list.order_by('-invoice_date'),
        'suppliers': suppliers,
        'status_filter': status_filter,
        'supplier_filter': supplier_filter,
        'date_from': date_from,
        'date_to': date_to,
        'status_choices': PurchaseInvoice.INVOICE_STATUS_CHOICES,
    }
    return render(request, 'purchases/invoices.html', context)

@login_required
def add_invoice(request, purchase_id):
    purchase = get_object_or_404(Purchase, id=purchase_id)

    if request.method == 'POST':
        invoice_number = request.POST.get('invoice_number')
        invoice_date = request.POST.get('invoice_date')
        due_date = request.POST.get('due_date')
        amount = request.POST.get('amount')
        notes = request.POST.get('notes')
        invoice_file = request.FILES.get('invoice_file')

        if not invoice_number or not invoice_date or not amount:
            messages.error(request, _('يرجى ملء جميع الحقول المطلوبة'))
            return redirect('purchases:add_invoice', purchase_id=purchase_id)

        # Check if invoice with same number already exists
        if PurchaseInvoice.objects.filter(invoice_number=invoice_number).exists():
            messages.error(request, _('يوجد فاتورة بنفس الرقم بالفعل'))
            return redirect('purchases:add_invoice', purchase_id=purchase_id)

        try:
            # Create invoice
            invoice = PurchaseInvoice(
                purchase=purchase,
                invoice_number=invoice_number,
                invoice_date=invoice_date,
                due_date=due_date if due_date else None,
                amount=amount,
                notes=notes,
                status='pending'
            )

            if invoice_file:
                invoice.invoice_file = invoice_file

            invoice.save()
            messages.success(request, _('تم إضافة الفاتورة بنجاح'))
            return redirect('purchases:view_purchase', purchase_id=purchase_id)

        except Exception as e:
            messages.error(request, f'حدث خطأ: {str(e)}')

    from datetime import date as date_class
    context = {
        'purchase': purchase,
        'today': date_class.today().strftime('%Y-%m-%d'),
    }
    return render(request, 'purchases/add_invoice.html', context)

@login_required
def view_invoice(request, invoice_id):
    invoice = get_object_or_404(PurchaseInvoice, id=invoice_id)
    payments = invoice.payments.all()

    context = {
        'invoice': invoice,
        'payments': payments,
    }
    return render(request, 'purchases/view_invoice.html', context)

@login_required
def edit_invoice(request, invoice_id):
    invoice = get_object_or_404(PurchaseInvoice, id=invoice_id)

    if request.method == 'POST':
        invoice_number = request.POST.get('invoice_number')
        invoice_date = request.POST.get('invoice_date')
        due_date = request.POST.get('due_date')
        amount = request.POST.get('amount')
        status = request.POST.get('status')
        notes = request.POST.get('notes')
        invoice_file = request.FILES.get('invoice_file')
        delete_file = request.POST.get('delete_file') == 'on'

        if not invoice_number or not invoice_date or not amount:
            messages.error(request, _('يرجى ملء جميع الحقول المطلوبة'))
            return redirect('purchases:edit_invoice', invoice_id=invoice_id)

        # Check if invoice with same number already exists (excluding current invoice)
        if PurchaseInvoice.objects.filter(invoice_number=invoice_number).exclude(id=invoice_id).exists():
            messages.error(request, _('يوجد فاتورة بنفس الرقم بالفعل'))
            return redirect('purchases:edit_invoice', invoice_id=invoice_id)

        try:
            # Update invoice
            invoice.invoice_number = invoice_number
            invoice.invoice_date = invoice_date
            invoice.due_date = due_date if due_date else None
            invoice.amount = amount
            invoice.status = status
            invoice.notes = notes

            # Handle file upload/deletion
            if delete_file and invoice.invoice_file:
                invoice.invoice_file.delete()
                invoice.invoice_file = None

            if invoice_file:
                if invoice.invoice_file:
                    invoice.invoice_file.delete()
                invoice.invoice_file = invoice_file

            invoice.save()
            messages.success(request, _('تم تحديث الفاتورة بنجاح'))
            return redirect('purchases:view_invoice', invoice_id=invoice_id)

        except Exception as e:
            messages.error(request, f'حدث خطأ: {str(e)}')

    context = {
        'invoice': invoice,
    }
    return render(request, 'purchases/edit_invoice.html', context)

@login_required
def delete_invoice(request, invoice_id):
    invoice = get_object_or_404(PurchaseInvoice, id=invoice_id)
    purchase_id = invoice.purchase.id

    # Check if invoice has payments
    if invoice.payments.exists():
        messages.error(request, _('لا يمكن حذف الفاتورة لأنها مرتبطة بمدفوعات'))
        return redirect('purchases:view_invoice', invoice_id=invoice_id)

    if request.method == 'POST':
        # Delete file if exists
        if invoice.invoice_file:
            invoice.invoice_file.delete()

        invoice.delete()
        messages.success(request, _('تم حذف الفاتورة بنجاح'))
        return redirect('purchases:view_purchase', purchase_id=purchase_id)

    context = {
        'invoice': invoice,
    }
    return render(request, 'purchases/delete_invoice.html', context)

# Payment Management
@login_required
def payments(request):
    supplier_filter = request.GET.get('supplier', '')
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')

    payments_list = SupplierPayment.objects.all()

    # Apply filters
    if supplier_filter:
        payments_list = payments_list.filter(purchase__supplier_id=supplier_filter)

    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
            payments_list = payments_list.filter(payment_date__gte=date_from_obj)
        except ValueError:
            pass

    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
            payments_list = payments_list.filter(payment_date__lte=date_to_obj)
        except ValueError:
            pass

    # Get all suppliers for filter dropdown
    suppliers = Supplier.objects.filter(is_active=True).order_by('name')

    context = {
        'payments': payments_list.order_by('-payment_date'),
        'suppliers': suppliers,
        'supplier_filter': supplier_filter,
        'date_from': date_from,
        'date_to': date_to,
        'payment_methods': SupplierPayment.PAYMENT_METHOD_CHOICES,
    }
    return render(request, 'purchases/payments.html', context)

@login_required
def add_payment(request, purchase_id):
    purchase = get_object_or_404(Purchase, id=purchase_id)
    invoices = purchase.invoices.filter(status__in=['pending', 'verified']).order_by('-invoice_date')

    if request.method == 'POST':
        amount = request.POST.get('amount')
        payment_method = request.POST.get('payment_method')
        payment_date = request.POST.get('payment_date')
        reference = request.POST.get('reference')
        notes = request.POST.get('notes')
        invoice_id = request.POST.get('invoice')

        if not amount or not payment_method or not payment_date:
            messages.error(request, _('يرجى ملء جميع الحقول المطلوبة'))
            return redirect('purchases:add_payment', purchase_id=purchase_id)

        try:
            # Create payment
            payment = SupplierPayment(
                purchase=purchase,
                amount=amount,
                payment_method=payment_method,
                payment_date=payment_date,
                reference=reference,
                notes=notes
            )

            # Link to invoice if selected
            if invoice_id:
                try:
                    invoice = PurchaseInvoice.objects.get(id=invoice_id)
                    payment.invoice = invoice
                except PurchaseInvoice.DoesNotExist:
                    pass

            payment.save()  # This will update purchase payment status in the save method
            messages.success(request, _('تم إضافة الدفعة بنجاح'))
            return redirect('purchases:view_purchase', purchase_id=purchase_id)

        except Exception as e:
            messages.error(request, f'حدث خطأ: {str(e)}')

    context = {
        'purchase': purchase,
        'invoices': invoices,
        'payment_methods': SupplierPayment.PAYMENT_METHOD_CHOICES,
        'today': date.today().strftime('%Y-%m-%d'),
        'remaining_amount': purchase.total_amount - (purchase.payments.aggregate(total=Sum('amount'))['total'] or 0),
    }
    return render(request, 'purchases/add_payment.html', context)

@login_required
def edit_payment(request, payment_id):
    payment = get_object_or_404(SupplierPayment, id=payment_id)
    purchase = payment.purchase
    invoices = purchase.invoices.filter(status__in=['pending', 'verified']).order_by('-invoice_date')

    if request.method == 'POST':
        amount = request.POST.get('amount')
        payment_method = request.POST.get('payment_method')
        payment_date = request.POST.get('payment_date')
        reference = request.POST.get('reference')
        notes = request.POST.get('notes')
        invoice_id = request.POST.get('invoice')

        if not amount or not payment_method or not payment_date:
            messages.error(request, _('يرجى ملء جميع الحقول المطلوبة'))
            return redirect('purchases:edit_payment', payment_id=payment_id)

        try:
            # Update payment
            payment.amount = amount
            payment.payment_method = payment_method
            payment.payment_date = payment_date
            payment.reference = reference
            payment.notes = notes

            # Update invoice link
            if invoice_id:
                try:
                    invoice = PurchaseInvoice.objects.get(id=invoice_id)
                    payment.invoice = invoice
                except PurchaseInvoice.DoesNotExist:
                    payment.invoice = None
            else:
                payment.invoice = None

            payment.save()  # This will update purchase payment status in the save method
            messages.success(request, _('تم تحديث الدفعة بنجاح'))
            return redirect('purchases:view_purchase', purchase_id=purchase.id)

        except Exception as e:
            messages.error(request, f'حدث خطأ: {str(e)}')

    context = {
        'payment': payment,
        'purchase': purchase,
        'invoices': invoices,
        'payment_methods': SupplierPayment.PAYMENT_METHOD_CHOICES,
    }
    return render(request, 'purchases/edit_payment.html', context)

@login_required
def delete_payment(request, payment_id):
    payment = get_object_or_404(SupplierPayment, id=payment_id)
    purchase_id = payment.purchase.id

    if request.method == 'POST':
        payment.delete()

        # Update purchase payment status
        purchase = payment.purchase
        total_paid = purchase.payments.aggregate(total=Sum('amount'))['total'] or 0

        if total_paid >= purchase.total_amount:
            purchase.payment_status = 'paid'
        elif total_paid > 0:
            purchase.payment_status = 'partial'
        else:
            purchase.payment_status = 'unpaid'

        purchase.save(update_fields=['payment_status'])

        messages.success(request, _('تم حذف الدفعة بنجاح'))
        return redirect('purchases:view_purchase', purchase_id=purchase_id)

    context = {
        'payment': payment,
    }
    return render(request, 'purchases/delete_payment.html', context)

# Reports
@login_required
def reports(request):
    context = {
        'report_types': [
            {'id': 'suppliers', 'name': _('تقرير الموردين')},
            {'id': 'purchases', 'name': _('تقرير المشتريات')},
            {'id': 'payments', 'name': _('تقرير المدفوعات')},
        ]
    }
    return render(request, 'purchases/reports.html', context)

@login_required
def supplier_report(request):
    suppliers = Supplier.objects.filter(is_active=True).order_by('name')

    # Annotate suppliers with purchase statistics
    suppliers = suppliers.annotate(
        purchase_count=Count('purchases'),
        purchase_total=Sum('purchases__total_amount'),
        pending_count=Count('purchases', filter=Q(purchases__status='pending')),
        received_count=Count('purchases', filter=Q(purchases__status='received')),
        paid_amount=Sum('purchases__total_amount', filter=Q(purchases__payment_status='paid')),
        unpaid_amount=Sum('purchases__total_amount', filter=Q(purchases__payment_status='unpaid')),
        partial_amount=Sum('purchases__total_amount', filter=Q(purchases__payment_status='partial'))
    )

    context = {
        'suppliers': suppliers,
        'report_type': 'suppliers',
    }
    return render(request, 'purchases/supplier_report.html', context)

@login_required
def purchases_report(request):
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    supplier_id = request.GET.get('supplier', '')
    status = request.GET.get('status', '')
    payment_status = request.GET.get('payment_status', '')

    purchases = Purchase.objects.all()

    # Apply filters
    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
            purchases = purchases.filter(date__gte=date_from_obj)
        except ValueError:
            pass

    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
            purchases = purchases.filter(date__lte=date_to_obj)
        except ValueError:
            pass

    if supplier_id:
        purchases = purchases.filter(supplier_id=supplier_id)

    if status:
        purchases = purchases.filter(status=status)

    if payment_status:
        purchases = purchases.filter(payment_status=payment_status)

    # Get summary statistics
    total_count = purchases.count()
    total_amount = purchases.aggregate(total=Sum('total_amount'))['total'] or 0
    paid_amount = purchases.filter(payment_status='paid').aggregate(total=Sum('total_amount'))['total'] or 0
    unpaid_amount = purchases.filter(payment_status='unpaid').aggregate(total=Sum('total_amount'))['total'] or 0
    partial_amount = purchases.filter(payment_status='partial').aggregate(total=Sum('total_amount'))['total'] or 0

    # Get all suppliers for filter dropdown
    suppliers = Supplier.objects.filter(is_active=True).order_by('name')

    context = {
        'purchases': purchases.order_by('-date'),
        'suppliers': suppliers,
        'date_from': date_from,
        'date_to': date_to,
        'supplier_id': supplier_id,
        'status': status,
        'payment_status': payment_status,
        'total_count': total_count,
        'total_amount': total_amount,
        'paid_amount': paid_amount,
        'unpaid_amount': unpaid_amount,
        'partial_amount': partial_amount,
        'status_choices': Purchase.STATUS_CHOICES,
        'payment_status_choices': Purchase.PAYMENT_STATUS_CHOICES,
        'report_type': 'purchases',
    }
    return render(request, 'purchases/purchases_report.html', context)

@login_required
def payments_report(request):
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    supplier_id = request.GET.get('supplier', '')
    payment_method = request.GET.get('payment_method', '')

    payments = SupplierPayment.objects.all()

    # Apply filters
    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
            payments = payments.filter(payment_date__gte=date_from_obj)
        except ValueError:
            pass

    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
            payments = payments.filter(payment_date__lte=date_to_obj)
        except ValueError:
            pass

    if supplier_id:
        payments = payments.filter(purchase__supplier_id=supplier_id)

    if payment_method:
        payments = payments.filter(payment_method=payment_method)

    # Get summary statistics
    total_count = payments.count()
    total_amount = payments.aggregate(total=Sum('amount'))['total'] or 0

    # Get payment method breakdown
    payment_methods_summary = payments.values('payment_method').annotate(
        count=Count('id'),
        total=Sum('amount')
    ).order_by('payment_method')

    # Get all suppliers for filter dropdown
    suppliers = Supplier.objects.filter(is_active=True).order_by('name')

    context = {
        'payments': payments.order_by('-payment_date'),
        'suppliers': suppliers,
        'date_from': date_from,
        'date_to': date_to,
        'supplier_id': supplier_id,
        'payment_method': payment_method,
        'total_count': total_count,
        'total_amount': total_amount,
        'payment_methods_summary': payment_methods_summary,
        'payment_method_choices': SupplierPayment.PAYMENT_METHOD_CHOICES,
        'report_type': 'payments',
    }
    return render(request, 'purchases/payments_report.html', context)

@login_required
def export_report(request, report_type):
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="{report_type}_report_{date.today().strftime("%Y%m%d")}.csv"'

    writer = csv.writer(response)

    if report_type == 'suppliers':
        # Export suppliers report
        writer.writerow([_('اسم المورد'), _('الفئة'), _('رقم الهاتف'), _('البريد الإلكتروني'), _('عدد المشتريات'), _('إجمالي المشتريات')])

        suppliers = Supplier.objects.filter(is_active=True).annotate(
            purchase_count=Count('purchases'),
            purchase_total=Sum('purchases__total_amount')
        ).order_by('name')

        for supplier in suppliers:
            writer.writerow([
                supplier.name,
                supplier.category.name if supplier.category else '',
                supplier.phone,
                supplier.email or '',
                supplier.purchase_count or 0,
                supplier.purchase_total or 0,
            ])

    elif report_type == 'purchases':
        # Export purchases report
        writer.writerow([_('رقم المرجع'), _('المورد'), _('تاريخ الشراء'), _('تاريخ التسليم'), _('المبلغ الإجمالي'), _('حالة الطلب'), _('حالة الدفع')])

        # Apply filters from request
        date_from = request.GET.get('date_from', '')
        date_to = request.GET.get('date_to', '')
        supplier_id = request.GET.get('supplier', '')
        status = request.GET.get('status', '')
        payment_status = request.GET.get('payment_status', '')

        purchases = Purchase.objects.all()

        # Apply filters
        if date_from:
            try:
                date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
                purchases = purchases.filter(date__gte=date_from_obj)
            except ValueError:
                pass

        if date_to:
            try:
                date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
                purchases = purchases.filter(date__lte=date_to_obj)
            except ValueError:
                pass

        if supplier_id:
            purchases = purchases.filter(supplier_id=supplier_id)

        if status:
            purchases = purchases.filter(status=status)

        if payment_status:
            purchases = purchases.filter(payment_status=payment_status)

        for purchase in purchases.order_by('-date'):
            writer.writerow([
                purchase.reference_number,
                purchase.supplier.name,
                purchase.date.strftime('%Y-%m-%d'),
                purchase.actual_delivery_date.strftime('%Y-%m-%d') if purchase.actual_delivery_date else '',
                purchase.total_amount,
                dict(Purchase.STATUS_CHOICES).get(purchase.status, ''),
                dict(Purchase.PAYMENT_STATUS_CHOICES).get(purchase.payment_status, ''),
            ])

    elif report_type == 'payments':
        # Export payments report
        writer.writerow([_('المورد'), _('رقم الطلب'), _('تاريخ الدفع'), _('المبلغ'), _('طريقة الدفع'), _('المرجع')])

        # Apply filters from request
        date_from = request.GET.get('date_from', '')
        date_to = request.GET.get('date_to', '')
        supplier_id = request.GET.get('supplier', '')
        payment_method = request.GET.get('payment_method', '')

        payments = SupplierPayment.objects.all()

        # Apply filters
        if date_from:
            try:
                date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
                payments = payments.filter(payment_date__gte=date_from_obj)
            except ValueError:
                pass

        if date_to:
            try:
                date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
                payments = payments.filter(payment_date__lte=date_to_obj)
            except ValueError:
                pass

        if supplier_id:
            payments = payments.filter(purchase__supplier_id=supplier_id)

        if payment_method:
            payments = payments.filter(payment_method=payment_method)

        for payment in payments.order_by('-payment_date'):
            writer.writerow([
                payment.purchase.supplier.name,
                payment.purchase.reference_number,
                payment.payment_date.strftime('%Y-%m-%d'),
                payment.amount,
                dict(SupplierPayment.PAYMENT_METHOD_CHOICES).get(payment.payment_method, ''),
                payment.reference or '',
            ])

    return response

# AJAX endpoints
@csrf_exempt
@login_required
def get_supplier_info(request, supplier_id):
    try:
        supplier = Supplier.objects.get(id=supplier_id)
        data = {
            'id': supplier.id,
            'name': supplier.name,
            'contact_person': supplier.contact_person,
            'phone': supplier.phone,
            'email': supplier.email,
            'address': supplier.address,
            'city': supplier.city,
            'country': supplier.country,
            'tax_number': supplier.tax_number,
            'website': supplier.website,
            'category': supplier.category.id if supplier.category else None,
            'category_name': supplier.category.name if supplier.category else '',
        }
        return JsonResponse(data)
    except Supplier.DoesNotExist:
        return JsonResponse({'error': 'Supplier not found'}, status=404)

@csrf_exempt
@login_required
def get_product_info(request, product_id):
    try:
        product = Product.objects.get(id=product_id)
        data = {
            'id': product.id,
            'code': product.code,
            'name': product.name,
            'category': product.category.name,
            'quantity': product.quantity,
            'purchase_price': float(product.purchase_price),
            'selling_price': float(product.selling_price),
            'image_url': product.image.url if product.image else None,
        }
        return JsonResponse(data)
    except Product.DoesNotExist:
        return JsonResponse({'error': 'Product not found'}, status=404)

@login_required
def get_top_products(request):
    # Get top 10 products by purchase quantity
    top_products = PurchaseItem.objects.values('product').annotate(
        total_quantity=Sum('quantity')
    ).order_by('-total_quantity')[:10]

    product_names = []
    quantities = []

    for item in top_products:
        try:
            product = Product.objects.get(id=item['product'])
            product_names.append(product.name)
            quantities.append(item['total_quantity'])
        except Product.DoesNotExist:
            continue

    data = {
        'product_names': product_names,
        'quantities': quantities
    }

    return JsonResponse(data)
